stages:
  - deploy
  - merge

variables:
  GIT_STRATEGY: none

# Deploy testing on stg server
deploy tesing:
  stage: deploy
  tags:
    - 102-stg
  only:
    - develop
  script:
    - cd /var/www/ams
    - git pull origin develop
    - /usr/bin/php8.2 /usr/bin/composer install
    - /usr/bin/php8.2 artisan horizon:terminate
    - /usr/bin/php8.2 /opt/cachetool/cachetool.phar opcache:reset --fcgi=/run/php/php8.2-fpm.sock
  environment:
    name: testing
    url: https://amsdev.topdev.asia

# Deploy production on inhouse server
deploy production:
  stage: deploy
  tags:
    - 20-inhouse
  only:
    - main
  script:
    - cd /var/www/html/ams
    - git pull origin main
    - /usr/bin/php8.2 artisan queue:restart
    - /usr/bin/php8.2 /opt/cachetool/cachetool.phar opcache:reset --fcgi=/run/php/php-ams-10x.sock
  environment:
    name: production
    url: https://ams.topdev.asia

# Merge main into develop
merge main to develop:
  stage: merge
  when: on_success
  tags:
    - 102-stg
  only:
    - main
  script:
    - rm -rf /var/www/ams-sync
    - git clone ssh://*******************:8229/applancer/ams.git /var/www/ams-sync
    - cd /var/www/ams-sync
    - git checkout develop
    - |
      git merge --no-ff main -m "chore: auto-merge main into develop after deployment" || { echo "Merge conflict detected!"; exit 1; }
    - git push origin develop
