{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@chenfengyuan/vue-countdown": "^1.1.2", "@fullhuman/postcss-purgecss": "^1.2.0", "@vue/cli-plugin-babel": "^3.7.0", "@vue/cli-plugin-eslint": "^3.7.0", "@vue/cli-service": "^4.5.6", "@vue/composition-api": "^1.2.3", "ag-grid-community": "^21.0.1", "ag-grid-vue": "^21.0.1", "algoliasearch": "^3.33.0", "apexcharts": "^3.21.0", "auth0-js": "^9.14.0", "axios": "^0.24.0", "axios-mock-adapter": "^1.18.2", "chart.js": "^2.8.0", "core-js": "2.6.5", "cross-env": "^5.2.1", "echarts": "^4.9.0", "file-saver": "2.0.1", "firebase": "^6.0.4", "instantsearch.css": "^7.4.5", "jsonwebtoken": "^8.5.1", "laravel-mix": "^4.0.7", "lodash": "^4.17.20", "material-icons": "^0.3.1", "node-sass": "^4.14.1", "perfect-scrollbar": "^1.4.0", "postcss-rtl": "1.5.0", "prismjs": "^1.21.0", "purgecss": "^1.3.0", "resolve-url-loader": "^2.3.1", "sass": "^1.26.11", "sass-loader": "^7.1.0", "script-loader": "0.7.2", "tailwindcss": "^1.8.10", "vee-validate": "^2.2.8", "vue": "^2.6.12", "vue-acl": "4.0.7", "vue-apexcharts": "^1.6.0", "vue-awesome-swiper": "^3.1.3", "vue-backtotop": "^1.6.1", "vue-clipboard2": "^0.3.0", "vue-context": "^4.0.0", "vue-echarts": "^4.0.3", "vue-feather-icons": "^5.1.0", "vue-flatpickr-component": "^8.1.6", "vue-form-wizard": "^0.8.4", "vue-fullcalendar": "^1.0.9", "vue-i18n": "^8.21.1", "vue-instantsearch": "^2.7.1", "vue-perfect-scrollbar": "^0.1.0", "vue-prism-component": "^1.2.0", "vue-property-decorator": "^8.5.1", "vue-quill-editor": "^3.0.6", "vue-router": "^3.4.5", "vue-select": "^3.10.8", "vue-simple-calendar": "^4.4.0", "vue-simple-suggest": "^1.10.2", "vue-star-rating": "^1.6.3", "vue-template-compiler": "^2.6.12", "vue-tour": "^1.5.0", "vue-tree-halower": "^1.8.0", "vue-video-player": "^5.0.2", "vue2-google-maps": "^0.10.6", "vue2-hammer": "^2.1.2", "vuecode.js": "0.0.27", "vuedraggable": "^2.24.1", "vuejs-datepicker": "^1.5.4", "vuepress": "^1.6.0", "vuesax": "3.11.13", "vuex": "^3.5.1", "xlsx": "^0.17.4"}, "dependencies": {"@fullcalendar/common": "^5.10.0", "@fullcalendar/core": "^5.10.0", "@fullcalendar/daygrid": "^5.10.0", "@fullcalendar/interaction": "^5.10.0", "@fullcalendar/list": "^5.10.0", "@fullcalendar/timegrid": "^5.10.0", "@fullcalendar/vue": "^5.10.0", "@mycure/vue-jitsi-meet": "0.0.7", "bootstrap-vue": "^2.21.2", "moment": "^2.28.0", "qs": "^6.10.1", "vue-cookies": "^1.7.4", "vue-imask": "^6.2.2", "vue-multiselect": "^2.1.6", "vue-native-color-picker": "^1.2.0", "vue2-daterange-picker": "^0.6.6", "vue2-dropzone": "^3.6.0"}}