parameters:
	ignoreErrors:

		-  "#^Function factory invoked with [0-9] parameters, 0 required\\.$#"
		-  "#^Function factory invoked with [0-9] parameter, 0 required\\.$#"

		-
			message: "#^Parameter \\#1 \\$time of method Modules\\\\Activity\\\\Services\\\\ProgressManager\\:\\:getJobsClickApply\\(\\) expects null, Illuminate\\\\Support\\\\Carbon given\\.$#"
			count: 2
			path: Modules/Activity/Campaigns/ClickApply24h.php

		-
			message: "#^Parameter \\#1 \\$message of static method Illuminate\\\\Log\\\\Logger\\:\\:info\\(\\) expects string, bool given\\.$#"
			count: 1
			path: Modules/Activity/Campaigns/PushCompleteCV.php

		-
			message: "#^Parameter \\#1 \\$time of method Modules\\\\Activity\\\\Services\\\\ProgressManager\\:\\:getFollowedJobs\\(\\) expects null, Illuminate\\\\Support\\\\Carbon given\\.$#"
			count: 2
			path: Modules/Activity/Campaigns/SavedJob24h.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Support\\\\Fluent\\:\\:nullable\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Database/Migrations/2020_08_17_171627_custom_activity_table.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:setErrors\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/EmailMarketing/AutoMauticEmailMarketing.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:toArray\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/EmailMarketing/AutoMauticEmailMarketing.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:setErrors\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/EmailMarketing/JobMauticEmailMarketing.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:toArray\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/EmailMarketing/JobMauticEmailMarketing.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: Modules/Activity/EmailMarketing/JobMauticEmailMarketing.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 3
			path: Modules/Activity/EmailMarketing/JobMauticEmailMarketing.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$area_id\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$collection\\.$#"
			count: 5
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$companies\\.$#"
			count: 2
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$job_status\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$jobs\\.$#"
			count: 2
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$skills\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$user_id\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$viewed_at\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$visitor\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:area\\(\\) has invalid return type Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:area\\(\\) should return Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:companies\\(\\) has invalid return type Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:companies\\(\\) should return Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\<Modules\\\\Company\\\\Entities\\\\Company\\>\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:jobs\\(\\) has invalid return type Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:jobs\\(\\) should return Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\<Modules\\\\Job\\\\Entities\\\\Job\\>\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:skills\\(\\) has invalid return type Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:skills\\(\\) should return Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\<Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\>\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:trackingUser\\(\\) has invalid return type Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:trackingUser\\(\\) should return Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:user\\(\\) has invalid return type Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:user\\(\\) should return Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$view$#"
			count: 1
			path: Modules/Activity/Events/ActivityCreated.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\ActivityCreated\\:\\:\\$device_token has unknown class Modules\\\\Activity\\\\Events\\\\FcmToken as its type\\.$#"
			count: 1
			path: Modules/Activity/Events/ActivityCreated.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\EventRepuestProcessed\\:\\:user\\(\\) has invalid return type Modules\\\\Activity\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/Activity/Events/EventRepuestProcessed.php

		-
			message: "#^PHPDoc tag @param for parameter \\$user with type Modules\\\\Activity\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User is not subtype of native type Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/Activity/Events/EventRepuestProcessed.php

		-
			message: "#^Parameter \\$user of method Modules\\\\Activity\\\\Events\\\\EventRepuestProcessed\\:\\:__construct\\(\\) has invalid type Modules\\\\Activity\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/Activity/Events/EventRepuestProcessed.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\EventRepuestProcessed\\:\\:\\$user \\(Modules\\\\Activity\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\) does not accept Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/Activity/Events/EventRepuestProcessed.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\EventRepuestProcessed\\:\\:\\$user has unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User as its type\\.$#"
			count: 1
			path: Modules/Activity/Events/EventRepuestProcessed.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:toArray\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\PublishCampaignCompleted\\:\\:campaign\\(\\) has invalid return type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\PublishCampaignCompleted\\:\\:campaign\\(\\) should return Modules\\\\Activity\\\\Contracts\\\\CampaignContract but returns Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^PHPDoc tag @param for parameter \\$campaign with type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^PHPDoc tag @return with type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Parameter \\#1 \\$message of static method Illuminate\\\\Log\\\\Logger\\:\\:info\\(\\) expects string, array\\<mixed, mixed\\> given\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Parameter \\$campaign of method Modules\\\\Activity\\\\Events\\\\PublishCampaignCompleted\\:\\:__construct\\(\\) has invalid type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\PublishCampaignCompleted\\:\\:\\$campaign \\(Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\) does not accept Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\PublishCampaignCompleted\\:\\:\\$campaign has unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract as its type\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:getErrors\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\PublishCampaignIncompleted\\:\\:campaign\\(\\) has invalid return type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\PublishCampaignIncompleted\\:\\:campaign\\(\\) should return Modules\\\\Activity\\\\Contracts\\\\CampaignContract but returns Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^PHPDoc tag @param for parameter \\$campaign with type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^PHPDoc tag @return with type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^Parameter \\$campaign of method Modules\\\\Activity\\\\Events\\\\PublishCampaignIncompleted\\:\\:__construct\\(\\) has invalid type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\PublishCampaignIncompleted\\:\\:\\$campaign \\(Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\) does not accept Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\PublishCampaignIncompleted\\:\\:\\$campaign has unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract as its type\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:toArray\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\PublishCampaignStarting\\:\\:campaign\\(\\) has invalid return type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\PublishCampaignStarting\\:\\:campaign\\(\\) should return Modules\\\\Activity\\\\Contracts\\\\CampaignContract but returns Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^PHPDoc tag @param for parameter \\$campaign with type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^PHPDoc tag @return with type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Parameter \\#1 \\$message of static method Illuminate\\\\Log\\\\Logger\\:\\:info\\(\\) expects string, array\\<mixed, mixed\\> given\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Parameter \\$campaign of method Modules\\\\Activity\\\\Events\\\\PublishCampaignStarting\\:\\:__construct\\(\\) has invalid type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\PublishCampaignStarting\\:\\:\\$campaign \\(Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\) does not accept Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\PublishCampaignStarting\\:\\:\\$campaign has unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract as its type\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\SaveViewProcessed\\:\\:activity\\(\\) has invalid return type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity\\.$#"
			count: 1
			path: Modules/Activity/Events/SaveViewProcessed.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$view$#"
			count: 1
			path: Modules/Activity/Events/SaveViewProcessed.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\SaveViewProcessed\\:\\:\\$activity \\(Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity\\) does not accept Modules\\\\Activity\\\\Entities\\\\Activity\\.$#"
			count: 1
			path: Modules/Activity/Events/SaveViewProcessed.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\SaveViewProcessed\\:\\:\\$activity has unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity as its type\\.$#"
			count: 1
			path: Modules/Activity/Events/SaveViewProcessed.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Jobs\\\\ActivityLoggerProcess\\:\\:cacheDataActivity\\(\\) is unused\\.$#"
			count: 1
			path: Modules/Activity/Jobs/ActivityLoggerProcess.php

		-
			message: "#^PHPDoc tag @throws with type Psr\\\\SimpleCache\\\\InvalidArgumentException is not subtype of Throwable$#"
			count: 1
			path: Modules/Activity/Jobs/ActivityLoggerProcess.php

		-
			message: "#^Parameter \\#3 \\$user of class Modules\\\\Activity\\\\Services\\\\Visitor constructor expects string\\|null, int given\\.$#"
			count: 1
			path: Modules/Activity/Jobs/ActivityLoggerProcess.php

		-
			message: "#^Variable \\$value in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Activity/Jobs/ActivityLoggerProcess.php

		-
			message: "#^Relation 'activity\\-logger' is not found in Illuminate\\\\Contracts\\\\Cache\\\\Repository model\\.$#"
			count: 1
			path: Modules/Activity/Jobs/ActivityLoggerSchedule.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:publishCampaignUsing\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Jobs/MakePublishCampaign.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Viewable\\:\\:\\$view_id\\.$#"
			count: 2
			path: Modules/Activity/Jobs/RemoveViewsOnDelete.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:publishCampaign\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Jobs/WebhookUpdateDataMautic.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$user\\.$#"
			count: 1
			path: Modules/Activity/Listeners/FacebookPixel.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Activity\\\\Events\\\\ActivityCreated\\.$#"
			count: 1
			path: Modules/Activity/Listeners/FacebookPixel.php

		-
			message: "#^Parameter \\#1 \\$app_id of static method FacebookAds\\\\Api\\:\\:init\\(\\) expects string, null given\\.$#"
			count: 1
			path: Modules/Activity/Listeners/FacebookPixel.php

		-
			message: "#^Parameter \\#1 \\$events of method FacebookAds\\\\Object\\\\ServerSide\\\\EventRequest\\:\\:setEvents\\(\\) expects array\\<FacebookAds\\\\Object\\\\ServerSide\\\\FacebookAds\\\\Object\\\\ServerSide\\\\Event\\>, array\\<int, FacebookAds\\\\Object\\\\ServerSide\\\\Event\\> given\\.$#"
			count: 1
			path: Modules/Activity/Listeners/FacebookPixel.php

		-
			message: "#^Parameter \\#1 \\$user_data of method FacebookAds\\\\Object\\\\ServerSide\\\\Event\\:\\:setUserData\\(\\) expects FacebookAds\\\\Object\\\\ServerSide\\\\FacebookAds\\\\Object\\\\ServerSide\\\\UserData, FacebookAds\\\\Object\\\\ServerSide\\\\UserData given\\.$#"
			count: 1
			path: Modules/Activity/Listeners/FacebookPixel.php

		-
			message: "#^Parameter \\#2 \\$app_secret of static method FacebookAds\\\\Api\\:\\:init\\(\\) expects string, null given\\.$#"
			count: 1
			path: Modules/Activity/Listeners/FacebookPixel.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:getProgress\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Listeners/SyncProgressCvBuilderToElasticsearch.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Activity\\\\Events\\\\PublishCampaignStarting\\.$#"
			count: 1
			path: Modules/Activity/Listeners/SyncProgressCvBuilderToElasticsearch.php

		-
			message: "#^Call to method only\\(\\) on an unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/Activity/Listeners/UnsearchableCvBuilderCompleted.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Activity\\\\Events\\\\EventRepuestProcessed\\.$#"
			count: 1
			path: Modules/Activity/Listeners/UnsearchableCvBuilderCompleted.php

		-
			message: "#^Parameter \\#1 \\$message of static method Illuminate\\\\Log\\\\Logger\\:\\:info\\(\\) expects string, array given\\.$#"
			count: 1
			path: Modules/Activity/Listeners/UnsearchableCvBuilderCompleted.php

		-
			message: "#^Access to property \\$user on an unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity\\.$#"
			count: 1
			path: Modules/Activity/Listeners/VistorOnViewable.php

		-
			message: "#^Access to property \\$viewable on an unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity\\.$#"
			count: 1
			path: Modules/Activity/Listeners/VistorOnViewable.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Activity\\\\Events\\\\SaveViewProcessed\\.$#"
			count: 1
			path: Modules/Activity/Listeners/VistorOnViewable.php

		-
			message: "#^Property Modules\\\\Activity\\\\Ranks\\\\SkillRank\\:\\:\\$timeelasped is never read, only written\\.$#"
			count: 1
			path: Modules/Activity/Ranks/SkillRank.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Contracts\\\\ActivityContract\\:\\:\\$exists\\.$#"
			count: 1
			path: Modules/Activity/Services/ActivityManager.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\ActivityContract\\:\\:fill\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/ActivityManager.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\ActivityContract\\:\\:prepareToAttachViewables\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/ActivityManager.php

		-
			message: "#^Call to an undefined static method Modules\\\\Activity\\\\Contracts\\\\ActivityContract\\:\\:created\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/ActivityManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\CampaignManager\\:\\:createSavedJob24hDriver\\(\\) has invalid return type Modules\\\\Activity\\\\Campaigns\\\\SavedJob\\.$#"
			count: 1
			path: Modules/Activity/Services/CampaignManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\CampaignManager\\:\\:createSavedJob24hDriver\\(\\) should return Modules\\\\Activity\\\\Campaigns\\\\SavedJob but returns Modules\\\\Activity\\\\Campaigns\\\\SavedJob24h\\.$#"
			count: 1
			path: Modules/Activity/Services/CampaignManager.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/Activity/Services/CampaignManager.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$detector$#"
			count: 1
			path: Modules/Activity/Services/CrawlerDetector.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Services\\\\Mautic\\\\AccessToken\\:\\:isExpirationTimestamp\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/AccessToken.php

		-
			message: "#^Instantiated class Modules\\\\Activity\\\\Services\\\\Mautic\\\\InvalidArgumentException not found\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/AccessToken.php

		-
			message: "#^Instantiated class Modules\\\\Activity\\\\Services\\\\Mautic\\\\RuntimeException not found\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/AccessToken.php

		-
			message: "#^Throwing object of an unknown class Modules\\\\Activity\\\\Services\\\\Mautic\\\\InvalidArgumentException\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/AccessToken.php

		-
			message: "#^Throwing object of an unknown class Modules\\\\Activity\\\\Services\\\\Mautic\\\\RuntimeException\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/AccessToken.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/Mautic.php

		-
			message: "#^Call to an undefined method Mautic\\\\Auth\\\\AuthInterface\\:\\:accessTokenUpdated\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Call to an undefined method Mautic\\\\Auth\\\\AuthInterface\\:\\:getAccessTokenData\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Call to an undefined method Mautic\\\\Auth\\\\AuthInterface\\:\\:validateAccessToken\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Mautic\\\\MauticFactory\\:\\:getClient\\(\\) has invalid return type Mautic\\\\MauticConsumer\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Mautic\\\\MauticFactory\\:\\:getClient\\(\\) should return Mautic\\\\MauticConsumer but return statement is missing\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Mautic\\\\MauticFactory\\:\\:getMauticUrl\\(\\) has invalid return type Modules\\\\Activity\\\\Services\\\\Mautic\\\\url\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Mautic\\\\MauticFactory\\:\\:getMauticUrl\\(\\) should return Modules\\\\Activity\\\\Services\\\\Mautic\\\\url but returns string\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Mautic\\\\MauticFactory\\:\\:make\\(\\) has invalid return type Mautic\\\\Config\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Mautic\\\\MauticFactory\\:\\:make\\(\\) should return Mautic\\\\Config but returns Mautic\\\\MauticConsumer\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^PHPDoc tag @throws with type ClientException is not subtype of Throwable$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Parameter \\#2 \\$uri of method GuzzleHttp\\\\Client\\:\\:request\\(\\) expects Psr\\\\Http\\\\Message\\\\UriInterface\\|string, Modules\\\\Activity\\\\Services\\\\Mautic\\\\url given\\.$#"
			count: 2
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Call to method clickApply\\(\\) on an unknown class Modules\\\\Activity\\\\Services\\\\Model\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Call to method followedJobs\\(\\) on an unknown class Modules\\\\Activity\\\\Services\\\\Model\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Call to method map\\(\\) on an unknown class App\\\\Collection\\\\Collection\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\ProgressManager\\:\\:getNewestJob\\(\\) is unused\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\ProgressManager\\:\\:getUserInstance\\(\\) has invalid return type Modules\\\\Activity\\\\Services\\\\Model\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\ProgressManager\\:\\:limitSuggestJob\\(\\) is unused\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^PHPDoc tag @return with type mixed is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Ternary operator condition is always true\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\UserCvBuilder\\:\\:newCollection\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Collection but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), mixed\\>\\.$#"
			count: 1
			path: Modules/Activity/Services/UserCvBuilder.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$visitor$#"
			count: 1
			path: Modules/Activity/Services/UserCvBuilder.php

		-
			message: "#^Parameter \\#1 \\$year of static method Carbon\\\\Carbon\\:\\:create\\(\\) expects DateTimeInterface\\|int\\|null, string given\\.$#"
			count: 1
			path: Modules/Activity/Services/UserCvBuilder.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/UserCvBuilder.php

		-
			message: "#^Call to method map\\(\\) on an unknown class App\\\\Collection\\\\Collection\\.$#"
			count: 1
			path: Modules/Activity/Services/UserFeed.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/UserFeed.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Visitor\\:\\:crawlerDetector\\(\\) has invalid return type Modules\\\\Activity\\\\Services\\\\Modules\\\\Activity\\\\Services\\\\CrawlerDetector\\.$#"
			count: 1
			path: Modules/Activity/Services/Visitor.php

		-
			message: "#^PHPDoc tag @return with type Modules\\\\Activity\\\\Services\\\\Modules\\\\Activity\\\\Services\\\\CrawlerDetector is not subtype of native type Modules\\\\Activity\\\\Services\\\\CrawlerDetector\\.$#"
			count: 1
			path: Modules/Activity/Services/Visitor.php

		-
			message: "#^PHPDoc tag @return with type string\\|null is not subtype of native type string\\.$#"
			count: 1
			path: Modules/Activity/Services/Visitor.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/VisitorFeed.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Entities\\\\Announcement\\:\\:\\$body\\.$#"
			count: 1
			path: Modules/Announcement/Entities/Announcement.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Entities\\\\Announcement\\:\\:\\$deleted_at\\.$#"
			count: 1
			path: Modules/Announcement/Entities/Announcement.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Entities\\\\Announcement\\:\\:\\$model_id\\.$#"
			count: 1
			path: Modules/Announcement/Entities/Announcement.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Entities\\\\Announcement\\:\\:\\$model_type\\.$#"
			count: 1
			path: Modules/Announcement/Entities/Announcement.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Entities\\\\Announcement\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Announcement/Entities/Announcement.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Entities\\\\Announcement\\:\\:\\$type\\.$#"
			count: 1
			path: Modules/Announcement/Entities/Announcement.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/Announcement/Repositories/Eloquents/AnnouncementRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/Announcement/Repositories/Eloquents/AnnouncementRepository.php

		-
			message: "#^Call to method aggregations\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementCollection.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementCollection.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementCollection.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementCollection.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\Announcement\\\\Transformers\\\\StatusJobAnnouncementCollection\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementCollection.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Transformers\\\\StatusJobAnnouncementCollection\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementCollection.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementCollection.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/Announcement/Transformers/StatusJobAnnouncementCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementCollection.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\Announcement\\\\Transformers\\\\StatusJobAnnouncementResource\\)\\:\\:\\$body\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\Announcement\\\\Transformers\\\\StatusJobAnnouncementResource\\)\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Transformers\\\\StatusJobAnnouncementResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\Announcement\\\\Transformers\\\\StatusJobAnnouncementResource\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Transformers\\\\StatusJobAnnouncementResource\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Banner\\\\Entities\\\\Banner\\:\\:\\$type\\.$#"
			count: 2
			path: Modules/Banner/Entities/Banner.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Banner/Http/Controllers/BannerController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:disableAuditing\\(\\)\\.$#"
			count: 1
			path: Modules/Banner/Http/Controllers/BannerController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:disableCache\\(\\)\\.$#"
			count: 1
			path: Modules/Banner/Http/Controllers/BannerController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getOldValuesAudit\\(\\)\\.$#"
			count: 1
			path: Modules/Banner/Http/Controllers/BannerController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:setOldValuesAudit\\(\\)\\.$#"
			count: 1
			path: Modules/Banner/Http/Controllers/BannerController.php

		-
			message: "#^Property Modules\\\\Banner\\\\Http\\\\Controllers\\\\BannerController\\:\\:\\$request is never read, only written\\.$#"
			count: 1
			path: Modules/Banner/Http/Controllers/BannerController.php

		-
			message: "#^Class BannerController not found\\.$#"
			count: 1
			path: Modules/Banner/Routes/web.php

		-
			message: "#^Access to an undefined property Modules\\\\Banner\\\\Transformers\\\\AuditTransformer\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/Banner/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Banner\\\\Transformers\\\\AuditTransformer\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Banner/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Banner\\\\Transformers\\\\AuditTransformer\\:\\:\\$device\\.$#"
			count: 1
			path: Modules/Banner/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Banner\\\\Transformers\\\\AuditTransformer\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Banner/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Banner\\\\Transformers\\\\AuditTransformer\\:\\:\\$image\\.$#"
			count: 1
			path: Modules/Banner/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Banner\\\\Transformers\\\\AuditTransformer\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Banner/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Banner\\\\Transformers\\\\AuditTransformer\\:\\:\\$type\\.$#"
			count: 1
			path: Modules/Banner/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Banner\\\\Transformers\\\\AuditTransformer\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/Banner/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Banner\\\\Transformers\\\\AuditTransformer\\:\\:\\$url\\.$#"
			count: 1
			path: Modules/Banner/Transformers/AuditTransformer.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/Banner/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$personCharge\\.$#"
			count: 1
			path: Modules/Company/Actions/PersonInChargeCompany.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:personCharge\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Actions/PersonInChargeCompany.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:withCacheCooldownSeconds\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Actions/PersonInChargeCompany.php

		-
			message: "#^Method Amscore\\\\Admin\\\\Actions\\\\Action\\:\\:__construct\\(\\) invoked with 1 parameter, 0 required\\.$#"
			count: 1
			path: Modules/Company/Actions/SearchEmployer.php

		-
			message: "#^Method Amscore\\\\Admin\\\\Actions\\\\Action\\:\\:__construct\\(\\) invoked with 1 parameter, 0 required\\.$#"
			count: 1
			path: Modules/Company/Actions/ShowDetailFrontend.php

		-
			message: "#^Method Amscore\\\\Admin\\\\Actions\\\\Action\\:\\:__construct\\(\\) invoked with 1 parameter, 0 required\\.$#"
			count: 1
			path: Modules/Company/Actions/ShowGroupFrontend.php

		-
			message: "#^Parameter \\#1 \\$companies of method Modules\\\\Company\\\\Console\\\\ExportCompanyForCs\\:\\:exportSheetCompaniesForCs\\(\\) expects Illuminate\\\\Database\\\\Eloquent\\\\Collection, Illuminate\\\\Support\\\\Collection\\<int, mixed\\> given\\.$#"
			count: 1
			path: Modules/Company/Console/ExportCompanyForCs.php

		-
			message: "#^Parameter \\#1 \\$companies of method Modules\\\\Company\\\\Console\\\\ExportCompanyForCs\\:\\:exportSheetCompaniesInfoForCs\\(\\) expects Illuminate\\\\Database\\\\Eloquent\\\\Collection, Illuminate\\\\Support\\\\Collection\\<int, mixed\\> given\\.$#"
			count: 1
			path: Modules/Company/Console/ExportCompanyForCs.php

		-
			message: "#^Result of method Illuminate\\\\Console\\\\Command\\:\\:info\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Company/Console/ExportCompanyForCs.php

		-
			message: "#^Property Modules\\\\Company\\\\Console\\\\ExportGetReportSheets\\:\\:\\$spearedSheetId has unknown class Modules\\\\Company\\\\Console\\\\Google_Service_Sheets as its type\\.$#"
			count: 1
			path: Modules/Company/Console/ExportGetReportSheets.php

		-
			message: "#^Result of method Illuminate\\\\Console\\\\Command\\:\\:info\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Company/Console/GoogleSheetExportCompany.php

		-
			message: "#^Parameter \\#1 \\$value of method Illuminate\\\\Database\\\\Query\\\\Builder\\:\\:limit\\(\\) expects int, array\\|string given\\.$#"
			count: 1
			path: Modules/Company/Console/ParseCompanyProcess.php

		-
			message: "#^Comparison operation \"\\>\\=\" between int and array\\|string\\|null results in an error\\.$#"
			count: 1
			path: Modules/Company/Console/SyncImageCompany.php

		-
			message: "#^Property Modules\\\\Company\\\\Console\\\\SyncImageCompany\\:\\:\\$companyRepository is never read, only written\\.$#"
			count: 1
			path: Modules/Company/Console/SyncImageCompany.php

		-
			message: "#^Property Modules\\\\Company\\\\Console\\\\SyncImageCompany\\:\\:\\$jobRepository is never read, only written\\.$#"
			count: 1
			path: Modules/Company/Console/SyncImageCompany.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Schema\\\\ColumnDefinition\\:\\:nullalbe\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Database/Migrations/2020_01_09_091532_update_company_table.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Blog\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Blog\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^Undefined variable\\: \\$file_name$#"
			count: 2
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\BottomIntroduceGroup\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Company/Entities/BottomIntroduceGroup.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\BottomIntroduceGroup\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Company/Entities/BottomIntroduceGroup.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Company/Entities/BottomIntroduceGroup.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/Company/Entities/BottomIntroduceGroup.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Company/Entities/BottomIntroduceGroup.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$refreshed_at\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$alias\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$benefits\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$blogs\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$categories_id\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$categories_name\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$cover_group_url\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$description\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$display_name\\.$#"
			count: 5
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$extra_skills_name\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$faqs\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$features\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$highlight\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$image_cover\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$image_cover_url\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$image_galleries\\.$#"
			count: 5
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$image_galleries_url\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$image_logo\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$image_logo_url\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$industries_id\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$industries_name\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$introduces\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$logo_group_url\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$nationalities\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$num_employees\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$num_followers_fake\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$num_viewers_fake\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$phone\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$products\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$recruitment_process\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$skills_id\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$skills_name\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$social_network\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$spotlight\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$status\\.$#"
			count: 4
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$tagline\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$uuid\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$website\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Package\\:\\:\\$jobs\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$district_id\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$latitude\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$longitude\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$postal_code\\.$#"
			count: 3
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$province_id\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$ward_id\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to undefined constant static\\(Modules\\\\Company\\\\Entities\\\\Company\\)\\:\\:PARENT_COLUMN\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:addresses\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:generateSlugOnCreate\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:generateSlugOnUpdate\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:sluggable\\(\\)\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\:\\:disableCache\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\:\\:view\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to an undefined method Modules\\\\Company\\\\Entities\\\\Company\\:\\:blog\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to method active\\(\\) on an unknown class Modules\\\\Subscription\\\\Traits\\\\Modules\\\\Subscription\\\\Entities\\\\Package\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Called 'pluck' on Laravel collection, but could have been retrieved as a query\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Cannot call method isForceDeleting\\(\\) on class\\-string\\|object\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\hasMany\\.$#"
			count: 3
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\hasOne\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\morphToMany\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Amscore\\\\Metable\\\\Meta\\> does not accept Amscore\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:addOneTerm\\(\\) with return type void returns Illuminate\\\\Database\\\\Eloquent\\\\Model but should not return anything\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:addTaxonomies\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Collection but returns \\$this\\(Modules\\\\Company\\\\Entities\\\\Company\\)\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:cleanAmscore\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:cleanAmscore\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:cleanAmscore\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:feature\\(\\) has invalid return type Modules\\\\Subscription\\\\Traits\\\\Modules\\\\Subscription\\\\Entities\\\\Package\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getCompanyUrlAttribute\\(\\) has invalid return type Modules\\\\Company\\\\Entities\\\\url\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getCompanyUrlAttribute\\(\\) should return Modules\\\\Company\\\\Entities\\\\url but returns string\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getEmployOverviewAttribute\\(\\) should return array but returns string\\|false\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getEmployerAggregateRatingAttribute\\(\\) should return array but returns string\\|false\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getFollowUrlAttribute\\(\\) has invalid return type Modules\\\\Company\\\\Entities\\\\url\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getFollowUrlAttribute\\(\\) should return Modules\\\\Company\\\\Entities\\\\url but returns Illuminate\\\\Contracts\\\\Routing\\\\UrlGenerator\\|string\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Amscore\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getSchemaLocalBusinessAttribute\\(\\) should return array but returns string\\|false\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getTermsAttribute\\(\\) should return array but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), mixed\\>\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getUpdateUrlAttribute\\(\\) has invalid return type Modules\\\\Company\\\\Entities\\\\url\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getUpdateUrlAttribute\\(\\) should return Modules\\\\Company\\\\Entities\\\\url but returns string\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:setPackagesAttribute\\(\\) should return Modules\\\\Company\\\\Entities\\\\Company but return statement is missing\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:setServicesAttribute\\(\\) should return Modules\\\\Company\\\\Entities\\\\Company but return statement is missing\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:subscription\\(\\) has invalid return type Modules\\\\Subscription\\\\Traits\\\\Modules\\\\Subscription\\\\Entities\\\\Package\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:subscription\\(\\) should return Modules\\\\Subscription\\\\Traits\\\\Modules\\\\Subscription\\\\Entities\\\\Package\\|null but returns Modules\\\\Subscription\\\\Entities\\\\Package\\|null\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$params$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$query$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$terms$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Parameter \\$expires_at of method Modules\\\\Company\\\\Entities\\\\Company\\:\\:addOneTerm\\(\\) has invalid type Modules\\\\Taxonomy\\\\Traits\\\\Carbon\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$arrayValues \\(array\\) on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Result of method Modules\\\\Company\\\\Entities\\\\Company\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Variable \\$taxonomies in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\HeadIntroduceGroup\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Company/Entities/HeadIntroduceGroup.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\HeadIntroduceGroup\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Company/Entities/HeadIntroduceGroup.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Company/Entities/HeadIntroduceGroup.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Introduce\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Introduce\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^Undefined variable\\: \\$file_name$#"
			count: 2
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/Company/Entities/Product.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Company/Entities/Product.php

		-
			message: "#^Undefined variable\\: \\$file_name$#"
			count: 2
			path: Modules/Company/Entities/Product.php

		-
			message: "#^Method Modules\\\\Company\\\\Events\\\\CreateCompanyProcessed\\:\\:company\\(\\) has invalid return type Modules\\\\Company\\\\Events\\\\Modules\\\\Company\\\\Entities\\\\Company\\.$#"
			count: 1
			path: Modules/Company/Events/CreateCompanyProcessed.php

		-
			message: "#^Method Modules\\\\Company\\\\Events\\\\UpdateCompanyProcessed\\:\\:company\\(\\) has invalid return type Modules\\\\Company\\\\Events\\\\Modules\\\\Company\\\\Entities\\\\Company\\.$#"
			count: 1
			path: Modules/Company/Events/UpdateCompanyProcessed.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$id$#"
			count: 1
			path: Modules/Company/Http/Controllers/API/ApiCompanyController.php

		-
			message: "#^Undefined variable\\: \\$id$#"
			count: 1
			path: Modules/Company/Http/Controllers/API/ApiCompanyController.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$childrens_count\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Http\\\\Controllers\\\\CompanyController\\:\\:\\$audits\\.$#"
			count: 5
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Http\\\\Controllers\\\\CompanyController\\:\\:\\$detail_url\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Http\\\\Controllers\\\\CompanyController\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Http\\\\Controllers\\\\CompanyController\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Http\\\\Controllers\\\\CompanyController\\:\\:\\$employees\\.$#"
			count: 2
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Http\\\\Controllers\\\\CompanyController\\:\\:\\$full_addresses\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Http\\\\Controllers\\\\CompanyController\\:\\:\\$input\\.$#"
			count: 2
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Http\\\\Controllers\\\\CompanyController\\:\\:\\$is_highlight\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Http\\\\Controllers\\\\CompanyController\\:\\:\\$is_spotlight\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Http\\\\Controllers\\\\CompanyController\\:\\:\\$personCharge\\.$#"
			count: 2
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Http\\\\Controllers\\\\CompanyController\\:\\:\\$phone\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Http\\\\Controllers\\\\CompanyController\\:\\:\\$status_display\\.$#"
			count: 2
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Displayers\\\\Actions\\:\\:add\\(\\)\\.$#"
			count: 5
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:withCount\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:disableAuditing\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:disableAutoloadTranslations\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:disableCache\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getOldValuesAudit\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:setOldValuesAudit\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Http/Controllers/CompanyController.php

		-
			message: "#^Method Modules\\\\Company\\\\Jobs\\\\CreateCompanyProcess\\:\\:handle\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 1
			path: Modules/Company/Jobs/CreateCompanyProcess.php

		-
			message: "#^Property Modules\\\\Company\\\\Jobs\\\\DeleteCompanyProcess\\:\\:\\$author is unused\\.$#"
			count: 1
			path: Modules/Company/Jobs/DeleteCompanyProcess.php

		-
			message: "#^Property Modules\\\\Company\\\\Jobs\\\\DeleteCompanyProcess\\:\\:\\$companyRepository is never read, only written\\.$#"
			count: 1
			path: Modules/Company/Jobs/DeleteCompanyProcess.php

		-
			message: "#^Property Modules\\\\Company\\\\Jobs\\\\DeleteCompanyProcess\\:\\:\\$id is never read, only written\\.$#"
			count: 1
			path: Modules/Company/Jobs/DeleteCompanyProcess.php

		-
			message: "#^Property Modules\\\\Company\\\\Jobs\\\\DeleteCompanyProcess\\:\\:\\$params is unused\\.$#"
			count: 1
			path: Modules/Company/Jobs/DeleteCompanyProcess.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\<Modules\\\\Company\\\\Entities\\\\Company, Modules\\\\Company\\\\Entities\\\\Blog\\>\\:\\:searchable\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Listeners/MetaAttributeModified.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Meta\\\\Events\\\\SavedMetaProcessed\\.$#"
			count: 1
			path: Modules/Company/Listeners/MetaAttributeModified.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type Modules\\\\Company\\\\Listeners\\\\CompanyWasCreated is not subtype of native type Modules\\\\Company\\\\Events\\\\CreateCompanyProcessed\\.$#"
			count: 1
			path: Modules/Company/Listeners/NotifyNewCompany.php

		-
			message: "#^Parameter \\$event of method Modules\\\\Company\\\\Listeners\\\\NotifyNewCompany\\:\\:handle\\(\\) has invalid type Modules\\\\Company\\\\Listeners\\\\CompanyWasCreated\\.$#"
			count: 1
			path: Modules/Company/Listeners/NotifyNewCompany.php

		-
			message: "#^Access to property \\$audits on an unknown class Modules\\\\Company\\\\Events\\\\Modules\\\\Company\\\\Entities\\\\Company\\.$#"
			count: 1
			path: Modules/Company/Listeners/NotifyUpdatedCompany.php

		-
			message: "#^Access to property \\$display_name on an unknown class Modules\\\\Company\\\\Events\\\\Modules\\\\Company\\\\Entities\\\\Company\\.$#"
			count: 1
			path: Modules/Company/Listeners/NotifyUpdatedCompany.php

		-
			message: "#^Access to property \\$id on an unknown class Modules\\\\Company\\\\Events\\\\Modules\\\\Company\\\\Entities\\\\Company\\.$#"
			count: 1
			path: Modules/Company/Listeners/NotifyUpdatedCompany.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type Modules\\\\Company\\\\Listeners\\\\CompanyWasCreated is not subtype of native type Modules\\\\Company\\\\Events\\\\UpdateCompanyProcessed\\.$#"
			count: 1
			path: Modules/Company/Listeners/NotifyUpdatedCompany.php

		-
			message: "#^Parameter \\$event of method Modules\\\\Company\\\\Listeners\\\\NotifyUpdatedCompany\\:\\:handle\\(\\) has invalid type Modules\\\\Company\\\\Listeners\\\\CompanyWasCreated\\.$#"
			count: 1
			path: Modules/Company/Listeners/NotifyUpdatedCompany.php

		-
			message: "#^Call to static method create\\(\\) on an unknown class NotificationChannels\\\\Telegram\\\\TelegramMessage\\.$#"
			count: 1
			path: Modules/Company/Notifications/NotifyAdminProcesscing.php

		-
			message: "#^Class NotificationChannels\\\\Telegram\\\\TelegramChannel not found\\.$#"
			count: 1
			path: Modules/Company/Notifications/NotifyAdminProcesscing.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$display_name\\.$#"
			count: 5
			path: Modules/Company/Notifications/NotifyRemindJobExpireAfter3Days.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/Company/Notifications/NotifyRemindJobExpireAfter3Days.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$emails_cc\\.$#"
			count: 1
			path: Modules/Company/Notifications/NotifyRemindJobExpireAfter3Days.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$emails_cc\\.$#"
			count: 1
			path: Modules/Company/Notifications/NotifyRemindJobExpireAfter3Days.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Repositories/Eloquents/CompanyEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Repositories/Eloquents/CompanyEloquentRepository.php

		-
			message: "#^Class CompanyController not found\\.$#"
			count: 1
			path: Modules/Company/Routes/web.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$personCharge\\.$#"
			count: 1
			path: Modules/Company/Sheets/CompanyInfoDataSheetForCs.php

		-
			message: "#^PHPDoc tag @property has invalid value \\(\\$personCharge\\)\\: Unexpected token \"\\$personCharge\", expected type at offset 17$#"
			count: 1
			path: Modules/Company/Sheets/CompanyInfoDataSheetForCs.php

		-
			message: "#^Property Modules\\\\Company\\\\Sheets\\\\CompanyInfoDataSheetForCs\\:\\:\\$personInCharge \\(Illuminate\\\\Database\\\\Eloquent\\\\Collection\\|null\\) does not accept Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), Amscore\\\\Admin\\\\Auth\\\\Database\\\\Administrator\\>\\>\\.$#"
			count: 1
			path: Modules/Company/Sheets/CompanyInfoDataSheetForCs.php

		-
			message: "#^Property Modules\\\\Company\\\\Sheets\\\\CompanyInfoDataSheetForCs\\:\\:\\$personInCharge is never read, only written\\.$#"
			count: 1
			path: Modules/Company/Sheets/CompanyInfoDataSheetForCs.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$benefits\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$description_str\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$full_addresses\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$image_logo_url\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$phone\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$recruitment_process\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$slug\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$status_display\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$tagline\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:\\$website\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Call to an undefined method Modules\\\\Company\\\\Transformers\\\\AuditTransformer\\:\\:getKey\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/Company/Transformers/AuditTransformer.php

		-
			message: "#^Call to method aggregations\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyCollection.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyCollection.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyCollection.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyCollection.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\Company\\\\Transformers\\\\CompanyCollection\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyCollection.php

		-
			message: "#^Method Modules\\\\Company\\\\Transformers\\\\CompanyCollection\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyCollection.php

		-
			message: "#^Method Modules\\\\Company\\\\Transformers\\\\CompanyCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyCollection.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/Company/Transformers/CompanyCollection.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/Company/Transformers/CompanyCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$benefits\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$categories_id\\.$#"
			count: 2
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$categories_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$description_str\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$detail_url\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$extra_skills_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$follow_url\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$image_cover\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$image_galleries\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$image_logo\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$industries_id\\.$#"
			count: 2
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$industries_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$members\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$nationalities_id\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$num_followeres\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$num_job_openings\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$num_jobs\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$num_viewers\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$skills_id\\.$#"
			count: 3
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$skills_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$social_network\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$tagline\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$website\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Call to an undefined method Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:getTaxonomies\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Call to an undefined method Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:relationLoaded\\(\\)\\.$#"
			count: 3
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 106$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$addresses\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$benefits\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$best_skills\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$categories_arr\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$categories_str\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$company_size\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$description_str\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$draft\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$follow_url\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$highlight\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$image_galleries\\.$#"
			count: 3
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$image_logo\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$industries_arr\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$industries_str\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$introduces\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$is_highlight\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$is_spotlight\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$job_opening_url\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$nationalities\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$news\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$num_followeres\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$num_job_openings\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$num_jobs\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$num_viewers\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$products\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$skills_arr\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$skills_str\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$social_network\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$spotlight\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$status_display\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$tagline\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$website\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\Company\\\\Transformers\\\\CompanyResource\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Method Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 106$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\EmployeeResource\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/EmployeeResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 106$#"
			count: 1
			path: Modules/Company/Transformers/EmployeeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\IntroduceResource\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Company/Transformers/IntroduceResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\IntroduceResource\\:\\:\\$image\\.$#"
			count: 1
			path: Modules/Company/Transformers/IntroduceResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\IntroduceResource\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Company/Transformers/IntroduceResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 106$#"
			count: 1
			path: Modules/Company/Transformers/IntroduceResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\ProductResource\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Company/Transformers/ProductResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\ProductResource\\:\\:\\$image\\.$#"
			count: 1
			path: Modules/Company/Transformers/ProductResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\ProductResource\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Company/Transformers/ProductResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 106$#"
			count: 1
			path: Modules/Company/Transformers/ProductResource.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/ElasticScout/Engines/ElasticSearchEngine.php

		-
			message: "#^Call to method searchableAs\\(\\) on an unknown class Laravel\\\\Scout\\\\Searchable\\.$#"
			count: 1
			path: Modules/ElasticScout/Engines/ElasticSearchEngine.php

		-
			message: "#^PHPDoc tag @var for variable \\$model has invalid type Laravel\\\\Scout\\\\Searchable\\.$#"
			count: 1
			path: Modules/ElasticScout/Engines/ElasticSearchEngine.php

		-
			message: "#^Method Modules\\\\ElasticScout\\\\Http\\\\Controllers\\\\ElasticScoutController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/ElasticScout/Http/Controllers/ElasticScoutController.php

		-
			message: "#^Method Modules\\\\ElasticScout\\\\Http\\\\Controllers\\\\ElasticScoutController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/ElasticScout/Http/Controllers/ElasticScoutController.php

		-
			message: "#^Method Modules\\\\ElasticScout\\\\Http\\\\Controllers\\\\ElasticScoutController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/ElasticScout/Http/Controllers/ElasticScoutController.php

		-
			message: "#^Method Modules\\\\ElasticScout\\\\Http\\\\Controllers\\\\ElasticScoutController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/ElasticScout/Http/Controllers/ElasticScoutController.php

		-
			message: "#^Method Modules\\\\ElasticScout\\\\Http\\\\Controllers\\\\ElasticScoutController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/ElasticScout/Http/Controllers/ElasticScoutController.php

		-
			message: "#^Method Modules\\\\ElasticScout\\\\Http\\\\Controllers\\\\ElasticScoutController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/ElasticScout/Http/Controllers/ElasticScoutController.php

		-
			message: "#^Method Modules\\\\ElasticScout\\\\Http\\\\Controllers\\\\ElasticScoutController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/ElasticScout/Http/Controllers/ElasticScoutController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/ElasticScout/Http/Controllers/ElasticScoutController.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/ElasticScout/Jobs/ImportStages.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/ElasticScout/Jobs/Stages/BulkSource.php

		-
			message: "#^Anonymous function has an unused use \\$appNamespace\\.$#"
			count: 1
			path: Modules/ElasticScout/Services/SearchableResolver.php

		-
			message: "#^Method Modules\\\\File\\\\DetectFile\\\\DetectProcess\\:\\:detect\\(\\) with return type void returns \\$this\\(Modules\\\\File\\\\DetectFile\\\\DetectProcess\\) but should not return anything\\.$#"
			count: 1
			path: Modules/File/DetectFile/DetectProcess.php

		-
			message: "#^Method Modules\\\\File\\\\Engines\\\\ConvertProcess\\:\\:convert\\(\\) with return type void returns \\$this\\(Modules\\\\File\\\\Engines\\\\ConvertProcess\\) but should not return anything\\.$#"
			count: 1
			path: Modules/File/Engines/ConvertProcess.php

		-
			message: "#^Access to an undefined property Modules\\\\File\\\\Entities\\\\Media\\:\\:\\$raw_text\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/File/Entities/Media.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Call to method associate\\(\\) on an unknown class Modules\\\\File\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Call to method provider\\(\\) on an unknown class Modules\\\\File\\\\Services\\\\DetectNewFile\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Class Modules\\\\File\\\\Services\\\\DetectNewFile not found\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Amscore\\\\Metable\\\\Meta\\> does not accept Amscore\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:cloneFile\\(\\) should return string but returns false\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Amscore\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:uploadBy\\(\\) has invalid return type Modules\\\\File\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:uploadBy\\(\\) should return Modules\\\\File\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\<Amscore\\\\Admin\\\\Auth\\\\Database\\\\Administrator\\>\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Result of method Modules\\\\File\\\\Entities\\\\Media\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$image_galleries\\.$#"
			count: 1
			path: Modules/File/Http/Controllers/API/CompanyController.php

		-
			message: "#^Array has 2 duplicate keys with value 'image_galleries\\.required' \\('image_galleries\\.required', 'image_galleries\\.required'\\)\\.$#"
			count: 1
			path: Modules/File/Http/Controllers/API/CompanyController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo\\:\\:disableCache\\(\\)\\.$#"
			count: 1
			path: Modules/File/Http/Controllers/API/MediaController.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$id$#"
			count: 2
			path: Modules/File/Http/Controllers/FileController.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Spatie\\\\MediaLibrary\\\\Events\\\\MediaHasBeenAdded\\.$#"
			count: 1
			path: Modules/File/Listeners/HandleMediaUploaded.php

		-
			message: "#^Call to an undefined static method Illuminate\\\\Support\\\\ServiceProvider\\:\\:createPermission\\(\\)\\.$#"
			count: 1
			path: Modules/File/Providers/FileServiceProvider.php

		-
			message: "#^Call to static method create\\(\\) on an unknown class Modules\\\\File\\\\Providers\\\\Menu\\.$#"
			count: 1
			path: Modules/File/Providers/FileServiceProvider.php

		-
			message: "#^Call to static method find\\(\\) on an unknown class Modules\\\\File\\\\Providers\\\\Menu\\.$#"
			count: 1
			path: Modules/File/Providers/FileServiceProvider.php

		-
			message: "#^Call to static method first\\(\\) on an unknown class Modules\\\\File\\\\Providers\\\\Role\\.$#"
			count: 1
			path: Modules/File/Providers/FileServiceProvider.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/File/Repositories/Eloquents/MediaEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/File/Repositories/Eloquents/MediaEloquentRepository.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/File/Services/ConvertNewMarkdownManager.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/File/Services/ParseFileManager.php

		-
			message: "#^Call to an undefined method Spatie\\\\MediaLibrary\\\\Models\\\\Media\\:\\:getPathFolder\\(\\)\\.$#"
			count: 1
			path: Modules/File/Services/PathGenerator.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\Firebase\\\\Entities\\\\FcmTopic\\>\\|Modules\\\\Firebase\\\\Entities\\\\FcmTopic\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Firebase/Console/SubscribeTopicAllDeviceToken.php

		-
			message: "#^Access to an undefined property Modules\\\\Firebase\\\\Entities\\\\DeviceToken\\:\\:\\$device_info\\.$#"
			count: 1
			path: Modules/Firebase/Entities/DeviceToken.php

		-
			message: "#^Access to an undefined property Modules\\\\Firebase\\\\Entities\\\\DeviceToken\\:\\:\\$device_token\\.$#"
			count: 2
			path: Modules/Firebase/Entities/DeviceToken.php

		-
			message: "#^Access to an undefined property Modules\\\\Firebase\\\\Entities\\\\DeviceToken\\:\\:\\$user_id\\.$#"
			count: 1
			path: Modules/Firebase/Entities/DeviceToken.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\belongsTo\\.$#"
			count: 1
			path: Modules/Firebase/Entities/FcmDeviceTokenActivity.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(string device_token\\)\\: Unexpected token \"device_token\", expected variable at offset 103$#"
			count: 1
			path: Modules/Firebase/Events/SendCloudMessageFailed.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(string device_token\\)\\: Unexpected token \"device_token\", expected variable at offset 133$#"
			count: 1
			path: Modules/Firebase/Events/SendCloudMessageSuccessful.php

		-
			message: "#^Method Modules\\\\Firebase\\\\Http\\\\Controllers\\\\FirebaseController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Firebase/Http/Controllers/FirebaseController.php

		-
			message: "#^Method Modules\\\\Firebase\\\\Http\\\\Controllers\\\\FirebaseController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Firebase/Http/Controllers/FirebaseController.php

		-
			message: "#^Method Modules\\\\Firebase\\\\Http\\\\Controllers\\\\FirebaseController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Firebase/Http/Controllers/FirebaseController.php

		-
			message: "#^Method Modules\\\\Firebase\\\\Http\\\\Controllers\\\\FirebaseController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Firebase/Http/Controllers/FirebaseController.php

		-
			message: "#^Method Modules\\\\Firebase\\\\Http\\\\Controllers\\\\FirebaseController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Firebase/Http/Controllers/FirebaseController.php

		-
			message: "#^Method Modules\\\\Firebase\\\\Http\\\\Controllers\\\\FirebaseController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Firebase/Http/Controllers/FirebaseController.php

		-
			message: "#^Method Modules\\\\Firebase\\\\Http\\\\Controllers\\\\FirebaseController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Firebase/Http/Controllers/FirebaseController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Firebase/Http/Controllers/FirebaseController.php

		-
			message: "#^Method Modules\\\\Firebase\\\\Jobs\\\\SendCloudMessaging\\:\\:getFcmMessage\\(\\) should return NotificationChannels\\\\Fcm\\\\FcmMessage but empty return statement found\\.$#"
			count: 1
			path: Modules/Firebase/Jobs/SendCloudMessaging.php

		-
			message: "#^Method Modules\\\\Firebase\\\\Jobs\\\\SendCloudMessaging\\:\\:handle\\(\\) with return type void returns array but should not return anything\\.$#"
			count: 1
			path: Modules/Firebase/Jobs/SendCloudMessaging.php

		-
			message: "#^Method Modules\\\\Firebase\\\\Jobs\\\\SendCloudMessaging\\:\\:handle\\(\\) with return type void returns array\\<int, array\\|Kreait\\\\Firebase\\\\Messaging\\\\MulticastSendReport\\> but should not return anything\\.$#"
			count: 1
			path: Modules/Firebase/Jobs/SendCloudMessaging.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(string device_token\\)\\: Unexpected token \"device_token\", expected variable at offset 94$#"
			count: 1
			path: Modules/Firebase/Jobs/SendCloudMessaging.php

		-
			message: "#^Method Modules\\\\Firebase\\\\Jobs\\\\SendCloudMessaging\\:\\:messaging\\(\\) should return Kreait\\\\Firebase\\\\Messaging but returns Kreait\\\\Firebase\\\\Contract\\\\Messaging\\.$#"
			count: 1
			path: Modules/Firebase/Jobs/SendCloudMessaging.php

		-
			message: "#^Class Modules\\\\Firebase\\\\Providers\\\\Illuminate\\\\Notifications\\\\Events\\\\NotificationSent not found\\.$#"
			count: 1
			path: Modules/Firebase/Providers/EventServiceProvider.php

		-
			message: "#^Access to an undefined property Modules\\\\Firebase\\\\Entities\\\\DeviceToken\\:\\:\\$device_token\\.$#"
			count: 4
			path: Modules/Firebase/Services/Firebase.php

		-
			message: "#^Access to an undefined property Modules\\\\Firebase\\\\Entities\\\\FcmTopic\\:\\:\\$name\\.$#"
			count: 4
			path: Modules/Firebase/Services/Firebase.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\Firebase\\\\Entities\\\\FcmTopic\\>\\|Modules\\\\Firebase\\\\Entities\\\\FcmTopic\\:\\:\\$name\\.$#"
			count: 2
			path: Modules/Firebase/Tests/Unit/FirebaseServiceTest.php

		-
			message: "#^Access to an undefined property Modules\\\\Firebase\\\\Entities\\\\DeviceToken\\:\\:\\$device_token\\.$#"
			count: 2
			path: Modules/Firebase/Tests/Unit/FirebaseServiceTest.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$candidate$#"
			count: 1
			path: Modules/HackerRank/Events/CandidateDidntCompleteTheTest.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$candidate$#"
			count: 1
			path: Modules/HackerRank/Events/HackerRankHaveReport.php

		-
			message: "#^Method Modules\\\\HackerRank\\\\Http\\\\Controllers\\\\HackerRankController\\:\\:webhook\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\Http\\\\JsonResponse\\.$#"
			count: 1
			path: Modules/HackerRank/Http/Controllers/HackerRankController.php

		-
			message: "#^Parameter \\#1 \\$data of method Illuminate\\\\Contracts\\\\Routing\\\\ResponseFactory\\:\\:json\\(\\) expects array\\|object\\|string, true given\\.$#"
			count: 1
			path: Modules/HackerRank/Http/Controllers/HackerRankController.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: Modules/HackerRank/Listeners/InviteCandidateAfterSendConfirm.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$jobs\\.$#"
			count: 1
			path: Modules/Job/Actions/AddInviteJob.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$status\\.$#"
			count: 3
			path: Modules/Job/Actions/AddInviteJob.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:jobs\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/AddInviteJob.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$status\\.$#"
			count: 3
			path: Modules/Job/Actions/AddInviteUser.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$user_id\\.$#"
			count: 1
			path: Modules/Job/Actions/AddInviteUser.php

		-
			message: "#^Call to protected method show\\(\\) of class Amscore\\\\Admin\\\\Actions\\\\Response\\.$#"
			count: 1
			path: Modules/Job/Actions/AddSeeder.php

		-
			message: "#^Call to protected method show\\(\\) of class Amscore\\\\Admin\\\\Actions\\\\Response\\.$#"
			count: 1
			path: Modules/Job/Actions/CrossApply.php

		-
			message: "#^Cannot call method latest\\(\\) on string\\.$#"
			count: 1
			path: Modules/Job/Actions/CrossApply.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$addresses\\.$#"
			count: 1
			path: Modules/Job/Actions/DuplicateJob.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/Job/Actions/DuplicateJob.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$job_information\\.$#"
			count: 1
			path: Modules/Job/Actions/DuplicateJob.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$job_meta\\.$#"
			count: 1
			path: Modules/Job/Actions/DuplicateJob.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$job_note\\.$#"
			count: 1
			path: Modules/Job/Actions/DuplicateJob.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$job_translation\\.$#"
			count: 1
			path: Modules/Job/Actions/DuplicateJob.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$media\\.$#"
			count: 1
			path: Modules/Job/Actions/DuplicateJob.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$taxonomies\\.$#"
			count: 1
			path: Modules/Job/Actions/DuplicateJob.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:addMedia\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/DuplicateJob.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:addresses\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/DuplicateJob.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:markAsReview\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/DuplicateJob.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:taxonomies\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/DuplicateJob.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getReportFromLeverAs\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/GetLeverReport.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$job\\.$#"
			count: 1
			path: Modules/Job/Actions/GetLeverReportAs.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$resume\\.$#"
			count: 1
			path: Modules/Job/Actions/GetLeverReportAs.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getReportFromLeverAs\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/GetLeverReportAs.php

		-
			message: "#^Call to an undefined method object\\:\\:getKey\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/GetLeverReportAs.php

		-
			message: "#^Call to an undefined method object\\:\\:isCompletedLever\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/GetLeverReportAs.php

		-
			message: "#^Call to protected method show\\(\\) of class Amscore\\\\Admin\\\\Actions\\\\Response\\.$#"
			count: 1
			path: Modules/Job/Actions/GetLeverReportAs.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:solvableHackerRank\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/MakeSolvableHackerRank.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$job\\.$#"
			count: 2
			path: Modules/Job/Actions/MarkReadyWithoutEvent.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:notAlreadySendMail\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/MarkReadyWithoutEvent.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$personChargeJob\\.$#"
			count: 1
			path: Modules/Job/Actions/PersonInCharge.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:personChargeJob\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/PersonInCharge.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$fbads_sheet_id\\.$#"
			count: 2
			path: Modules/Job/Actions/RemoveFacebookAds.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$fbads_spreadsheet_id\\.$#"
			count: 1
			path: Modules/Job/Actions/RemoveFacebookAds.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Entities\\\\Job\\:\\:removeMeta\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Actions/RemoveFacebookAds.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$ggads_sheet_id\\.$#"
			count: 2
			path: Modules/Job/Actions/RemoveGoogleAds.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$ggads_spreadsheet_id\\.$#"
			count: 1
			path: Modules/Job/Actions/RemoveGoogleAds.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Entities\\\\Job\\:\\:removeMeta\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Actions/RemoveGoogleAds.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$creator_id\\.$#"
			count: 1
			path: Modules/Job/Actions/SendCandidateConfirmApplied.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Job/Actions/SendCandidateConfirmApplied.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$job\\.$#"
			count: 1
			path: Modules/Job/Actions/SendCandidateConfirmApplied.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:notify\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/SendCandidateConfirmApplied.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: Modules/Job/Actions/SendCandidateConfirmApplied.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$job\\.$#"
			count: 1
			path: Modules/Job/Actions/SendCandidateToEmployer.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$resume\\.$#"
			count: 1
			path: Modules/Job/Actions/SendCandidateToEmployer.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:notify\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/SendCandidateToEmployer.php

		-
			message: "#^Method Modules\\\\Job\\\\Actions\\\\SendCandidateToEmployer\\:\\:form\\(\\) should return string but return statement is missing\\.$#"
			count: 1
			path: Modules/Job/Actions/SendCandidateToEmployer.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$sent_at\\.$#"
			count: 1
			path: Modules/Job/Actions/SendJobInvite.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$status\\.$#"
			count: 3
			path: Modules/Job/Actions/SendJobInvite.php

		-
			message: "#^Parameter \\#1 \\$job_invite of class Modules\\\\Job\\\\Jobs\\\\SendJobInviteUser constructor expects Modules\\\\Job\\\\Entities\\\\JobInvitationEmail, Illuminate\\\\Database\\\\Eloquent\\\\Model given\\.$#"
			count: 1
			path: Modules/Job/Actions/SendJobInvite.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:markHackerrankReported\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Actions/SendReportHackerrank.php

		-
			message: "#^Method Amscore\\\\Admin\\\\Actions\\\\Action\\:\\:__construct\\(\\) invoked with 1 parameter, 0 required\\.$#"
			count: 1
			path: Modules/Job/Actions/ShowDetailFrontend.php

		-
			message: "#^Method Amscore\\\\Admin\\\\Actions\\\\Action\\:\\:__construct\\(\\) invoked with 1 parameter, 0 required\\.$#"
			count: 1
			path: Modules/Job/Actions/ShowProfileCandidate.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$media_id\\.$#"
			count: 1
			path: Modules/Job/Actions/UpdateAvailableCv.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$social_column\\.$#"
			count: 1
			path: Modules/Job/Actions/UpdateFBJobXML.php

		-
			message: "#^Class Modules\\\\Job\\\\Notifications\\\\EmailAutoConfirmationSingleApply constructor invoked with 1 parameter, 0 required\\.$#"
			count: 1
			path: Modules/Job/Console/CheckEmailMissingForCandidate.php

		-
			message: "#^Result of method Illuminate\\\\Console\\\\Command\\:\\:info\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Job/Console/ExportJobForCs.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$extra_skills_slug\\.$#"
			count: 1
			path: Modules/Job/Console/ExportJobsForRecommendations.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$job_levels_slug\\.$#"
			count: 1
			path: Modules/Job/Console/ExportJobsForRecommendations.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$job_types_slug\\.$#"
			count: 1
			path: Modules/Job/Console/ExportJobsForRecommendations.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$skills_slug\\.$#"
			count: 1
			path: Modules/Job/Console/ExportJobsForRecommendations.php

		-
			message: "#^Comparison operation \"\\=\\=\" between array\\|bool\\|string\\|null and int\\<0, max\\> results in an error\\.$#"
			count: 1
			path: Modules/Job/Console/ExportJobsForRecommendations.php

		-
			message: "#^Parameter \\#1 \\$count of method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Modules\\\\Job\\\\Entities\\\\Job\\>\\:\\:chunk\\(\\) expects int, array\\|bool\\|string\\|null given\\.$#"
			count: 1
			path: Modules/Job/Console/ExportJobsForRecommendations.php

		-
			message: "#^Result of method Illuminate\\\\Console\\\\Command\\:\\:info\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Job/Console/ExportTrackingToSheet.php

		-
			message: "#^Method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Modules\\\\Job\\\\Entities\\\\Candidate\\>\\:\\:with\\(\\) invoked with 6 parameters, 1 required\\.$#"
			count: 1
			path: Modules/Job/Console/GoogleSheetExportCandidates.php

		-
			message: "#^Result of method Illuminate\\\\Console\\\\Command\\:\\:info\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Job/Console/GoogleSheetExportCandidates.php

		-
			message: "#^Strict comparison using \\=\\=\\= between Modules\\\\Job\\\\Entities\\\\Job and null will always evaluate to false\\.$#"
			count: 1
			path: Modules/Job/Console/ImportSimilarJobs.php

		-
			message: "#^Variable \\$index in PHPDoc tag @var does not match any variable in the foreach loop\\: \\$data, \\$item$#"
			count: 1
			path: Modules/Job/Console/ImportSimilarJobs.php

		-
			message: "#^Parameter \\#1 \\$value of method Illuminate\\\\Database\\\\Query\\\\Builder\\:\\:limit\\(\\) expects int, array\\|string given\\.$#"
			count: 1
			path: Modules/Job/Console/ParseJobProcess.php

		-
			message: "#^Parameter \\#1 \\$value of method Carbon\\\\Carbon\\:\\:subDays\\(\\) expects int, array\\|string\\|null given\\.$#"
			count: 1
			path: Modules/Job/Console/ReportRecruitmentProcess.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$owned_id\\.$#"
			count: 1
			path: Modules/Job/Console/SyncJobToPartner.php

		-
			message: "#^Call to static method taxonomy\\(\\) on an unknown class Modules\\\\Job\\\\Console\\\\Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\.$#"
			count: 1
			path: Modules/Job/Console/TopDevSkillsDemographic.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Schema\\\\ColumnDefinition\\:\\:integer\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Database/Migrations/2021_08_18_103011_create_job_invitation_emails_table.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$confirmed_at\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$created_at\\.$#"
			count: 3
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$cvbuilder_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$delivered_at\\.$#"
			count: 2
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$developer_deleted_at\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$device_apply\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$documentScore\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$documentVersion\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$employer_viewed_at\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$group\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$hackerrankEnrollments\\.$#"
			count: 6
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$has_offer\\.$#"
			count: 2
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$isDocument\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$job_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$mediaById\\.$#"
			count: 3
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$media_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$partner_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$recalled_at\\.$#"
			count: 2
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$referred_by\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$resume_id\\.$#"
			count: 3
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$sent_autoflow_at\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$sent_candidate_at\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$sent_employer_at\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$sid\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$source\\.$#"
			count: 2
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$status\\.$#"
			count: 7
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$statusLogs\\.$#"
			count: 2
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$status_display\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$why_not_matching\\.$#"
			count: 2
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$level\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$uuid\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$files_cv\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Collection\\:\\:belongToTheGroup\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\:\\:disableCache\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\:\\:getModel\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\:\\:newExistingPivot\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:setSidAttribute\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Cannot access property \\$id on array\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\belongsTo\\.$#"
			count: 3
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Amscore\\\\Metable\\\\Meta\\> does not accept Amscore\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:company\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Traits\\\\customRelation\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:company\\(\\) should return Modules\\\\User\\\\Entities\\\\Traits\\\\customRelation but returns App\\\\Helpers\\\\CustomRelation\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:getCandidateProgress\\(\\) should return array but returns null\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Amscore\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:newFromBuilderRecursive\\(\\) should return static\\(Modules\\\\Job\\\\Entities\\\\Candidate\\) but returns Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:newFromElastisearch\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:newFromElastisearch\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), static\\(Modules\\\\Job\\\\Entities\\\\Candidate\\)\\>\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^PHPDoc tag @param for parameter \\$query with type Modules\\\\Job\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder is not subtype of native type Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$query$#"
			count: 5
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 4
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 4
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Parameter \\$query of method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:scopeNotFinishTheTest\\(\\) has invalid type Modules\\\\Job\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Result of method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Unsafe call to private method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:isClassInClass\\(\\) through static\\:\\:\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Unsafe call to private method Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:isMultiLevelArray\\(\\) through static\\:\\:\\.$#"
			count: 1
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 8
			path: Modules/Job/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\ES\\:\\:\\$documentScore\\.$#"
			count: 1
			path: Modules/Job/Entities/ES.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\ES\\:\\:\\$documentVersion\\.$#"
			count: 1
			path: Modules/Job/Entities/ES.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\ES\\:\\:\\$isDocument\\.$#"
			count: 1
			path: Modules/Job/Entities/ES.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\:\\:getModel\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/ES.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\:\\:newExistingPivot\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/ES.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\ES\\:\\:newFromBuilderRecursive\\(\\) should return static\\(Modules\\\\Job\\\\Entities\\\\ES\\) but returns Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: Modules/Job/Entities/ES.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\ES\\:\\:newFromElastisearch\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/Job/Entities/ES.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\ES\\:\\:newFromElastisearch\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), static\\(Modules\\\\Job\\\\Entities\\\\ES\\)\\>\\.$#"
			count: 1
			path: Modules/Job/Entities/ES.php

		-
			message: "#^Unsafe call to private method Modules\\\\Job\\\\Entities\\\\ES\\:\\:isClassInClass\\(\\) through static\\:\\:\\.$#"
			count: 1
			path: Modules/Job/Entities/ES.php

		-
			message: "#^Unsafe call to private method Modules\\\\Job\\\\Entities\\\\ES\\:\\:isMultiLevelArray\\(\\) through static\\:\\:\\.$#"
			count: 1
			path: Modules/Job/Entities/ES.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/ES.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\Job\\\\Entities\\\\JobCount\\>\\:\\:\\$refresh_count\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$benefits\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$display_name\\.$#"
			count: 7
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$emails_cc\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$faqs\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$image_logo_url\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$tagline\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$benefits\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$blog_tags\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$categories_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$categories_name\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$content\\.$#"
			count: 4
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$contract_types_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$contract_types_name\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$crm_invoice_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$emails_cc\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$experiences\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$experiences_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$expires_at\\.$#"
			count: 4
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$features\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$hot\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$image_banner\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$image_galleries\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$image_thumbnail_url\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$job_levels_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$job_meta\\.$#"
			count: 3
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$job_note\\.$#"
			count: 5
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$job_translation\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$job_types_id\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$level\\.$#"
			count: 4
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$lever_id\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$num_followers_fake\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$owned_id\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$pivot\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$refreshed_at\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$requirements\\.$#"
			count: 3
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$responsibilities\\.$#"
			count: 3
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$salary\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$sidebar_image_banner_url\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$sidebar_image_link\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$skills_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$skills_str\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$status\\.$#"
			count: 7
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$uuid\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\JobCount\\:\\:\\$job_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\JobInformation\\:\\:\\$audits\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Package\\:\\:\\$pivot\\.$#"
			count: 4
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$feature\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$district_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$latitude\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$longitude\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$postal_code\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$province_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$ward_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Modules\\\\Job\\\\Entities\\\\Job\\>\\:\\:disableCache\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Collection\\:\\:googleDeindexing\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Collection\\:\\:googleIndexing\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\Job\\\\Entities\\\\JobCount\\>\\:\\:exists\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:generateSlugOnCreate\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:generateSlugOnUpdate\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:sluggable\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\:\\:disableCache\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\:\\:view\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to function is_null\\(\\) with Carbon\\\\Carbon will always evaluate to false\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to method active\\(\\) on an unknown class Modules\\\\Subscription\\\\Traits\\\\Modules\\\\Subscription\\\\Entities\\\\Package\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Called 'pluck' on Laravel collection, but could have been retrieved as a query\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Cannot call method isForceDeleting\\(\\) on class\\-string\\|object\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\belongstoMany\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\morphToMany\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Dead catch \\- Exception is never thrown in the try block\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:addOneTerm\\(\\) with return type void returns Illuminate\\\\Database\\\\Eloquent\\\\Model but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:addTaxonomies\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Collection but returns \\$this\\(Modules\\\\Job\\\\Entities\\\\Job\\)\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:cleanAmscore\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:cleanAmscore\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:cleanAmscore\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:feature\\(\\) has invalid return type Modules\\\\Subscription\\\\Traits\\\\Modules\\\\Subscription\\\\Entities\\\\Package\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:getFollowUrlAttribute\\(\\) has invalid return type Modules\\\\Job\\\\Entities\\\\url\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:getFollowUrlAttribute\\(\\) should return Modules\\\\Job\\\\Entities\\\\url but returns Illuminate\\\\Contracts\\\\Routing\\\\UrlGenerator\\|string\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:getPerformAs\\(\\) has invalid return type Modules\\\\Job\\\\Traits\\\\uid\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:getPerformAs\\(\\) should return Modules\\\\Job\\\\Traits\\\\uid but returns string\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:getSchemaJobPostingAttribute\\(\\) should return array but returns string\\|false\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:getSummaryViewersAttribute\\(\\) should return string but empty return statement found\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:getTermsAttribute\\(\\) should return array but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), mixed\\>\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeHackerrank\\(\\) has invalid return type Modules\\\\Job\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeHackerrank\\(\\) should return Modules\\\\Job\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyClose\\(\\) has invalid return type App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyClose\\(\\) should return App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyDraft\\(\\) has invalid return type App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyDraft\\(\\) should return App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyOpen\\(\\) has invalid return type App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyOpen\\(\\) should return App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyReview\\(\\) has invalid return type App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyReview\\(\\) should return App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeStatus\\(\\) has invalid return type App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeStatus\\(\\) should return App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:setArraySalaryAttribute\\(\\) should return array but empty return statement found\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:setArraySalaryAttribute\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:setPackagesAttribute\\(\\) should return Modules\\\\Job\\\\Entities\\\\Job but return statement is missing\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:setServicesAttribute\\(\\) should return Modules\\\\Job\\\\Entities\\\\Job but return statement is missing\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:subscription\\(\\) has invalid return type Modules\\\\Subscription\\\\Traits\\\\Modules\\\\Subscription\\\\Entities\\\\Package\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:subscription\\(\\) should return Modules\\\\Subscription\\\\Traits\\\\Modules\\\\Subscription\\\\Entities\\\\Package\\|null but returns Modules\\\\Subscription\\\\Entities\\\\Package\\|null\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$params$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$query$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$terms$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Parameter \\#1 \\$models of method Modules\\\\Job\\\\Entities\\\\Job\\:\\:queueMakeGoogleIndexing\\(\\) expects Illuminate\\\\Database\\\\Eloquent\\\\Collection, \\$this\\(Modules\\\\Job\\\\Entities\\\\Job\\) given\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Parameter \\#1 \\$models of method Modules\\\\Job\\\\Entities\\\\Job\\:\\:queueRemoveGoogleIndexing\\(\\) expects Illuminate\\\\Database\\\\Eloquent\\\\Collection, \\$this\\(Modules\\\\Job\\\\Entities\\\\Job\\) given\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Parameter \\$expires_at of method Modules\\\\Job\\\\Entities\\\\Job\\:\\:addOneTerm\\(\\) has invalid type Modules\\\\Taxonomy\\\\Traits\\\\Carbon\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Result of method Modules\\\\Job\\\\Entities\\\\Job\\:\\:googleDeindexing\\(\\) \\(void\\) is used\\.$#"
			count: 3
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Variable \\$taxonomies in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Instanceof between array and App\\\\Traits\\\\Concerns\\\\Salary will always evaluate to false\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInformation.php

		-
			message: "#^PHPDoc tag @return has invalid value \\(;\\)\\: Unexpected token \";\", expected type at offset 76$#"
			count: 1
			path: Modules/Job/Entities/JobInformation.php

		-
			message: "#^Variable \\$value on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 4
			path: Modules/Job/Entities/JobInformation.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\JobInvitationEmail\\:\\:\\$scopeMeta\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\JobInvitationEmail\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\belongsTo\\.$#"
			count: 2
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Amscore\\\\Metable\\\\Meta\\> does not accept Amscore\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\JobInvitationEmail\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\JobInvitationEmail\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\JobInvitationEmail\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Amscore\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\JobInvitationEmail\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\JobInvitationEmail\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\JobInvitationEmail\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^Result of method Modules\\\\Job\\\\Entities\\\\JobInvitationEmail\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationEmail.php

		-
			message: "#^PHPDoc type array of property Modules\\\\Job\\\\Entities\\\\JobInvitationUser\\:\\:\\$primaryKey is not covariant with PHPDoc type string of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$primaryKey\\.$#"
			count: 1
			path: Modules/Job/Entities/JobInvitationUser.php

		-
			message: "#^PHPDoc type array of property Modules\\\\Job\\\\Entities\\\\PersonChargeJob\\:\\:\\$primaryKey is not covariant with PHPDoc type string of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$primaryKey\\.$#"
			count: 1
			path: Modules/Job/Entities/PersonChargeJob.php

		-
			message: "#^Method Modules\\\\Job\\\\Events\\\\DeleteJobProcessed\\:\\:job\\(\\) has invalid return type Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Events/DeleteJobProcessed.php

		-
			message: "#^PHPDoc tag @param for parameter \\$job with type Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job is not subtype of native type Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Events/DeleteJobProcessed.php

		-
			message: "#^Parameter \\$job of method Modules\\\\Job\\\\Events\\\\DeleteJobProcessed\\:\\:__construct\\(\\) has invalid type Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Events/DeleteJobProcessed.php

		-
			message: "#^Property Modules\\\\Job\\\\Events\\\\DeleteJobProcessed\\:\\:\\$job \\(Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\) does not accept Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Events/DeleteJobProcessed.php

		-
			message: "#^Property Modules\\\\Job\\\\Events\\\\DeleteJobProcessed\\:\\:\\$job has unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job as its type\\.$#"
			count: 1
			path: Modules/Job/Events/DeleteJobProcessed.php

		-
			message: "#^Method Modules\\\\Job\\\\Events\\\\HackerRankModified\\:\\:job\\(\\) has invalid return type Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Events/HackerRankModified.php

		-
			message: "#^PHPDoc tag @param for parameter \\$job with type Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job is not subtype of native type Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Events/HackerRankModified.php

		-
			message: "#^Parameter \\$job of method Modules\\\\Job\\\\Events\\\\HackerRankModified\\:\\:__construct\\(\\) has invalid type Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Events/HackerRankModified.php

		-
			message: "#^Property Modules\\\\Job\\\\Events\\\\HackerRankModified\\:\\:\\$job \\(Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\) does not accept Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Events/HackerRankModified.php

		-
			message: "#^Property Modules\\\\Job\\\\Events\\\\HackerRankModified\\:\\:\\$job has unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job as its type\\.$#"
			count: 1
			path: Modules/Job/Events/HackerRankModified.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Events\\\\JobHasApplied\\:\\:\\$candidates\\.$#"
			count: 1
			path: Modules/Job/Events/JobHasApplied.php

		-
			message: "#^Property Modules\\\\Job\\\\Events\\\\JobHasApplied\\:\\:\\$candidate is unused\\.$#"
			count: 1
			path: Modules/Job/Events/JobHasApplied.php

		-
			message: "#^Access to offset 0 on an unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Events/NewApplyProcessed.php

		-
			message: "#^Call to method first\\(\\) on an unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 2
			path: Modules/Job/Events/NewApplyProcessed.php

		-
			message: "#^Call to method pluck\\(\\) on an unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Events/NewApplyProcessed.php

		-
			message: "#^Method Modules\\\\Job\\\\Events\\\\NewApplyProcessed\\:\\:broadcastOn\\(\\) should return Illuminate\\\\Broadcasting\\\\Channel but return statement is missing\\.$#"
			count: 1
			path: Modules/Job/Events/NewApplyProcessed.php

		-
			message: "#^Method Modules\\\\Job\\\\Events\\\\NewApplyProcessed\\:\\:candidates\\(\\) has invalid return type Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Events/NewApplyProcessed.php

		-
			message: "#^Method Modules\\\\Job\\\\Events\\\\NewApplyProcessed\\:\\:candidates\\(\\) should return Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), mixed\\>\\.$#"
			count: 1
			path: Modules/Job/Events/NewApplyProcessed.php

		-
			message: "#^Property Modules\\\\Job\\\\Events\\\\NewApplyProcessed\\:\\:\\$candidates has unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job as its type\\.$#"
			count: 1
			path: Modules/Job/Events/NewApplyProcessed.php

		-
			message: "#^Parameter \\$job of method Modules\\\\Job\\\\Events\\\\NewJobProcessed\\:\\:__construct\\(\\) has invalid type Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Events/NewJobProcessed.php

		-
			message: "#^Property Modules\\\\Job\\\\Events\\\\NewJobProcessed\\:\\:\\$creator has unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User as its type\\.$#"
			count: 1
			path: Modules/Job/Events/NewJobProcessed.php

		-
			message: "#^Property Modules\\\\Job\\\\Events\\\\NewJobProcessed\\:\\:\\$job has unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job as its type\\.$#"
			count: 1
			path: Modules/Job/Events/NewJobProcessed.php

		-
			message: "#^PHPDoc tag @param for parameter \\$candidate with type Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Candidate is not subtype of native type Modules\\\\Job\\\\Entities\\\\Candidate\\.$#"
			count: 1
			path: Modules/Job/Events/SendEmailConfirmSuccessfull.php

		-
			message: "#^Parameter \\$candidate of method Modules\\\\Job\\\\Events\\\\SendEmailConfirmSuccessfull\\:\\:__construct\\(\\) has invalid type Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Candidate\\.$#"
			count: 1
			path: Modules/Job/Events/SendEmailConfirmSuccessfull.php

		-
			message: "#^Property Modules\\\\Job\\\\Events\\\\SendEmailConfirmSuccessfull\\:\\:\\$candidate \\(Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Candidate\\) does not accept Modules\\\\Job\\\\Entities\\\\Candidate\\.$#"
			count: 1
			path: Modules/Job/Events/SendEmailConfirmSuccessfull.php

		-
			message: "#^Property Modules\\\\Job\\\\Events\\\\SendEmailConfirmSuccessfull\\:\\:\\$candidate has unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Candidate as its type\\.$#"
			count: 1
			path: Modules/Job/Events/SendEmailConfirmSuccessfull.php

		-
			message: "#^Function json not found\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/API/ApiCandidateController.php

		-
			message: "#^Method Modules\\\\Job\\\\Http\\\\Controllers\\\\API\\\\ApiCandidateController\\:\\:parseCVFile\\(\\) is unused\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/API/ApiCandidateController.php

		-
			message: "#^Instantiated class Modules\\\\Job\\\\Jobs\\\\DeleteJobProcess not found\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/API/ApiJobController.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\)\\:\\:\\$resume\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\Job\\\\Entities\\\\Candidate\\>\\|Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$why_not_matching\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$job\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:\\$apply_from\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:\\$device_apply\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:\\$has_offer\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:\\$input\\.$#"
			count: 14
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:\\$resume\\.$#"
			count: 2
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:\\$row\\.$#"
			count: 16
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:ofCompany\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:ofJob\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:orderBy\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:mergeOtherTextForNotMatching\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:builder\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:confirming\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:delivering\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:hasConfirmed\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:hasDelivered\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\:\\:isReady\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Left side of && is always true\\.$#"
			count: 2
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Method Amscore\\\\Admin\\\\Grid\\\\Column\\:\\:modal\\(\\) invoked with 2 parameters, 0\\-1 required\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Parameter \\#1 \\$row of method Amscore\\\\Admin\\\\Actions\\\\RowAction\\:\\:setRow\\(\\) expects Illuminate\\\\Database\\\\Eloquent\\\\Model, \\$this\\(Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\) given\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Parameter \\#1 \\$terms of method Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:prepareTermData\\(\\) expects Modules\\\\Taxonomy\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Collection, Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\Taxonomy\\\\Entities\\\\Terms\\> given\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Parameter \\#1 \\$tool of method Amscore\\\\Admin\\\\Form\\\\Tools\\:\\:add\\(\\) expects string, App\\\\Tools\\\\MarkdownTool given\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Parameter \\#1 \\$tool of method Amscore\\\\Admin\\\\Form\\\\Tools\\:\\:add\\(\\) expects string, App\\\\Tools\\\\SendEmailCandidate given\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Parameter \\#1 \\$tool of method Amscore\\\\Admin\\\\Grid\\\\Tools\\:\\:append\\(\\) expects Amscore\\\\Admin\\\\Grid\\\\Tools\\\\AbstractTool\\|string, Modules\\\\Job\\\\Actions\\\\AddSeeder given\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Parameter \\#4 \\$row of class Amscore\\\\Admin\\\\Grid\\\\Displayers\\\\Editable constructor expects stdClass, \\$this\\(Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\) given\\.$#"
			count: 2
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Parameter \\#4 \\$row of class Amscore\\\\Admin\\\\Grid\\\\Displayers\\\\Label constructor expects stdClass, \\$this\\(Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\) given\\.$#"
			count: 5
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Parameter \\#4 \\$row of class Amscore\\\\Admin\\\\Grid\\\\Displayers\\\\Link constructor expects stdClass, \\$this\\(Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\) given\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Parameter \\#4 \\$row of class App\\\\Tools\\\\Tooltip constructor expects stdClass, \\$this\\(Modules\\\\Job\\\\Http\\\\Controllers\\\\CandidateController\\) given\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\Company\\\\Entities\\\\Company\\>\\|Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Support\\\\HigherOrderCollectionProxy\\<string, Modules\\\\Subscription\\\\Entities\\\\Term\\|null\\>\\:\\:\\$name\\.$#"
			count: 2
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$display_name\\.$#"
			count: 2
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$categories_name\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$link_crawl\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$similar_jobs_count\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:\\$audits\\.$#"
			count: 5
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:\\$company\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:\\$expires_at\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:\\$input\\.$#"
			count: 8
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:\\$is_hot\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:\\$job_count\\.$#"
			count: 3
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:\\$published_at\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:\\$similar_jobs_count\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:\\$social_column\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:ofCompany\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:select\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:withCount\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:addTaxonomies\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:detachTaxonomiesCollection\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:disableAuditing\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getOldValuesAudit\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:job_information\\(\\)\\.$#"
			count: 3
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:job_note\\(\\)\\.$#"
			count: 3
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:job_translation\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:setOldValuesAudit\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:setPackagesAttribute\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:setServicesAttribute\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Entities\\\\Job\\:\\:approval\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:getKey\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Method Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:approval\\(\\) should return Illuminate\\\\Http\\\\Response but returns string\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Method Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:cleanAmscore\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Method Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:cleanAmscore\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Method Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:cleanAmscore\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Parameter \\#4 \\$row of class Amscore\\\\Admin\\\\Grid\\\\Displayers\\\\Label constructor expects stdClass, Modules\\\\Job\\\\Entities\\\\Job given\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Variable \\$packages in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Variable \\$services in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobInviteController\\:\\:\\$author\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobInviteController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobInviteController\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobInviteController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobInviteController\\:\\:\\$input\\.$#"
			count: 2
			path: Modules/Job/Http/Controllers/JobInviteController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobInviteController\\:\\:\\$jobs\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobInviteController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobInviteController\\:\\:\\$resume\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobInviteController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobInviteController\\:\\:\\$row\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobInviteController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobInviteController\\:\\:\\$sent_at\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobInviteController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:orderBy\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobInviteController.php

		-
			message: "#^Access to property \\$scopeMeta on an unknown class Modules\\\\Job\\\\Http\\\\Requests\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Http/Requests/StoreJobRequest.php

		-
			message: "#^Access to property \\$scopeTerm on an unknown class Modules\\\\Job\\\\Http\\\\Requests\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Http/Requests/StoreJobRequest.php

		-
			message: "#^Class Modules\\\\Job\\\\Http\\\\Requests\\\\Job not found\\.$#"
			count: 2
			path: Modules/Job/Http/Requests/StoreJobRequest.php

		-
			message: "#^Access to property \\$scopeMeta on an unknown class Modules\\\\Job\\\\Http\\\\Requests\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Http/Requests/UpdateJobRequest.php

		-
			message: "#^Access to property \\$scopeTerm on an unknown class Modules\\\\Job\\\\Http\\\\Requests\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Http/Requests/UpdateJobRequest.php

		-
			message: "#^Class Modules\\\\Job\\\\Http\\\\Requests\\\\Job not found\\.$#"
			count: 2
			path: Modules/Job/Http/Requests/UpdateJobRequest.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Support\\\\Collection\\<int, mixed\\>\\:\\:belongToTheGroup\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Jobs\\\\CandidateApply\\:\\:error\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^Call to static method isJSON\\(\\) on an unknown class Modules\\\\Job\\\\Jobs\\\\Helpers\\.$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^Method Modules\\\\Job\\\\Jobs\\\\CandidateApply\\:\\:actionParseModify\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^Method Modules\\\\Job\\\\Jobs\\\\CandidateApply\\:\\:applyNonLogin\\(\\) is unused\\.$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^Method Modules\\\\Job\\\\Jobs\\\\CandidateApply\\:\\:getEmailByParse\\(\\) with return type void returns array\\|string\\|null but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^Method Modules\\\\Job\\\\Jobs\\\\CandidateApply\\:\\:getEmailByParse\\(\\) with return type void returns false but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$collect\\)\\: Unexpected token \"\\[\", expected type at offset 99$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$email\\)\\: Unexpected token \"\\[\", expected type at offset 46$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$emails\\)\\: Unexpected token \"\\[\", expected type at offset 54$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$media\\)\\: Unexpected token \"\\[\", expected type at offset 71$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$model\\)\\: Unexpected token \"\\[\", expected type at offset 43$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$parse\\)\\: Unexpected token \"\\[\", expected type at offset 80$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$var$#"
			count: 2
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^PHPDoc tag @var has invalid value \\(\\[object\\]\\)\\: Unexpected token \"\\[\", expected type at offset 212$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\CandidateApply\\:\\:\\$user_resume \\(Modules\\\\User\\\\Entities\\\\User\\) in empty\\(\\) is not falsy\\.$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^Property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$addresses \\(Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\VietnamArea\\\\Entities\\\\Address\\>\\) does not accept array\\{array\\{street\\: string\\}\\}\\.$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^Result of method Modules\\\\Job\\\\Jobs\\\\CandidateApply\\:\\:getEmailByParse\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^Variable \\$email might not be defined\\.$#"
			count: 1
			path: Modules/Job/Jobs/CandidateApply.php

		-
			message: "#^Method Modules\\\\Job\\\\Jobs\\\\CreateJobProcess\\:\\:handle\\(\\) with return type void returns Modules\\\\Job\\\\Entities\\\\Job but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Jobs/CreateJobProcess.php

		-
			message: "#^Parameter \\#1 \\$job of class Modules\\\\Job\\\\Events\\\\NewJobProcessed constructor expects Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job, Modules\\\\Job\\\\Entities\\\\Job given\\.$#"
			count: 1
			path: Modules/Job/Jobs/CreateJobProcess.php

		-
			message: "#^Parameter \\#1 \\$message of static method Illuminate\\\\Log\\\\Logger\\:\\:error\\(\\) expects string, array given\\.$#"
			count: 1
			path: Modules/Job/Jobs/CreateJobProcess.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: Modules/Job/Jobs/ParseJobCrawlProcess.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Support\\\\Collection\\<int, mixed\\>\\:\\:belongToTheGroup\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Jobs\\\\ProcessCandidates\\:\\:error\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^Method Modules\\\\Job\\\\Jobs\\\\ProcessCandidates\\:\\:actionParseModify\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^Method Modules\\\\Job\\\\Jobs\\\\ProcessCandidates\\:\\:applyNonLogin\\(\\) is unused\\.$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^Method Modules\\\\Job\\\\Jobs\\\\ProcessCandidates\\:\\:getEmailByParse\\(\\) with return type void returns array\\|string\\|null but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^Method Modules\\\\Job\\\\Jobs\\\\ProcessCandidates\\:\\:getEmailByParse\\(\\) with return type void returns false but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$collect\\)\\: Unexpected token \"\\[\", expected type at offset 99$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$email\\)\\: Unexpected token \"\\[\", expected type at offset 46$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$emails\\)\\: Unexpected token \"\\[\", expected type at offset 54$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$media\\)\\: Unexpected token \"\\[\", expected type at offset 71$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$model\\)\\: Unexpected token \"\\[\", expected type at offset 43$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$parse\\)\\: Unexpected token \"\\[\", expected type at offset 80$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$var$#"
			count: 2
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^PHPDoc tag @var has invalid value \\(\\[object\\]\\)\\: Unexpected token \"\\[\", expected type at offset 212$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\ProcessCandidates\\:\\:\\$user_resume \\(Modules\\\\User\\\\Entities\\\\User\\) in empty\\(\\) is not falsy\\.$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^Property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$addresses \\(Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\VietnamArea\\\\Entities\\\\Address\\>\\) does not accept array\\{array\\{street\\: string\\}\\}\\.$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^Result of method Modules\\\\Job\\\\Jobs\\\\ProcessCandidates\\:\\:getEmailByParse\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^Variable \\$email might not be defined\\.$#"
			count: 1
			path: Modules/Job/Jobs/ProcessCandidates.php

		-
			message: "#^Call to method map\\(\\) on an unknown class App\\\\Collection\\\\Collection\\.$#"
			count: 1
			path: Modules/Job/Jobs/SendJobInviteUser.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\SendJobInviteUser\\:\\:\\$JobRepositoryInterface is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/SendJobInviteUser.php

		-
			message: "#^Method Modules\\\\Job\\\\Jobs\\\\UpdateJobProcess\\:\\:handle\\(\\) with return type void returns true but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Jobs/UpdateJobProcess.php

		-
			message: "#^Access to property \\$id on an unknown class Modules\\\\Job\\\\Listeners\\\\Media\\.$#"
			count: 1
			path: Modules/Job/Listeners/ApplyWithCvBuilder.php

		-
			message: "#^Call to method each\\(\\) on an unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Listeners/ApplyWithCvBuilder.php

		-
			message: "#^Method Modules\\\\Job\\\\Listeners\\\\ApplyWithCvBuilder\\:\\:attachToMedia\\(\\) has invalid return type Modules\\\\Job\\\\Listeners\\\\Media\\.$#"
			count: 1
			path: Modules/Job/Listeners/ApplyWithCvBuilder.php

		-
			message: "#^Method Modules\\\\Job\\\\Listeners\\\\ApplyWithCvBuilder\\:\\:topdevCvHasBeenAvailableToApply\\(\\) has invalid return type Modules\\\\Job\\\\Listeners\\\\Media\\.$#"
			count: 1
			path: Modules/Job/Listeners/ApplyWithCvBuilder.php

		-
			message: "#^Call to method each\\(\\) on an unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Listeners/CountCandidate.php

		-
			message: "#^Access to an undefined property Modules\\\\HackerRank\\\\Entities\\\\HackerrankEnrollment\\:\\:\\$model\\.$#"
			count: 1
			path: Modules/Job/Listeners/DuplicateReportHackerRank.php

		-
			message: "#^Relation 'model' is not found in Modules\\\\HackerRank\\\\Entities\\\\HackerrankEnrollment model\\.$#"
			count: 1
			path: Modules/Job/Listeners/DuplicateReportHackerRank.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Job/Listeners/ModifiedJobListener.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$candidates\\.$#"
			count: 1
			path: Modules/Job/Listeners/ModifiedJobListener.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getModified\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Listeners/ModifiedJobListener.php

		-
			message: "#^Call to method sendMessage\\(\\) on an unknown class Telegram\\\\Bot\\\\Laravel\\\\Facades\\\\Telegram\\\\Bot\\\\Api\\.$#"
			count: 1
			path: Modules/Job/Listeners/ModifiedJobListener.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Job\\\\Events\\\\UpdateJobProcessed\\.$#"
			count: 1
			path: Modules/Job/Listeners/ModifiedJobListener.php

		-
			message: "#^Property Modules\\\\Job\\\\Listeners\\\\ModifiedJobListener\\:\\:\\$telegram \\(Telegram\\\\Bot\\\\Laravel\\\\Facades\\\\Telegram\\\\Bot\\\\Api\\) does not accept Telegram\\\\Bot\\\\Api\\.$#"
			count: 1
			path: Modules/Job/Listeners/ModifiedJobListener.php

		-
			message: "#^Property Modules\\\\Job\\\\Listeners\\\\ModifiedJobListener\\:\\:\\$telegram has unknown class Telegram\\\\Bot\\\\Laravel\\\\Facades\\\\Telegram\\\\Bot\\\\Api as its type\\.$#"
			count: 1
			path: Modules/Job/Listeners/ModifiedJobListener.php

		-
			message: "#^Variable \\$audit might not be defined\\.$#"
			count: 1
			path: Modules/Job/Listeners/ModifiedJobListener.php

		-
			message: "#^Access to property \\$company on an unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 2
			path: Modules/Job/Listeners/NewJobListener.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Job\\\\Events\\\\NewJobProcessed\\.$#"
			count: 1
			path: Modules/Job/Listeners/NewJobListener.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 1
			path: Modules/Job/Listeners/NewJobListener.php

		-
			message: "#^Variable \\$user in empty\\(\\) is never defined\\.$#"
			count: 1
			path: Modules/Job/Listeners/NewJobListener.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Publisher\\\\Events\\\\PublishGoogleSuccess\\.$#"
			count: 1
			path: Modules/Job/Listeners/PublishFacebookAdsCompleted.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Publisher\\\\Events\\\\PublishGoogleSuccess\\.$#"
			count: 1
			path: Modules/Job/Listeners/PublishGoogleAdsCompleted.php

		-
			message: "#^Access to property \\$company on an unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 2
			path: Modules/Job/Listeners/RemoveJobListener.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Job\\\\Events\\\\DeleteJobProcessed\\.$#"
			count: 1
			path: Modules/Job/Listeners/RemoveJobListener.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$candidates\\.$#"
			count: 1
			path: Modules/Job/Listeners/ResetHackerrank.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Meta\\\\Events\\\\SavingMetaProcessed\\.$#"
			count: 1
			path: Modules/Job/Listeners/ResetHackerrank.php

		-
			message: "#^Method Modules\\\\Job\\\\Notifications\\\\CandidateEmailMonitoring\\:\\:toTelegram\\(\\) has invalid return type Modules\\\\Job\\\\Notifications\\\\NotificationChannels\\\\Telegram\\\\TelegramMessage\\.$#"
			count: 1
			path: Modules/Job/Notifications/CandidateEmailMonitoring.php

		-
			message: "#^Method Modules\\\\Job\\\\Notifications\\\\CandidateEmailMonitoring\\:\\:toTelegram\\(\\) should return Modules\\\\Job\\\\Notifications\\\\NotificationChannels\\\\Telegram\\\\TelegramMessage but returns App\\\\Channels\\\\Messages\\\\TelegramMessage\\.$#"
			count: 1
			path: Modules/Job/Notifications/CandidateEmailMonitoring.php

		-
			message: "#^Call to method map\\(\\) on an unknown class App\\\\Collection\\\\Collection\\.$#"
			count: 1
			path: Modules/Job/Notifications/ConfirmAppliedToApplicant.php

		-
			message: "#^Method Modules\\\\Job\\\\Notifications\\\\ConfirmAppliedToApplicant\\:\\:bodyMail\\(\\) has invalid return type Modules\\\\Job\\\\Notifications\\\\html\\.$#"
			count: 1
			path: Modules/Job/Notifications/ConfirmAppliedToApplicant.php

		-
			message: "#^Method Modules\\\\Job\\\\Notifications\\\\ConfirmAppliedToApplicant\\:\\:bodyMail\\(\\) should return Modules\\\\Job\\\\Notifications\\\\html but returns string\\|false\\.$#"
			count: 1
			path: Modules/Job/Notifications/ConfirmAppliedToApplicant.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$notifiable\\)\\: Unexpected token \"\\[\", expected type at offset 25$#"
			count: 2
			path: Modules/Job/Notifications/ConfirmAppliedToApplicant.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Notifications/EmailAutoConfirmationApplyToAll.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Notifications/EmailAutoConfirmationSingleApply.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$resume\\.$#"
			count: 1
			path: Modules/Job/Notifications/NotifyCandidateAppliedToEmployer.php

		-
			message: "#^Method Modules\\\\Job\\\\Notifications\\\\NotifyCandidateAppliedToEmployer\\:\\:bodyMail\\(\\) has invalid return type Modules\\\\Job\\\\Notifications\\\\html\\.$#"
			count: 1
			path: Modules/Job/Notifications/NotifyCandidateAppliedToEmployer.php

		-
			message: "#^Method Modules\\\\Job\\\\Notifications\\\\NotifyCandidateAppliedToEmployer\\:\\:bodyMail\\(\\) should return Modules\\\\Job\\\\Notifications\\\\html but returns string\\|false\\.$#"
			count: 1
			path: Modules/Job/Notifications/NotifyCandidateAppliedToEmployer.php

		-
			message: "#^Method Modules\\\\Job\\\\Notifications\\\\NotifyCandidateAppliedToEmployer\\:\\:toEmail\\(\\) has invalid return type Modules\\\\Job\\\\Notifications\\\\email\\.$#"
			count: 1
			path: Modules/Job/Notifications/NotifyCandidateAppliedToEmployer.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$notifiable\\)\\: Unexpected token \"\\[\", expected type at offset 25$#"
			count: 3
			path: Modules/Job/Notifications/NotifyCandidateAppliedToEmployer.php

		-
			message: "#^Property Modules\\\\Job\\\\Notifications\\\\NotifyCandidateAppliedToEmployer\\:\\:\\$job is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Notifications/NotifyCandidateAppliedToEmployer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Notifications\\\\NotifyForModifiedJob\\:\\:\\$candidates\\.$#"
			count: 1
			path: Modules/Job/Notifications/NotifyForModifiedJob.php

		-
			message: "#^Method Modules\\\\Job\\\\Notifications\\\\SendEmailHackerRankReport\\:\\:bodyMail\\(\\) has invalid return type Modules\\\\Job\\\\Notifications\\\\html\\.$#"
			count: 1
			path: Modules/Job/Notifications/SendEmailHackerRankReport.php

		-
			message: "#^Method Modules\\\\Job\\\\Notifications\\\\SendEmailHackerRankReport\\:\\:bodyMail\\(\\) should return Modules\\\\Job\\\\Notifications\\\\html but returns string\\|false\\.$#"
			count: 1
			path: Modules/Job/Notifications/SendEmailHackerRankReport.php

		-
			message: "#^Method Modules\\\\Job\\\\Notifications\\\\SendEmailHackerRankReport\\:\\:toEmail\\(\\) has invalid return type Modules\\\\Job\\\\Notifications\\\\email\\.$#"
			count: 1
			path: Modules/Job/Notifications/SendEmailHackerRankReport.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$notifiable\\)\\: Unexpected token \"\\[\", expected type at offset 25$#"
			count: 3
			path: Modules/Job/Notifications/SendEmailHackerRankReport.php

		-
			message: "#^Method Modules\\\\Job\\\\Repositories\\\\Analytics\\\\ClientIdRandom\\:\\:get\\(\\) should return string\\|null but returns Ramsey\\\\Uuid\\\\UuidInterface\\.$#"
			count: 1
			path: Modules/Job/Repositories/Analytics/ClientIdRandom.php

		-
			message: "#^Anonymous function has an unused use \\$cacheLocation\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/CandidateEloquentRepository.php

		-
			message: "#^Anonymous function has an unused use \\$labelFormat\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/CandidateEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Repositories/Eloquents/CandidateEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Repositories/Eloquents/CandidateEloquentRepository.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Anonymous function has an unused use \\$keyword\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getStatusColumn\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getStatusReview\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:withoutSyncingToSearch\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Call to function array_key_exists\\(\\) with 'ids' and string will always evaluate to false\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Method ONGR\\\\ElasticsearchDSL\\\\Aggregation\\\\Bucketing\\\\FilterAggregation\\:\\:setFilter\\(\\) invoked with 2 parameters, 1 required\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'companies' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'company' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'except_ids' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'extra_skills' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'full_address' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'hot' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'level' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'ordering' on string in isset\\(\\) does not exist\\.$#"
			count: 4
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'package_ids' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'page' on string in isset\\(\\) does not exist\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'page_size' on string in isset\\(\\) does not exist\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'popular' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'region_ids' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'service_ids' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'skills_id' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'status' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Parameter \\#2 \\$search of function array_key_exists expects array, string given\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 5
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Right side of && is always false\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Class CandidateController not found\\.$#"
			count: 1
			path: Modules/Job/Routes/web.php

		-
			message: "#^Class JobController not found\\.$#"
			count: 1
			path: Modules/Job/Routes/web.php

		-
			message: "#^Class JobInviteController not found\\.$#"
			count: 1
			path: Modules/Job/Routes/web.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Services\\\\Drivers\\\\CandidateAbstractProvider\\:\\:\\$media\\.$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateAbstractProvider.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Support\\\\Collection\\<int, mixed\\>\\:\\:belongToTheGroup\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateAbstractProvider.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Repositories\\\\Contracts\\\\CandidateRepositoryInterface\\:\\:where\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateAbstractProvider.php

		-
			message: "#^Comparison operation \"\\>\" between int\\<1, max\\> and 0 is always true\\.$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateAbstractProvider.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$email\\)\\: Unexpected token \"\\[\", expected type at offset 44$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateAbstractProvider.php

		-
			message: "#^Property Modules\\\\Job\\\\Services\\\\Drivers\\\\CandidateAbstractProvider\\:\\:\\$candidateRepository \\(Modules\\\\Job\\\\Repositories\\\\Contracts\\\\CandidateRepositoryInterface\\) does not accept Modules\\\\Job\\\\Entities\\\\Candidate\\.$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateAbstractProvider.php

		-
			message: "#^Method Modules\\\\Job\\\\Services\\\\Drivers\\\\CandidateApplyWithoutLogin\\:\\:actionParseModify\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateApplyWithoutLogin.php

		-
			message: "#^Method Modules\\\\Job\\\\Services\\\\Drivers\\\\CandidateApplyWithoutLogin\\:\\:getEmailByParse\\(\\) with return type void returns array\\|string\\|null but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateApplyWithoutLogin.php

		-
			message: "#^Method Modules\\\\Job\\\\Services\\\\Drivers\\\\CandidateApplyWithoutLogin\\:\\:getEmailByParse\\(\\) with return type void returns false but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateApplyWithoutLogin.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$emails\\)\\: Unexpected token \"\\[\", expected type at offset 54$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateApplyWithoutLogin.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$parse\\)\\: Unexpected token \"\\[\", expected type at offset 80$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateApplyWithoutLogin.php

		-
			message: "#^Result of method Modules\\\\Job\\\\Services\\\\Drivers\\\\CandidateApplyWithoutLogin\\:\\:getEmailByParse\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateApplyWithoutLogin.php

		-
			message: "#^Result of \\|\\| is always true\\.$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateApplyWithoutLogin.php

		-
			message: "#^Undefined variable\\: \\$payload$#"
			count: 2
			path: Modules/Job/Services/Drivers/CandidateApplyWithoutLogin.php

		-
			message: "#^Variable \\$email might not be defined\\.$#"
			count: 1
			path: Modules/Job/Services/Drivers/CandidateApplyWithoutLogin.php

		-
			message: "#^Variable \\$payload in isset\\(\\) is never defined\\.$#"
			count: 2
			path: Modules/Job/Services/Drivers/CandidateApplyWithoutLogin.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$mediaById\\.$#"
			count: 1
			path: Modules/Job/Services/SimilarityService.php

		-
			message: "#^Method Modules\\\\Job\\\\Services\\\\SimilarityService\\:\\:checkSimilarity\\(\\) should return array but returns int\\.$#"
			count: 1
			path: Modules/Job/Services/SimilarityService.php

		-
			message: "#^Binary operation \"\\.\\=\" between 0\\|0\\.0\\|array\\{\\}\\|string\\|false\\|null and void results in an error\\.$#"
			count: 3
			path: Modules/Job/Services/helpers.php

		-
			message: "#^Result of static method App\\\\Helpers\\\\Helpers\\:\\:toSlug\\(\\) \\(void\\) is used\\.$#"
			count: 3
			path: Modules/Job/Services/helpers.php

		-
			message: "#^Variable \\$slug might not be defined\\.$#"
			count: 3
			path: Modules/Job/Services/helpers.php

		-
			message: "#^Variable \\$slugStr in empty\\(\\) always exists and is always falsy\\.$#"
			count: 3
			path: Modules/Job/Services/helpers.php

		-
			message: "#^Access to an undefined property OwenIt\\\\Auditing\\\\Models\\\\Audit\\:\\:\\$user\\.$#"
			count: 1
			path: Modules/Job/Sheets/JobDataSheetForCs.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$author\\.$#"
			count: 2
			path: Modules/Job/Sheets/JobDataSheetForCs.php

		-
			message: "#^PHPDoc tag @return has invalid value \\(\\$string\\)\\: Unexpected token \"\\$string\", expected type at offset 83$#"
			count: 1
			path: Modules/Job/Sheets/JobDataSheetForCs.php

		-
			message: "#^Property Modules\\\\Job\\\\Sheets\\\\JobDataSheetForCs\\:\\:\\$dataEmployees \\(Illuminate\\\\Database\\\\Eloquent\\\\Collection\\|null\\) does not accept Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), Modules\\\\User\\\\Entities\\\\User\\>\\>\\.$#"
			count: 1
			path: Modules/Job/Sheets/JobDataSheetForCs.php

		-
			message: "#^Property Modules\\\\Job\\\\Sheets\\\\JobEmailDataSheetForCs\\:\\:\\$dataEmployees \\(Illuminate\\\\Database\\\\Eloquent\\\\Collection\\|null\\) does not accept Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), Modules\\\\User\\\\Entities\\\\User\\>\\>\\.$#"
			count: 1
			path: Modules/Job/Sheets/JobEmailDataSheetForCs.php

		-
			message: "#^Property Modules\\\\Job\\\\Sheets\\\\JobEmailDataSheetForCs\\:\\:\\$dataMetaCompanies \\(Illuminate\\\\Database\\\\Eloquent\\\\Collection\\|null\\) does not accept Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), Modules\\\\Meta\\\\Entities\\\\Meta\\>\\>\\.$#"
			count: 1
			path: Modules/Job/Sheets/JobEmailDataSheetForCs.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:\\$exists\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:\\$metaCollect\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:\\$scopeMeta\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:getMorphClass\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:isForceDeleting\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:relationLoaded\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Call to an undefined static method Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:created\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Call to an undefined static method static\\(Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\)\\:\\:deleted\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Amscore\\\\Metable\\\\Meta\\> does not accept Amscore\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Method Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Method Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Method Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Amscore\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Method Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Method Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Method Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Parameter \\#1 \\$model of method Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:processMetaItem\\(\\) expects Illuminate\\\\Database\\\\Eloquent\\\\Model, \\$this\\(Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\) given\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Result of method Modules\\\\Job\\\\Sheets\\\\ListCandidateSheet\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Job/Sheets/ListCandidateSheet.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 2
			path: Modules/Job/Sheets/ListJobPaidSheet.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$blog_posts\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$blog_tags\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$company\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$experiences_name\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$extra_skills_name\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$full_addresses\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$job_information\\.$#"
			count: 2
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$job_levels_name\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$job_note_recruiment_process\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$job_types_name\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$requirements\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$responsibilities\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$sidebar_image_banner_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$sidebar_image_link\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$skills_name\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$status_display\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Transformers\\\\AuditTransformer\\:\\:getKey\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/Job/Transformers/AuditTransformer.php

		-
			message: "#^Call to method aggregations\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateCollection.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateCollection.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateCollection.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateCollection.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\Job\\\\Transformers\\\\CandidateCollection\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateCollection.php

		-
			message: "#^Method Modules\\\\Job\\\\Transformers\\\\CandidateCollection\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateCollection.php

		-
			message: "#^Method Modules\\\\Job\\\\Transformers\\\\CandidateCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateCollection.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/Job/Transformers/CandidateCollection.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/Job/Transformers/CandidateCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$files_cv\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$files_hackerrank\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$is_solving_hackerrank\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$job\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$resume\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$tags_hackerrank\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\Job\\\\Transformers\\\\CandidateResource\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Method Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Call to method aggregations\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobCollection.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobCollection.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobCollection.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobCollection.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\Job\\\\Transformers\\\\JobCollection\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobCollection.php

		-
			message: "#^Method Modules\\\\Job\\\\Transformers\\\\JobCollection\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobCollection.php

		-
			message: "#^Method Modules\\\\Job\\\\Transformers\\\\JobCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobCollection.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/Job/Transformers/JobCollection.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/Job/Transformers/JobCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$apply_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$benefits\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$company\\.$#"
			count: 4
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$detail_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$experiences_id\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$experiences_name\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$extra_skills_id\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$job_levels_id\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$job_levels_name\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$job_types_id\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$job_types_name\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$job_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$num_followers\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$num_ready_candidates\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$num_unqualified_candidates\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$num_viewers\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$recruiment_process\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$requirements\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$responsibilities\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$salary\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$schema_job_posting\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$skills_id\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$skills_name\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$status_display\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:getKey\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:relationLoaded\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$addresses\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$apply_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$benefits\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$candidates_count\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$created_by\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$crm_invoice_id\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$detail_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$experiences_arr\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$experiences_str\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$extra_skills\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$features\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$id\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$job_levels_arr\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$job_levels_str\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$job_types_arr\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$job_types_str\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$job_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$num_candidates\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$num_followers\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$num_ready_candidates\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$num_viewers\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$package_list\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$recruiment_process\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$requirements\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$responsibilities\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$salary\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$schema_job_posting\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$service_list\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$skills_arr\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$skills_str\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$status_display\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$summary_viewers\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\Job\\\\Transformers\\\\JobResource\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Method Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\)\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\)\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:\\$title\\.$#"
			count: 2
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\:\\:disableCache\\(\\)\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Amscore\\\\Metable\\\\Meta\\> does not accept Amscore\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Amscore\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Result of method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Anonymous function has an unused use \\$grid\\.$#"
			count: 1
			path: Modules/MessageCampaign/Http/Controllers/MessageCampaignController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:orderBy\\(\\)\\.$#"
			count: 1
			path: Modules/MessageCampaign/Http/Controllers/MessageCampaignController.php

		-
			message: "#^Class MessageCampaignController not found\\.$#"
			count: 1
			path: Modules/MessageCampaign/Routes/web.php

		-
			message: "#^Access to property \\$mimeType on an unknown class Modules\\\\Meta\\\\Entities\\\\File\\.$#"
			count: 1
			path: Modules/Meta/Entities/Meta.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/Meta/Entities/Meta.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Meta/Entities/Meta.php

		-
			message: "#^Parameter \\$file of anonymous function has invalid type Modules\\\\Meta\\\\Entities\\\\File\\.$#"
			count: 1
			path: Modules/Meta/Entities/Meta.php

		-
			message: "#^Method Modules\\\\Meta\\\\Http\\\\Controllers\\\\MetaController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Meta/Http/Controllers/MetaController.php

		-
			message: "#^Method Modules\\\\Meta\\\\Http\\\\Controllers\\\\MetaController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Meta/Http/Controllers/MetaController.php

		-
			message: "#^Method Modules\\\\Meta\\\\Http\\\\Controllers\\\\MetaController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Meta/Http/Controllers/MetaController.php

		-
			message: "#^Method Modules\\\\Meta\\\\Http\\\\Controllers\\\\MetaController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Meta/Http/Controllers/MetaController.php

		-
			message: "#^Method Modules\\\\Meta\\\\Http\\\\Controllers\\\\MetaController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Meta/Http/Controllers/MetaController.php

		-
			message: "#^Method Modules\\\\Meta\\\\Http\\\\Controllers\\\\MetaController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Meta/Http/Controllers/MetaController.php

		-
			message: "#^Method Modules\\\\Meta\\\\Http\\\\Controllers\\\\MetaController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Meta/Http/Controllers/MetaController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Meta/Http/Controllers/MetaController.php

		-
			message: "#^Parameter \\#1 \\$message of static method Illuminate\\\\Log\\\\Logger\\:\\:info\\(\\) expects string, array given\\.$#"
			count: 1
			path: Modules/Notification/Channels/DatabaseChannel.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Modules\\\\Notification\\\\Entities\\\\DatabaseNotification\\>\\:\\:searchable\\(\\)\\.$#"
			count: 1
			path: Modules/Notification/Console/CreateBulkNotification.php

		-
			message: "#^Parameter \\#1 \\$value of method Carbon\\\\Carbon\\:\\:setHour\\(\\) expects int, array\\|string\\|null given\\.$#"
			count: 1
			path: Modules/Notification/Console/CreateBulkNotification.php

		-
			message: "#^Parameter \\#1 \\$value of method Carbon\\\\Carbon\\:\\:setMinute\\(\\) expects int, array\\|string\\|null given\\.$#"
			count: 1
			path: Modules/Notification/Console/CreateBulkNotification.php

		-
			message: "#^Result of method Illuminate\\\\Console\\\\Command\\:\\:info\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Notification/Console/ReportNotification.php

		-
			message: "#^Method Modules\\\\Notification\\\\Http\\\\Controllers\\\\NotificationController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Notification/Http/Controllers/NotificationController.php

		-
			message: "#^Method Modules\\\\Notification\\\\Http\\\\Controllers\\\\NotificationController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Notification/Http/Controllers/NotificationController.php

		-
			message: "#^Method Modules\\\\Notification\\\\Http\\\\Controllers\\\\NotificationController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Notification/Http/Controllers/NotificationController.php

		-
			message: "#^Method Modules\\\\Notification\\\\Http\\\\Controllers\\\\NotificationController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Notification/Http/Controllers/NotificationController.php

		-
			message: "#^Method Modules\\\\Notification\\\\Http\\\\Controllers\\\\NotificationController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Notification/Http/Controllers/NotificationController.php

		-
			message: "#^Method Modules\\\\Notification\\\\Http\\\\Controllers\\\\NotificationController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Notification/Http/Controllers/NotificationController.php

		-
			message: "#^Method Modules\\\\Notification\\\\Http\\\\Controllers\\\\NotificationController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Notification/Http/Controllers/NotificationController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Notification/Http/Controllers/NotificationController.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: Modules/Notification/Listeners/SyncNotificationToElasticsearch.php

		-
			message: "#^Access to an undefined property Modules\\\\Post\\\\Entities\\\\Post\\:\\:\\$content\\.$#"
			count: 2
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Access to an undefined property Modules\\\\Post\\\\Entities\\\\Post\\:\\:\\$expires_at\\.$#"
			count: 2
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Access to an undefined property Modules\\\\Post\\\\Entities\\\\Post\\:\\:\\$published_at\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Access to an undefined property Modules\\\\Post\\\\Entities\\\\Post\\:\\:\\$scopeMeta\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:generateSlugOnCreate\\(\\)\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:generateSlugOnUpdate\\(\\)\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getType\\(\\)\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:sluggable\\(\\)\\.$#"
			count: 2
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\:\\:disableCache\\(\\)\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\:\\:view\\(\\)\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Cannot call method isForceDeleting\\(\\) on class\\-string\\|object\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\morphToMany\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Amscore\\\\Metable\\\\Meta\\> does not accept Amscore\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:addOneTerm\\(\\) with return type void returns Illuminate\\\\Database\\\\Eloquent\\\\Model but should not return anything\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:addTaxonomies\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Collection but returns \\$this\\(Modules\\\\Post\\\\Entities\\\\Post\\)\\.$#"
			count: 2
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:cleanAmscore\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:cleanAmscore\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:cleanAmscore\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Amscore\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:getTermsAttribute\\(\\) should return array but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), mixed\\>\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$params$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$query$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$terms$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Parameter \\$expires_at of method Modules\\\\Post\\\\Entities\\\\Post\\:\\:addOneTerm\\(\\) has invalid type Modules\\\\Taxonomy\\\\Traits\\\\Carbon\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Result of method Modules\\\\Post\\\\Entities\\\\Post\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Variable \\$taxonomies in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Http\\\\Controllers\\\\PostController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Post/Http/Controllers/PostController.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$social_column\\.$#"
			count: 1
			path: Modules/Publisher/Actions/FeedJobToCatalogFacebook.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$social_column\\.$#"
			count: 1
			path: Modules/Publisher/Actions/FeedJobToFacebook.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$social_column\\.$#"
			count: 1
			path: Modules/Publisher/Actions/FeedJobToGoogleDataFeed.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$social_column\\.$#"
			count: 1
			path: Modules/Publisher/Actions/PostJobToGoogleBusiness.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$social_post_content\\.$#"
			count: 2
			path: Modules/Publisher/Actions/RemoveFacebookCatalog.php

		-
			message: "#^Call to an undefined method Modules\\\\Publisher\\\\Services\\\\Contracts\\\\GoogleClientInterface\\:\\:getDataFeedList\\(\\)\\.$#"
			count: 1
			path: Modules/Publisher/Actions/RemoveFacebookCatalog.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$social_post_content\\.$#"
			count: 2
			path: Modules/Publisher/Actions/RemoveGoogleDatafeed.php

		-
			message: "#^Result of method Illuminate\\\\Console\\\\Command\\:\\:info\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Publisher/Console/GetGoogleToken.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(BulkSheetInterface\\)\\: Unexpected token \"\\\\n     \", expected variable at offset 92$#"
			count: 1
			path: Modules/Publisher/Events/PublishGoogleSuccess.php

		-
			message: "#^Access to constant VIETNAM_AREA_PROVINCE on an unknown class Modules\\\\Publisher\\\\Http\\\\Controllers\\\\VietnamArea\\.$#"
			count: 1
			path: Modules/Publisher/Http/Controllers/PublisherController.php

		-
			message: "#^Call to an undefined method Modules\\\\Publisher\\\\Services\\\\Contracts\\\\GoogleClientInterface\\:\\:getSheetList\\(\\)\\.$#"
			count: 2
			path: Modules/Publisher/Http/Controllers/PublisherController.php

		-
			message: "#^Method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Modules\\\\Job\\\\Entities\\\\Job\\>\\:\\:whereMeta\\(\\) invoked with 2 parameters, 0\\-1 required\\.$#"
			count: 1
			path: Modules/Publisher/Http/Controllers/PublisherController.php

		-
			message: "#^Method Modules\\\\Publisher\\\\Http\\\\Controllers\\\\PublisherController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Publisher/Http/Controllers/PublisherController.php

		-
			message: "#^Undefined variable\\: \\$keyword$#"
			count: 1
			path: Modules/Publisher/Http/Controllers/PublisherController.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: Modules/Publisher/Http/Controllers/PublisherController.php

		-
			message: "#^Method Modules\\\\Publisher\\\\Jobs\\\\CreateJobXMLFile\\:\\:handle\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Publisher/Jobs/CreateJobXMLFile.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$updateAll$#"
			count: 1
			path: Modules/Publisher/Jobs/CreateJobXMLFile.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$platformFeed$#"
			count: 1
			path: Modules/Publisher/Jobs/FacebookCatalogFeed.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$updateAll$#"
			count: 1
			path: Modules/Publisher/Jobs/FacebookCatalogFeed.php

		-
			message: "#^Call to method getMessage\\(\\) on an unknown class Modules\\\\Publisher\\\\Jobs\\\\GuzzleException\\.$#"
			count: 1
			path: Modules/Publisher/Jobs/FacebookCatalogFeed_Old.php

		-
			message: "#^Call to method getResponse\\(\\) on an unknown class Modules\\\\Publisher\\\\Jobs\\\\GuzzleException\\.$#"
			count: 1
			path: Modules/Publisher/Jobs/FacebookCatalogFeed_Old.php

		-
			message: "#^Class Modules\\\\Publisher\\\\Jobs\\\\GuzzleException not found\\.$#"
			count: 1
			path: Modules/Publisher/Jobs/FacebookCatalogFeed_Old.php

		-
			message: "#^Method Modules\\\\Publisher\\\\Jobs\\\\FacebookCatalogFeed_Old\\:\\:handle\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Publisher/Jobs/FacebookCatalogFeed_Old.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$platformFeed$#"
			count: 1
			path: Modules/Publisher/Jobs/FacebookCatalogFeed_Old.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$updateAll$#"
			count: 1
			path: Modules/Publisher/Jobs/FacebookCatalogFeed_Old.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$platformFeed$#"
			count: 1
			path: Modules/Publisher/Jobs/GoogleAdsFeed.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$updateAll$#"
			count: 1
			path: Modules/Publisher/Jobs/GoogleAdsFeed.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/FacebookCatalogProvider.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$slug_str\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/FacebookJobProvider.php

		-
			message: "#^Call to an undefined method Modules\\\\Publisher\\\\Services\\\\Contracts\\\\GoogleClientInterface\\:\\:createSheet\\(\\)\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/FacebookJobProvider.php

		-
			message: "#^Call to an undefined method Modules\\\\Publisher\\\\Services\\\\Contracts\\\\GoogleClientInterface\\:\\:setContent\\(\\)\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/FacebookJobProvider.php

		-
			message: "#^Variable \\$updateSheetID might not be defined\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/FacebookJobProvider.php

		-
			message: "#^Undefined variable\\: \\$jobList$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/FacebookJobProvider_Old.php

		-
			message: "#^Access to an undefined property Modules\\\\Publisher\\\\Services\\\\Drivers\\\\FeedXML\\\\FeedXMLProvider\\:\\:\\$updateAllJob\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/FeedXMLProvider.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$slug_str\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider.php

		-
			message: "#^Call to an undefined method Modules\\\\Publisher\\\\Services\\\\Contracts\\\\GoogleClientInterface\\:\\:createSheet\\(\\)\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider.php

		-
			message: "#^Call to an undefined method Modules\\\\Publisher\\\\Services\\\\Contracts\\\\GoogleClientInterface\\:\\:setContent\\(\\)\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider.php

		-
			message: "#^Variable \\$updateSheetID might not be defined\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$slug_str\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider_Old.php

		-
			message: "#^Call to an undefined method Modules\\\\Publisher\\\\Services\\\\Contracts\\\\GoogleClientInterface\\:\\:updateDataFeed\\(\\)\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider_Old.php

		-
			message: "#^Call to method setAttributeFieldMappings\\(\\) on an unknown class Modules\\\\Publisher\\\\Services\\\\Drivers\\\\FeedXML\\\\FeedMapping\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider_Old.php

		-
			message: "#^Call to method setAttributeValues\\(\\) on an unknown class Modules\\\\Publisher\\\\Services\\\\Drivers\\\\FeedXML\\\\FeedItem\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider_Old.php

		-
			message: "#^Call to method setFeedId\\(\\) on an unknown class Modules\\\\Publisher\\\\Services\\\\Drivers\\\\FeedXML\\\\FeedItem\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider_Old.php

		-
			message: "#^Call to method setFeedId\\(\\) on an unknown class Modules\\\\Publisher\\\\Services\\\\Drivers\\\\FeedXML\\\\FeedMapping\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider_Old.php

		-
			message: "#^Call to method setPlaceholderType\\(\\) on an unknown class Modules\\\\Publisher\\\\Services\\\\Drivers\\\\FeedXML\\\\FeedMapping\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider_Old.php

		-
			message: "#^Constant this not found\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider_Old.php

		-
			message: "#^Instantiated class Modules\\\\Publisher\\\\Services\\\\Drivers\\\\FeedXML\\\\FeedItem not found\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider_Old.php

		-
			message: "#^Instantiated class Modules\\\\Publisher\\\\Services\\\\Drivers\\\\FeedXML\\\\FeedMapping not found\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider_Old.php

		-
			message: "#^Parameter \\#1 \\$operand of method Google\\\\AdsApi\\\\AdWords\\\\v201809\\\\cm\\\\FeedItemOperation\\:\\:setOperand\\(\\) expects Google\\\\AdsApi\\\\AdWords\\\\v201809\\\\cm\\\\FeedItem, Modules\\\\Publisher\\\\Services\\\\Drivers\\\\FeedXML\\\\FeedItem given\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider_Old.php

		-
			message: "#^Parameter \\#1 \\$operand of method Google\\\\AdsApi\\\\AdWords\\\\v201809\\\\cm\\\\FeedMappingOperation\\:\\:setOperand\\(\\) expects Google\\\\AdsApi\\\\AdWords\\\\v201809\\\\cm\\\\FeedMapping, Modules\\\\Publisher\\\\Services\\\\Drivers\\\\FeedXML\\\\FeedMapping given\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/FeedXML/GoogleDataFeedProvider_Old.php

		-
			message: "#^Call to an undefined method Modules\\\\Publisher\\\\Services\\\\Contracts\\\\GoogleClientInterface\\:\\:postJobToBusiness\\(\\)\\.$#"
			count: 1
			path: Modules/Publisher/Services/Drivers/GoogleBusinessProvider.php

		-
			message: "#^Call to method findOrFail\\(\\) on an unknown class Modules\\\\Publisher\\\\Services\\\\Traits\\\\Google\\\\JobRepositoryInterface\\.$#"
			count: 1
			path: Modules/Publisher/Services/GoogleClientService.php

		-
			message: "#^Call to method getMessage\\(\\) on an unknown class Modules\\\\Publisher\\\\Services\\\\Traits\\\\Google\\\\Exception\\.$#"
			count: 1
			path: Modules/Publisher/Services/GoogleClientService.php

		-
			message: "#^Call to method patch\\(\\) on an unknown class Modules\\\\Publisher\\\\Services\\\\Traits\\\\Google\\\\Client\\.$#"
			count: 1
			path: Modules/Publisher/Services/GoogleClientService.php

		-
			message: "#^Call to method post\\(\\) on an unknown class Modules\\\\Publisher\\\\Services\\\\Traits\\\\Google\\\\Client\\.$#"
			count: 1
			path: Modules/Publisher/Services/GoogleClientService.php

		-
			message: "#^Caught class Modules\\\\Publisher\\\\Services\\\\Traits\\\\Google\\\\Exception not found\\.$#"
			count: 1
			path: Modules/Publisher/Services/GoogleClientService.php

		-
			message: "#^Class Modules\\\\Publisher\\\\Services\\\\Traits\\\\Google\\\\JobRepositoryInterface not found\\.$#"
			count: 1
			path: Modules/Publisher/Services/GoogleClientService.php

		-
			message: "#^Constant fail not found\\.$#"
			count: 1
			path: Modules/Publisher/Services/GoogleClientService.php

		-
			message: "#^Instantiated class Modules\\\\Publisher\\\\Services\\\\Traits\\\\Google\\\\Client not found\\.$#"
			count: 2
			path: Modules/Publisher/Services/GoogleClientService.php

		-
			message: "#^Method Modules\\\\Snooze\\\\Http\\\\Controllers\\\\SnoozeController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Snooze/Http/Controllers/SnoozeController.php

		-
			message: "#^Method Modules\\\\Snooze\\\\Http\\\\Controllers\\\\SnoozeController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Snooze/Http/Controllers/SnoozeController.php

		-
			message: "#^Method Modules\\\\Snooze\\\\Http\\\\Controllers\\\\SnoozeController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Snooze/Http/Controllers/SnoozeController.php

		-
			message: "#^Method Modules\\\\Snooze\\\\Http\\\\Controllers\\\\SnoozeController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Snooze/Http/Controllers/SnoozeController.php

		-
			message: "#^Method Modules\\\\Snooze\\\\Http\\\\Controllers\\\\SnoozeController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Snooze/Http/Controllers/SnoozeController.php

		-
			message: "#^Method Modules\\\\Snooze\\\\Http\\\\Controllers\\\\SnoozeController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Snooze/Http/Controllers/SnoozeController.php

		-
			message: "#^Method Modules\\\\Snooze\\\\Http\\\\Controllers\\\\SnoozeController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Snooze/Http/Controllers/SnoozeController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Snooze/Http/Controllers/SnoozeController.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Package\\:\\:\\$term_id\\.$#"
			count: 1
			path: Modules/Subscription/Console/HandlingIfSubscriptionExpired.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Subscription\\:\\:\\$expires_at\\.$#"
			count: 1
			path: Modules/Subscription/Console/HandlingIfSubscriptionExpired.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Subscription\\:\\:\\$taxonomy_id\\.$#"
			count: 1
			path: Modules/Subscription/Console/HandlingIfSubscriptionExpired.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Feature.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$feature\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Feature.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Feature.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$slug\\.$#"
			count: 2
			path: Modules/Subscription/Entities/Feature.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\belongsTo\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Feature.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Message.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$feature\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Message.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Message.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$slug\\.$#"
			count: 2
			path: Modules/Subscription/Entities/Message.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\belongsTo\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Message.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Package.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$feature\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Package.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Package.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$slug\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Package.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\belongsTo\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Package.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Subscription\\:\\:\\$taxonomy_id\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Subscription.php

		-
			message: "#^Call to an undefined method Modules\\\\Subscription\\\\Entities\\\\Subscription\\:\\:customRelation\\(\\)\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Subscription.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Entities\\\\Subscription\\:\\:term\\(\\) has invalid return type App\\\\Traits\\\\customRelation\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Subscription.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Actions\\\\BatchDeleteTaxonomy\\:\\:handle\\(\\) should return string but returns Amscore\\\\Admin\\\\Actions\\\\Response\\.$#"
			count: 1
			path: Modules/Taxonomy/Actions/BatchDeleteTaxonomy.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$term\\.$#"
			count: 1
			path: Modules/Taxonomy/Actions/UserByTaxonomy.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\Taxonomy\\\\Entities\\\\Terms\\>\\|Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 2
			path: Modules/Taxonomy/Console/CleanTermNotUnique.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: Modules/Taxonomy/Console/CleanTermNotUnique.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Taxonomy/Console/CleanTermNotUnique.php

		-
			message: "#^Argument of an invalid type Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: Modules/Taxonomy/Console/CleanTermNotUnique.php

		-
			message: "#^Called 'pluck' on Laravel collection, but could have been retrieved as a query\\.$#"
			count: 1
			path: Modules/Taxonomy/Console/CleanTermNotUnique.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxable\\:\\:\\$gables_id\\.$#"
			count: 2
			path: Modules/Taxonomy/Console/MergeTerm.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxable\\:\\:\\$gables_type\\.$#"
			count: 2
			path: Modules/Taxonomy/Console/MergeTerm.php

		-
			message: "#^Parameter \\#1 \\$callback of method Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\),Modules\\\\Taxonomy\\\\Entities\\\\Taxable\\>\\:\\:countBy\\(\\) expects \\(callable\\(\\)\\: mixed\\)\\|null, 'gables_id' given\\.$#"
			count: 1
			path: Modules/Taxonomy/Console/MergeTerm.php

		-
			message: "#^Parameter \\#3 \\$default of method Illuminate\\\\Console\\\\Command\\:\\:choice\\(\\) expects string\\|null, int given\\.$#"
			count: 1
			path: Modules/Taxonomy/Console/MergeTerm.php

		-
			message: "#^Result of method Illuminate\\\\Console\\\\Command\\:\\:error\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Taxonomy/Console/MergeTerm.php

		-
			message: "#^Result of method Illuminate\\\\Console\\\\Command\\:\\:info\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Taxonomy/Console/MergeTerm.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Support\\\\Fluent\\:\\:references\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Database/Migrations/2021_11_30_103857_create_terms_translations_table.php

		-
			message: "#^Call to private method withoutGlobalScopes\\(\\) of parent class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\<TRelatedModel of Illuminate\\\\Database\\\\Eloquent\\\\Model,TChildModel of Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxable.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$expires_at\\.$#"
			count: 2
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$meta_description\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$meta_keywords\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$meta_title\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$sort_order\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$taxonomy\\.$#"
			count: 2
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$feature\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\:\\:view\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Call to an undefined method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:isForceDeleting\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\belongsTo\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Amscore\\\\Metable\\\\Meta\\> does not accept Amscore\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Amscore\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Result of method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Terms.php

		-
			message: "#^Access to offset int\\<0, max\\> on an unknown class Modules\\\\Taxonomy\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Collection\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Terms.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:generateSlugOnCreate\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Terms.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:generateSlugOnUpdate\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Terms.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:sluggable\\(\\)\\.$#"
			count: 2
			path: Modules/Taxonomy/Entities/Terms.php

		-
			message: "#^Call to method count\\(\\) on an unknown class Modules\\\\Taxonomy\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Collection\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Terms.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\hasOne\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Terms.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:taxonomies\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany\\<Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\>\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Terms.php

		-
			message: "#^Parameter \\$terms of method Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:prepareTermData\\(\\) has invalid type Modules\\\\Taxonomy\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Collection\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Terms.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Taxonomy/Http/Controllers/TaxonomyController.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Http\\\\Controllers\\\\TaxonomyController\\:\\:\\$term\\.$#"
			count: 2
			path: Modules/Taxonomy/Http/Controllers/TaxonomyController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Displayers\\\\Actions\\:\\:add\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Http/Controllers/TaxonomyController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:taxonomy\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Http/Controllers/TaxonomyController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:withCount\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Http/Controllers/TaxonomyController.php

		-
			message: "#^PHPDoc tag @param for parameter \\$request with type Modules\\\\Taxonomy\\\\Http\\\\Controllers\\\\Illuminate\\\\Http\\\\Request is not subtype of native type Illuminate\\\\Http\\\\Request\\.$#"
			count: 1
			path: Modules/Taxonomy/Http/Controllers/TaxonomyController.php

		-
			message: "#^Parameter \\$request of method Modules\\\\Taxonomy\\\\Http\\\\Controllers\\\\TaxonomyController\\:\\:skills\\(\\) has invalid type Modules\\\\Taxonomy\\\\Http\\\\Controllers\\\\Illuminate\\\\Http\\\\Request\\.$#"
			count: 1
			path: Modules/Taxonomy/Http/Controllers/TaxonomyController.php

		-
			message: "#^Class TaxonomyController not found\\.$#"
			count: 1
			path: Modules/Taxonomy/Routes/web.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:getExpiresAt\\(\\) has invalid return type Carbon\\\\Carbon\\\\Carbon\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:getTaxonomy\\(\\) has invalid return type Modules\\\\Taxonomy\\\\Services\\\\Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:setExpiresAt\\(\\) should return Modules\\\\Taxonomy\\\\Services\\\\Plan but empty return statement found\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Parameter \\#1 \\$time of static method Carbon\\\\Carbon\\:\\:parse\\(\\) expects DateTimeInterface\\|string\\|null, \\(int\\|false\\) given\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Property Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:\\$expires_at \\(Carbon\\\\Carbon\\\\Carbon\\|null\\) does not accept Carbon\\\\Carbon\\.$#"
			count: 4
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Property Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:\\$expires_at has unknown class Carbon\\\\Carbon\\\\Carbon as its type\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Property Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:\\$taxonomy \\(Modules\\\\Taxonomy\\\\Services\\\\Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\) does not accept Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\.$#"
			count: 2
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Property Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:\\$taxonomy has unknown class Modules\\\\Taxonomy\\\\Services\\\\Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy as its type\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Call to method getMessage\\(\\) on an unknown class Modules\\\\Taxonomy\\\\Services\\\\Exception\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/TaxonomyService.php

		-
			message: "#^Caught class Modules\\\\Taxonomy\\\\Services\\\\Exception not found\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/TaxonomyService.php

		-
			message: "#^Result of \\|\\| is always true\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/TaxonomyService.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/TaxonomyService.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyCollection.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyCollection.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\)\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Call to an undefined method \\$this\\(Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\)\\:\\:getKey\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:markAccountAsApproved\\(\\)\\.$#"
			count: 1
			path: Modules/User/Actions/ApprovalAccount.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:unMarkAccountAsApproved\\(\\)\\.$#"
			count: 1
			path: Modules/User/Actions/DisapprovalAccount.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:restore\\(\\)\\.$#"
			count: 1
			path: Modules/User/Actions/RestoreUser.php

		-
			message: "#^Call to an undefined method Modules\\\\User\\\\Actions\\\\UploadFileCV\\:\\:multipleFile\\(\\)\\.$#"
			count: 1
			path: Modules/User/Actions/UploadFileCV.php

		-
			message: "#^Variable \\$path_files might not be defined\\.$#"
			count: 1
			path: Modules/User/Actions/UploadFileCV.php

		-
			message: "#^Result of method Illuminate\\\\Console\\\\Command\\:\\:info\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/User/Console/ExportDataMeJobLikeThis.php

		-
			message: "#^Result of method Illuminate\\\\Console\\\\Command\\:\\:info\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/User/Console/ExportEmployeesForCs.php

		-
			message: "#^Result of method Illuminate\\\\Console\\\\Command\\:\\:info\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/User/Console/GoogleSheetExportEmployer.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$tagArray\\.$#"
			count: 1
			path: Modules/User/Console/JobAnnouncement.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:sendMailJobAnnouncement\\(\\) invoked with 0 parameters, 1 required\\.$#"
			count: 1
			path: Modules/User/Console/JobAnnouncement.php

		-
			message: "#^Static method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:with\\(\\) invoked with 0 parameters, at least 1 required\\.$#"
			count: 1
			path: Modules/User/Console/JobAnnouncement.php

		-
			message: "#^Undefined variable\\: \\$courses$#"
			count: 1
			path: Modules/User/Console/JobAnnouncement.php

		-
			message: "#^Call to function is_null\\(\\) with Modules\\\\User\\\\Entities\\\\User will always evaluate to false\\.$#"
			count: 1
			path: Modules/User/Console/RefreshUser.php

		-
			message: "#^Method Modules\\\\User\\\\Console\\\\RefreshUser\\:\\:refreshUser\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/User/Console/RefreshUser.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/User/Console/SynsCbuilderUser.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\User\\\\Entities\\\\Announcement\\)\\:\\:\\$frequency\\.$#"
			count: 1
			path: Modules/User/Entities/Announcement.php

		-
			message: "#^Method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\:\\:orwhereRaw\\(\\) invoked with 3 parameters, 1\\-2 required\\.$#"
			count: 1
			path: Modules/User/Entities/Announcement.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\Announcement\\:\\:scopeAlreadyReceiveAnnouncement\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/Announcement.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\Announcement\\:\\:scopeAlreadyReceiveAnnouncement\\(\\) should return Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\.$#"
			count: 1
			path: Modules/User/Entities/Announcement.php

		-
			message: "#^Access to undefined constant Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:APPROVED_AT\\.$#"
			count: 1
			path: Modules/User/Entities/Scopes/ApprovedScope.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Support\\\\HigherOrderCollectionProxy\\<string, Modules\\\\Job\\\\Entities\\\\Job\\>\\:\\:\\$skills_id\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$image_logo_url\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\Firebase\\\\Entities\\\\FcmDeviceTokenActivity\\:\\:\\$action\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$blacklistCompanies\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$extra_skills\\.$#"
			count: 3
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$files_cv\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$files_cvbuilder\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$followedCompanies\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$followedJobs\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$resume_ids\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$skills_id\\.$#"
			count: 3
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$skills_name\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$skype\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$source\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$status_display\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$willing_to_work\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$years_of_exp\\.$#"
			count: 3
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$district_id\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$latitude\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$longitude\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$postal_code\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$province_id\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$ward_id\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Anonymous function has an unused use \\$candidate\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:addresses\\(\\)\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\:\\:disableCache\\(\\)\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^Call to an undefined method object\\:\\:isDirty\\(\\)\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Cannot call method isForceDeleting\\(\\) on class\\-string\\|object\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\morphToMany\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\Meta\\\\Entities\\\\Meta\\> does not accept Amscore\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:addOneTerm\\(\\) with return type void returns Illuminate\\\\Database\\\\Eloquent\\\\Model but should not return anything\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:addTaxonomies\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Collection but returns \\$this\\(Modules\\\\User\\\\Entities\\\\User\\)\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:appliedJobs\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany\\<Modules\\\\Job\\\\Entities\\\\Job\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:attachRelations\\(\\) should return array but returns false\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:authoredJobs\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany\\<Modules\\\\Job\\\\Entities\\\\Job\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:blacklistCompanies\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:candidates\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany\\<Modules\\\\Job\\\\Entities\\\\Candidate\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:cleanAmscore\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:cleanAmscore\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:cleanAmscore\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:followedCompanies\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:followedJobs\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getAvatarAttribute\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\url\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getAvatarAttribute\\(\\) should return Modules\\\\User\\\\Entities\\\\url but returns array\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getCompanyForVerification\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Traits\\\\Model\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getCompanyForVerification\\(\\) should return Modules\\\\User\\\\Entities\\\\Traits\\\\Model but returns Modules\\\\Company\\\\Entities\\\\Company\\|null\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getListIdCompaniesAttribute\\(\\) should return string but returns array\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Modules\\\\Meta\\\\Entities\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getReadyToApplyAttribute\\(\\) should return string but return statement is missing\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getTermsAttribute\\(\\) should return array but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), mixed\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:newestCandidate\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<Modules\\\\Job\\\\Entities\\\\Candidate\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:notificationFollowings\\(\\) has invalid return type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:notificationFollowings\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeApproved\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeApproved\\(\\) should return Modules\\\\User\\\\Entities\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeDoesntHaveApply\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeDoesntHaveApply\\(\\) should return Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeEmployers\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeEmployers\\(\\) should return Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeHaveApply\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeHaveApply\\(\\) should return Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeQuickSearch\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeQuickSearch\\(\\) should return Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeResumes\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeResumes\\(\\) should return Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:setAvatarAttribute\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$jobs$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$params$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$query$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$terms$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 4
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 4
			path: Modules/User/Entities/User.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Parameter \\#3 \\$targets of class Overtrue\\\\LaravelFollow\\\\Events\\\\RelationAttached constructor expects array\\|int, stdClass given\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Parameter \\$expires_at of method Modules\\\\User\\\\Entities\\\\User\\:\\:addOneTerm\\(\\) has invalid type Modules\\\\Taxonomy\\\\Traits\\\\Carbon\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$arrayValues \\(array\\) on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Result of method Modules\\\\User\\\\Entities\\\\User\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Return type \\(Modules\\\\User\\\\Entities\\\\Traits\\\\Model\\) of method Modules\\\\User\\\\Entities\\\\User\\:\\:getCompanyForVerification\\(\\) should be compatible with return type \\(string\\) of method Modules\\\\User\\\\Entities\\\\Contracts\\\\MustApproveAccount\\:\\:getCompanyForVerification\\(\\)$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Strict comparison using \\=\\=\\= between false and array\\|null will always evaluate to false\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 6
			path: Modules/User/Entities/User.php

		-
			message: "#^Variable \\$taxonomies in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Variable \\$update in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Events\\\\MeJobLikeThisActive\\:\\:user\\(\\) has invalid return type Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisActive.php

		-
			message: "#^Method Modules\\\\User\\\\Events\\\\MeJobLikeThisActive\\:\\:user\\(\\) should return Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job but returns Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\Announcement\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisActive.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\MeJobLikeThisActive\\:\\:\\$user \\(Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\Announcement\\) does not accept Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisActive.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\MeJobLikeThisActive\\:\\:\\$user has unknown class Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\Announcement as its type\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisActive.php

		-
			message: "#^Method Modules\\\\User\\\\Events\\\\MeJobLikeThisProcessed\\:\\:announcement\\(\\) has invalid return type Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisProcessed.php

		-
			message: "#^Method Modules\\\\User\\\\Events\\\\MeJobLikeThisProcessed\\:\\:announcement\\(\\) should return Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job but returns Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\Announcement\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisProcessed.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\MeJobLikeThisProcessed\\:\\:\\$announcement \\(Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\Announcement\\) does not accept Modules\\\\User\\\\Entities\\\\Announcement\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisProcessed.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\MeJobLikeThisProcessed\\:\\:\\$announcement has unknown class Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\Announcement as its type\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisProcessed.php

		-
			message: "#^Method Modules\\\\User\\\\Events\\\\NewUserProcessed\\:\\:user\\(\\) has invalid return type Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Events/NewUserProcessed.php

		-
			message: "#^Method Modules\\\\User\\\\Events\\\\NewUserProcessed\\:\\:user\\(\\) should return Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job but returns Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/User/Events/NewUserProcessed.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\NewUserProcessed\\:\\:\\$user \\(Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\) does not accept Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/User/Events/NewUserProcessed.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\NewUserProcessed\\:\\:\\$user has unknown class Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User as its type\\.$#"
			count: 1
			path: Modules/User/Events/NewUserProcessed.php

		-
			message: "#^PHPDoc tag @param for parameter \\$candidate with type Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User is not subtype of native type Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/User/Events/SendEmailConfirmSuccessfullHackerank.php

		-
			message: "#^Parameter \\$candidate of method Modules\\\\User\\\\Events\\\\SendEmailConfirmSuccessfullHackerank\\:\\:__construct\\(\\) has invalid type Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/User/Events/SendEmailConfirmSuccessfullHackerank.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\SendEmailConfirmSuccessfullHackerank\\:\\:\\$candidate \\(Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Candidate\\) does not accept Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/User/Events/SendEmailConfirmSuccessfullHackerank.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\SendEmailConfirmSuccessfullHackerank\\:\\:\\$candidate has unknown class Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Candidate as its type\\.$#"
			count: 1
			path: Modules/User/Events/SendEmailConfirmSuccessfullHackerank.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\UserHasRecentlyUpdateProfile\\:\\:\\$user \\(Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\) does not accept Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/User/Events/UserHasRecentlyUpdateProfile.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\UserHasRecentlyUpdateProfile\\:\\:\\$user has unknown class Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User as its type\\.$#"
			count: 1
			path: Modules/User/Events/UserHasRecentlyUpdateProfile.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany\\<Modules\\\\User\\\\Entities\\\\Announcement\\>\\:\\:withTrashed\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/AnnouncementController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\EmployerController\\:\\:show\\(\\) has invalid return type Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\CompanyResource\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/EmployerController.php

		-
			message: "#^Instantiated class Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\DeleteUserProcess not found\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\UserController\\:\\:destroy\\(\\) has invalid return type Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\Response\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\UserController\\:\\:destroy\\(\\) should return Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\Response but returns Redmix0901\\\\Core\\\\Http\\\\Responses\\\\BaseHttpResponse\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\UserController\\:\\:show\\(\\) has invalid return type Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\Response\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\UserController\\:\\:store\\(\\) has invalid return type Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\Response\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\UserController\\:\\:store\\(\\) should return Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\Response but returns Modules\\\\User\\\\Transformers\\\\UserResource\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserController.php

		-
			message: "#^PHPDoc tag @param for parameter \\$request with type Illuminate\\\\Http\\\\Request is not subtype of native type Modules\\\\User\\\\Http\\\\Requests\\\\StoreUserRequest\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserController.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$id$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserController.php

		-
			message: "#^Variable \\$applied_jobs might not be defined\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserController.php

		-
			message: "#^Variable \\$attached_resume might not be defined\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserController.php

		-
			message: "#^Variable \\$followed_jobs might not be defined\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserController.php

		-
			message: "#^Variable \\$latest_attachment might not be defined\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserController.php

		-
			message: "#^Call to method user\\(\\) on an unknown class Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\Requests\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserProgressController.php

		-
			message: "#^Call to static method make\\(\\) on an unknown class Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\UserReource\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserProgressController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\UserProgressController\\:\\:appliedJobs\\(\\) should return Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserProgressController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\UserProgressController\\:\\:followedJobs\\(\\) should return Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserProgressController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\UserProgressController\\:\\:job\\(\\) should return Modules\\\\Job\\\\Transformers\\\\JobResource but returns Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserProgressController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\UserProgressController\\:\\:myJobs\\(\\) should return Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserProgressController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\UserProgressController\\:\\:viewedJobs\\(\\) should return Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserProgressController.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$keyword$#"
			count: 3
			path: Modules/User/Http/Controllers/API/UserProgressController.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$page$#"
			count: 3
			path: Modules/User/Http/Controllers/API/UserProgressController.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$page_size$#"
			count: 3
			path: Modules/User/Http/Controllers/API/UserProgressController.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$status$#"
			count: 3
			path: Modules/User/Http/Controllers/API/UserProgressController.php

		-
			message: "#^Parameter \\$request of method Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\UserProgressController\\:\\:me\\(\\) has invalid type Modules\\\\User\\\\Http\\\\Controllers\\\\API\\\\Requests\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/API/UserProgressController.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\Company\\\\Entities\\\\Company\\>\\|Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$approved_at\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$audits\\.$#"
			count: 5
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$birthday\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$company\\.$#"
			count: 12
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$email_verified_at\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$firstname\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$gender\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$lastname\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$phone\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$position\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$type\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\EmployerController\\:\\:\\$username\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:employers\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:orderBy\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Method Amscore\\\\Admin\\\\Grid\\\\Column\\:\\:modal\\(\\) invoked with 2 parameters, 0\\-1 required\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/EmployerController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$recalled_at\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Candidate\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$extra_skills_name\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$full_addresses\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$gender\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$input\\.$#"
			count: 7
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$ready_to_apply\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$skills_name\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$skype\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$source\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$utm_medium\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$utm_source\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:\\$years_of_exp_upgraded\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$job\\.$#"
			count: 2
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Anonymous function has an unused use \\$class\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Anonymous function has an unused use \\$model\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Anonymous function has an unused use \\$taxonomies\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Anonymous function has an unused use \\$userRepository\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:orderBy\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:resumes\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Call to an undefined method App\\\\Contracts\\\\Form\\:\\:phoneValidateUnique\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:setMetaAttribute\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:setTermsAttribute\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Call to an undefined method object\\:\\:isRecalled\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Method Amscore\\\\Admin\\\\Grid\\\\Column\\:\\:modal\\(\\) invoked with 2 parameters, 0\\-1 required\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\:\\:detail\\(\\) should return Amscore\\\\Admin\\\\Show but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 2
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Parameter \\#1 \\$tool of method Amscore\\\\Admin\\\\Grid\\\\Tools\\:\\:append\\(\\) expects Amscore\\\\Admin\\\\Grid\\\\Tools\\\\AbstractTool\\|string, Modules\\\\User\\\\Actions\\\\UploadFileCV given\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Parameter \\#4 \\$row of class Amscore\\\\Admin\\\\Grid\\\\Displayers\\\\Label constructor expects stdClass, \\$this\\(Modules\\\\User\\\\Http\\\\Controllers\\\\ResumeController\\) given\\.$#"
			count: 2
			path: Modules/User/Http/Controllers/ResumeController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$approved_at\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$email_verified_at\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$extra_skills_name\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$full_addresses\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$gender\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$input\\.$#"
			count: 10
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$phone\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$row\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$skills_name\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$sub_skill\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$type\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$username\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:\\$years_of_exp\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:orderBy\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:withTrashed\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Call to an undefined method Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:trashed\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Method Amscore\\\\Admin\\\\Grid\\\\Column\\:\\:modal\\(\\) invoked with 2 parameters, 0\\-1 required\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:addNotes\\(\\) has invalid return type Modules\\\\User\\\\Http\\\\Controllers\\\\Response\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:addNotes\\(\\) should return Modules\\\\User\\\\Http\\\\Controllers\\\\Response but returns Redmix0901\\\\Core\\\\Http\\\\Responses\\\\BaseHttpResponse\\.$#"
			count: 2
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:notes\\(\\) has invalid return type Modules\\\\User\\\\Http\\\\Controllers\\\\Response\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:notes\\(\\) should return Modules\\\\User\\\\Http\\\\Controllers\\\\Response but returns Illuminate\\\\Http\\\\JsonResponse\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:notes\\(\\) should return Modules\\\\User\\\\Http\\\\Controllers\\\\Response but returns Modules\\\\User\\\\Transformers\\\\NoteCollection\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Parameter \\#4 \\$row of class Amscore\\\\Admin\\\\Grid\\\\Displayers\\\\Label constructor expects stdClass, \\$this\\(Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\) given\\.$#"
			count: 2
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Middleware\\\\EnsureAccountApproved\\:\\:handle\\(\\) should return Illuminate\\\\Http\\\\RedirectResponse\\|Illuminate\\\\Http\\\\Response but returns Illuminate\\\\Http\\\\JsonResponse\\|void\\.$#"
			count: 1
			path: Modules/User/Http/Middleware/EnsureAccountApproved.php

		-
			message: "#^Result of function abort \\(void\\) is used\\.$#"
			count: 1
			path: Modules/User/Http/Middleware/EnsureAccountApproved.php

		-
			message: "#^Method Modules\\\\User\\\\Jobs\\\\CreateUserProcess\\:\\:handle\\(\\) with return type void returns Modules\\\\User\\\\Entities\\\\User but should not return anything\\.$#"
			count: 1
			path: Modules/User/Jobs/CreateUserProcess.php

		-
			message: "#^Parameter \\$exception of method Modules\\\\User\\\\Jobs\\\\CreateUserProcess\\:\\:failed\\(\\) has invalid type Modules\\\\User\\\\Jobs\\\\Exception\\.$#"
			count: 2
			path: Modules/User/Jobs/CreateUserProcess.php

		-
			message: "#^Call to method provider\\(\\) on an unknown class Modules\\\\File\\\\Services\\\\DetectNewFile\\.$#"
			count: 1
			path: Modules/User/Jobs/DetectFileProcess.php

		-
			message: "#^Class Modules\\\\File\\\\Services\\\\DetectNewFile not found\\.$#"
			count: 1
			path: Modules/User/Jobs/DetectFileProcess.php

		-
			message: "#^Method Modules\\\\User\\\\Jobs\\\\DetectFileProcess\\:\\:getEmail\\(\\) with return type void returns array\\<int, mixed\\>\\|null but should not return anything\\.$#"
			count: 1
			path: Modules/User/Jobs/DetectFileProcess.php

		-
			message: "#^Method Modules\\\\User\\\\Jobs\\\\DetectFileProcess\\:\\:getPhone\\(\\) with return type void returns array\\<int, string\\> but should not return anything\\.$#"
			count: 1
			path: Modules/User/Jobs/DetectFileProcess.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$content\\)\\: Unexpected token \"\\[\", expected type at offset 54$#"
			count: 3
			path: Modules/User/Jobs/DetectFileProcess.php

		-
			message: "#^Result of method Modules\\\\User\\\\Jobs\\\\DetectFileProcess\\:\\:getEmail\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/User/Jobs/DetectFileProcess.php

		-
			message: "#^Result of method Modules\\\\User\\\\Jobs\\\\DetectFileProcess\\:\\:getPhone\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/User/Jobs/DetectFileProcess.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Overtrue\\\\LaravelFollow\\\\Events\\\\RelationAttached\\.$#"
			count: 1
			path: Modules/User/Listeners/FollowedJob.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$jobs\\.$#"
			count: 2
			path: Modules/User/Listeners/JustClickApplyJob.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$user\\.$#"
			count: 2
			path: Modules/User/Listeners/JustClickApplyJob.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Activity\\\\Events\\\\ActivityCreated\\.$#"
			count: 1
			path: Modules/User/Listeners/JustClickApplyJob.php

		-
			message: "#^Access to property \\$users on an unknown class Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Listeners/MeJobLikeThis.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\User\\\\Events\\\\MeJobLikeThisProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/MeJobLikeThis.php

		-
			message: "#^Call to method load\\(\\) on an unknown class Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Listeners/MeJobLikeThisActiveListener.php

		-
			message: "#^Call to method count\\(\\) on an unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 2
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^Call to method first\\(\\) on an unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 4
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^Call to method isNotEmpty\\(\\) on an unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^Call to method pluck\\(\\) on an unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 2
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type Modules\\\\User\\\\Events\\\\NewApplyProcessed is not subtype of native type Modules\\\\Job\\\\Events\\\\NewApplyProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Job\\\\Events\\\\NewApplyProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^Parameter \\$event of method Modules\\\\User\\\\Listeners\\\\NewApply\\:\\:shouldQueue\\(\\) has invalid type Modules\\\\User\\\\Events\\\\NewApplyProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\UserProfile\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/User/Listeners/ParseUserProfileCvUpload.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type Modules\\\\User\\\\Listeners\\\\NewUserProcessed is not subtype of native type Modules\\\\User\\\\Events\\\\PushCreateCv\\.$#"
			count: 1
			path: Modules/User/Listeners/PushCreateCvListener.php

		-
			message: "#^Parameter \\$event of method Modules\\\\User\\\\Listeners\\\\PushCreateCvListener\\:\\:handle\\(\\) has invalid type Modules\\\\User\\\\Listeners\\\\NewUserProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/PushCreateCvListener.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type Modules\\\\User\\\\Listeners\\\\NewUserProcessed is not subtype of native type Modules\\\\User\\\\Events\\\\PushUpdateUserProfile\\.$#"
			count: 1
			path: Modules/User/Listeners/PushUpdateProfileListener.php

		-
			message: "#^Parameter \\$event of method Modules\\\\User\\\\Listeners\\\\PushUpdateProfileListener\\:\\:handle\\(\\) has invalid type Modules\\\\User\\\\Listeners\\\\NewUserProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/PushUpdateProfileListener.php

		-
			message: "#^Call to method first\\(\\) on an unknown class Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 2
			path: Modules/User/Listeners/UpdateUtmForUser.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Job\\\\Events\\\\NewApplyProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/UpdateUtmForUser.php

		-
			message: "#^Right side of \\|\\| is always false\\.$#"
			count: 1
			path: Modules/User/Listeners/UpdateUtmForUser.php

		-
			message: "#^Call to method hasApprovedAccount\\(\\) on an unknown class Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Listeners/WelcomeToTopdevListener.php

		-
			message: "#^Call to method isEmployer\\(\\) on an unknown class Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Listeners/WelcomeToTopdevListener.php

		-
			message: "#^Call to method sendEmailVerification\\(\\) on an unknown class Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Listeners/WelcomeToTopdevListener.php

		-
			message: "#^Call to method sendNotifyToAdministrator\\(\\) on an unknown class Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Listeners/WelcomeToTopdevListener.php

		-
			message: "#^Call to method sendWelcomeToUser\\(\\) on an unknown class Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Listeners/WelcomeToTopdevListener.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\User\\\\Events\\\\NewUserProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/WelcomeToTopdevListener.php

		-
			message: "#^Call to method map\\(\\) on an unknown class App\\\\Collection\\\\Collection\\.$#"
			count: 1
			path: Modules/User/Notifications/AnnouncementNotification.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: Modules/User/Notifications/AutoConfirmApplyNotification.php

		-
			message: "#^Method Modules\\\\User\\\\Notifications\\\\AutoConfirmApplyNotification\\:\\:setDataBody\\(\\) invoked with 1 parameter, 0 required\\.$#"
			count: 1
			path: Modules/User/Notifications/AutoConfirmApplyNotification.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$link$#"
			count: 1
			path: Modules/User/Notifications/AutoConfirmApplyNotification.php

		-
			message: "#^Call to method map\\(\\) on an unknown class App\\\\Collection\\\\Collection\\.$#"
			count: 1
			path: Modules/User/Notifications/EmailWeeklyConfirmNotification.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/User/Notifications/HandleAfterEasyApplyNotification.php

		-
			message: "#^Call to method map\\(\\) on an unknown class App\\\\Collection\\\\Collection\\.$#"
			count: 1
			path: Modules/User/Notifications/HandleAfterEasyApplyNotification.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$link$#"
			count: 1
			path: Modules/User/Notifications/HandleAfterEasyApplyNotification.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$display_name\\.$#"
			count: 2
			path: Modules/User/Notifications/JobNewOfCompanyFollowedNotification.php

		-
			message: "#^Call to method map\\(\\) on an unknown class App\\\\Collection\\\\Collection\\.$#"
			count: 1
			path: Modules/User/Notifications/JobNewOfCompanyFollowedNotification.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$link$#"
			count: 1
			path: Modules/User/Notifications/JobNewOfCompanyFollowedNotification.php

		-
			message: "#^Method Modules\\\\User\\\\Notifications\\\\NotifyJobAnnouncement\\:\\:bodyMail\\(\\) has invalid return type Modules\\\\User\\\\Notifications\\\\html\\.$#"
			count: 1
			path: Modules/User/Notifications/NotifyJobAnnouncement.php

		-
			message: "#^Method Modules\\\\User\\\\Notifications\\\\NotifyJobAnnouncement\\:\\:bodyMail\\(\\) should return Modules\\\\User\\\\Notifications\\\\html but returns array\\|string\\.$#"
			count: 1
			path: Modules/User/Notifications/NotifyJobAnnouncement.php

		-
			message: "#^Call to method map\\(\\) on an unknown class App\\\\Collection\\\\Collection\\.$#"
			count: 1
			path: Modules/User/Notifications/PushApplyJobFollowedNotification.php

		-
			message: "#^Constructor of class Modules\\\\User\\\\Notifications\\\\PushApplyJobFollowedNotification has an unused parameter \\$delay\\.$#"
			count: 1
			path: Modules/User/Notifications/PushApplyJobFollowedNotification.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$link$#"
			count: 1
			path: Modules/User/Notifications/PushApplyJobFollowedNotification.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$link$#"
			count: 1
			path: Modules/User/Notifications/PushCompleteCvNotification.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$link$#"
			count: 1
			path: Modules/User/Notifications/PushCreateCvNotification.php

		-
			message: "#^Method Modules\\\\User\\\\Notifications\\\\SendEmailHackerRankReportUser\\:\\:bodyMail\\(\\) has invalid return type Modules\\\\User\\\\Notifications\\\\html\\.$#"
			count: 1
			path: Modules/User/Notifications/SendEmailHackerRankReportUser.php

		-
			message: "#^Method Modules\\\\User\\\\Notifications\\\\SendEmailHackerRankReportUser\\:\\:bodyMail\\(\\) should return Modules\\\\User\\\\Notifications\\\\html but returns string\\|false\\.$#"
			count: 1
			path: Modules/User/Notifications/SendEmailHackerRankReportUser.php

		-
			message: "#^Method Modules\\\\User\\\\Notifications\\\\SendEmailHackerRankReportUser\\:\\:toEmail\\(\\) has invalid return type Modules\\\\User\\\\Notifications\\\\email\\.$#"
			count: 1
			path: Modules/User/Notifications/SendEmailHackerRankReportUser.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$notifiable\\)\\: Unexpected token \"\\[\", expected type at offset 25$#"
			count: 3
			path: Modules/User/Notifications/SendEmailHackerRankReportUser.php

		-
			message: "#^Method Modules\\\\User\\\\Notifications\\\\VerifyEmailNotification\\:\\:bodyMail\\(\\) has invalid return type Modules\\\\User\\\\Notifications\\\\html\\.$#"
			count: 1
			path: Modules/User/Notifications/VerifyEmailNotification.php

		-
			message: "#^Method Modules\\\\User\\\\Notifications\\\\VerifyEmailNotification\\:\\:bodyMail\\(\\) should return Modules\\\\User\\\\Notifications\\\\html but returns string\\|false\\.$#"
			count: 1
			path: Modules/User/Notifications/VerifyEmailNotification.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$notifiable\\)\\: Unexpected token \"\\[\", expected type at offset 25$#"
			count: 2
			path: Modules/User/Notifications/VerifyEmailNotification.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/User/Repositories/Eloquents/CvBuilderEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/User/Repositories/Eloquents/CvBuilderEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/User/Repositories/Eloquents/UserEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/User/Repositories/Eloquents/UserEloquentRepository.php

		-
			message: "#^Class EmployerController not found\\.$#"
			count: 1
			path: Modules/User/Routes/web.php

		-
			message: "#^Class ResumeController not found\\.$#"
			count: 3
			path: Modules/User/Routes/web.php

		-
			message: "#^Class UserController not found\\.$#"
			count: 1
			path: Modules/User/Routes/web.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Modules\\\\User\\\\Entities\\\\MyResume\\>\\|Modules\\\\User\\\\Entities\\\\MyResume\\:\\:\\$user_id\\.$#"
			count: 1
			path: Modules/User/Services/ConvertCompletedCvToUserProfile.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/User/Services/ConvertCompletedCvToUserProfile.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$ttid\\.$#"
			count: 1
			path: Modules/User/Services/ConvertCompletedCvToUserProfile.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/User/Services/ConvertResumeToUserProfile.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$ttid\\.$#"
			count: 1
			path: Modules/User/Services/ConvertResumeToUserProfile.php

		-
			message: "#^Variable \\$education in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/User/Services/ConvertResumeToUserProfile.php

		-
			message: "#^PHPDoc tag @var contains unknown class Illuminate\\\\Http\\\\Client\\\\Response\\.$#"
			count: 1
			path: Modules/User/Services/ParseCv.php

		-
			message: "#^Parameter \\#1 \\$array of static method Illuminate\\\\Support\\\\Arr\\:\\:get\\(\\) expects array\\|ArrayAccess, Illuminate\\\\Http\\\\Client\\\\Response given\\.$#"
			count: 5
			path: Modules/User/Services/ParseCv.php

		-
			message: "#^Property Modules\\\\User\\\\Sheets\\\\EmployeesDataSheetForCs\\:\\:\\$dataAudits \\(Illuminate\\\\Database\\\\Eloquent\\\\Collection\\|null\\) does not accept Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), OwenIt\\\\Auditing\\\\Models\\\\Audit\\>\\>\\.$#"
			count: 1
			path: Modules/User/Sheets/EmployeesDataSheetForCs.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$addresses\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$avatar_url\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$birthday\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$changeable_password\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$company_id\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$cover_letter\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$educations\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$experiences\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$full_name\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$gender\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$phone\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$recent_position\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$social_networks\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$sort_name\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$username\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$willing_to_work\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Call to method aggregations\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeCollection.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeCollection.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeCollection.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeCollection.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\User\\\\Transformers\\\\EmployeeCollection\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeCollection.php

		-
			message: "#^Method Modules\\\\User\\\\Transformers\\\\EmployeeCollection\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeCollection.php

		-
			message: "#^Method Modules\\\\User\\\\Transformers\\\\EmployeeCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeCollection.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/User/Transformers/EmployeeCollection.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/User/Transformers/EmployeeCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployeeResource\\:\\:\\$approved_at\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployeeResource\\:\\:\\$company_id\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployeeResource\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployeeResource\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployeeResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployeeResource\\:\\:\\$lastname\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployeeResource\\:\\:\\$phone\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployeeResource\\:\\:\\$position\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployeeResource\\:\\:\\$username\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\User\\\\Transformers\\\\EmployeeResource\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Method Modules\\\\User\\\\Transformers\\\\EmployeeResource\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 106$#"
			count: 1
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/User/Transformers/EmployeeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\GetUserProfileResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/User/Transformers/GetUserProfileResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\GetUserProfileResource\\:\\:\\$province\\.$#"
			count: 1
			path: Modules/User/Transformers/GetUserProfileResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\GetUserProfileResource\\:\\:\\$user\\.$#"
			count: 1
			path: Modules/User/Transformers/GetUserProfileResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\GetUserProfileResource\\:\\:\\$years_of_exp\\.$#"
			count: 1
			path: Modules/User/Transformers/GetUserProfileResource.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 2
			path: Modules/User/Transformers/GetUserProfileResource.php

		-
			message: "#^Method Modules\\\\User\\\\Transformers\\\\NoteCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/NoteCollection.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 106$#"
			count: 1
			path: Modules/User/Transformers/NoteCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\NoteResource\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/User/Transformers/NoteResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\NoteResource\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/User/Transformers/NoteResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\NoteResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/User/Transformers/NoteResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/User/Transformers/NoteResource.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeCollection.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeCollection.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeCollection.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\User\\\\Transformers\\\\ResumeCollection\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeCollection.php

		-
			message: "#^Method Modules\\\\User\\\\Transformers\\\\ResumeCollection\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeCollection.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/User/Transformers/ResumeCollection.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 106$#"
			count: 1
			path: Modules/User/Transformers/ResumeCollection.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/User/Transformers/ResumeCollection.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\User\\\\Transformers\\\\ResumeResource\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Method Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/User/Transformers/UserLogResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResource\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResource\\:\\:\\$full_name\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^Call to method all\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^Call to method first\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^Call to method paginate\\(\\) on an unknown class App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^Instanceof between class\\-string\\<static\\(Modules\\\\User\\\\Transformers\\\\UserResource\\)\\> and Illuminate\\\\Http\\\\Resources\\\\Json\\\\ResourceCollection will always evaluate to false\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^Method Modules\\\\User\\\\Transformers\\\\UserResource\\:\\:getElasticCollection\\(\\) has invalid return type App\\\\Traits\\\\App\\\\Collection\\\\ElasticCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(App\\\\Collection\\\\ElasticCollection\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 93$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 4
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$city\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$latitude\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$longitude\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$postal_code\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$state\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^If condition is always false\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Method Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:scopeApplyStatus\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$builder$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$model$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:district\\(\\)\\.$#"
			count: 1
			path: Modules/VietnamArea/Http/Controllers/DistrictController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:province\\(\\)\\.$#"
			count: 1
			path: Modules/VietnamArea/Http/Controllers/ProvinceController.php

		-
			message: "#^Parameter \\#1 \\$data of class Amscore\\\\Admin\\\\Widgets\\\\Form constructor expects array, Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea given\\.$#"
			count: 1
			path: Modules/VietnamArea/Http/Controllers/VietnamAreaController.php

		-
			message: "#^Parameter \\#2 \\$content of class Amscore\\\\Admin\\\\Widgets\\\\Box constructor expects string, Amscore\\\\Admin\\\\Widgets\\\\Form given\\.$#"
			count: 1
			path: Modules/VietnamArea/Http/Controllers/VietnamAreaController.php

		-
			message: "#^Property Modules\\\\VietnamArea\\\\Http\\\\Controllers\\\\VietnamAreaController\\:\\:\\$request is never read, only written\\.$#"
			count: 1
			path: Modules/VietnamArea/Http/Controllers/VietnamAreaController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\:\\:parent\\(\\)\\.$#"
			count: 1
			path: Modules/VietnamArea/Http/Controllers/WardController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:ward\\(\\)\\.$#"
			count: 1
			path: Modules/VietnamArea/Http/Controllers/WardController.php

		-
			message: "#^Class Modules\\\\VietnamArea\\\\Events\\\\CreateProcessed constructor invoked with 2 parameters, 0 required\\.$#"
			count: 1
			path: Modules/VietnamArea/Observers/AddressObserver.php

		-
			message: "#^Class Modules\\\\VietnamArea\\\\Events\\\\ModifiedProcessed constructor invoked with 2 parameters, 0 required\\.$#"
			count: 1
			path: Modules/VietnamArea/Observers/AddressObserver.php

		-
			message: "#^Property Redmix0901\\\\Core\\\\Repositories\\\\Caches\\\\BaseCacheDecorator\\:\\:\\$cache_time \\(Redmix0901\\\\Core\\\\Repositories\\\\Caches\\\\cache_time\\) does not accept int\\.$#"
			count: 1
			path: Modules/VietnamArea/Repositories/Caches/AreaCacheDecorator.php

		-
			message: "#^Class DistrictController not found\\.$#"
			count: 1
			path: Modules/VietnamArea/Routes/web.php

		-
			message: "#^Class ProvinceController not found\\.$#"
			count: 1
			path: Modules/VietnamArea/Routes/web.php

		-
			message: "#^Class VietnamAreaController not found\\.$#"
			count: 1
			path: Modules/VietnamArea/Routes/web.php

		-
			message: "#^Class WardController not found\\.$#"
			count: 1
			path: Modules/VietnamArea/Routes/web.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 106$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:\\$district_id\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:\\$full_address\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:\\$province_id\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:\\$street\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:\\$ward_id\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Call to an undefined method Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:getKey\\(\\)\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset 95$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:createNote\\(\\)\\.$#"
			count: 1
			path: app/Actions/AddNote.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:markAsClose\\(\\)\\.$#"
			count: 1
			path: app/Actions/MarkAsClose.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:markAsOpen\\(\\)\\.$#"
			count: 1
			path: app/Actions/MarkAsOpen.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:republish\\(\\)\\.$#"
			count: 1
			path: app/Actions/Republish.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:restore\\(\\)\\.$#"
			count: 1
			path: app/Actions/Restore.php

		-
			message: "#^Property App\\\\Actions\\\\RowAction\\:\\:\\$target has unknown class string as its type\\.$#"
			count: 1
			path: app/Actions/RowAction.php

		-
			message: "#^Call to an undefined method GuzzleHttp\\\\ClientInterface\\:\\:post\\(\\)\\.$#"
			count: 1
			path: app/Channels/GoogleChannel.php

		-
			message: "#^Method App\\\\Channels\\\\GoogleChannel\\:\\:indexing\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 1
			path: app/Channels/GoogleChannel.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$settings$#"
			count: 1
			path: app/Channels/GoogleChannel.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: app/Channels/HackerRankApi.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: app/Channels/HackerRankApi.php

		-
			message: "#^Method App\\\\Channels\\\\MauticChannel\\:\\:SetSettings\\(\\) with return type void returns array\\<string, mixed\\> but should not return anything\\.$#"
			count: 1
			path: app/Channels/MauticChannel.php

		-
			message: "#^Method App\\\\Channels\\\\MauticChannel\\:\\:_auth\\(\\) with return type void returns Mautic\\\\Auth\\\\AuthInterface but should not return anything\\.$#"
			count: 1
			path: app/Channels/MauticChannel.php

		-
			message: "#^Method App\\\\Channels\\\\MauticChannel\\:\\:api\\(\\) with return type void returns Mautic\\\\Api\\\\Api but should not return anything\\.$#"
			count: 1
			path: app/Channels/MauticChannel.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[array\\] \\$params\\)\\: Unexpected token \"\\]\", expected '\\(' at offset 24$#"
			count: 1
			path: app/Channels/MauticChannel.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$target \\- choose \\(companies\\|contacts\\|emails\\|tags\\|segments\\|campaigns\\|stages\\|\\.\\.\\.\\)\\)\\: Unexpected token \"\\[\", expected type at offset 48$#"
			count: 1
			path: app/Channels/MauticChannel.php

		-
			message: "#^PHPDoc tag @var has invalid value \\(\\[string\\]\\)\\: Unexpected token \"\\[\", expected type at offset 23$#"
			count: 1
			path: app/Channels/MauticChannel.php

		-
			message: "#^Parameter \\#2 \\$auth of method Mautic\\\\MauticApi\\:\\:newApi\\(\\) expects Mautic\\\\Auth\\\\AuthInterface, void given\\.$#"
			count: 1
			path: app/Channels/MauticChannel.php

		-
			message: "#^Property App\\\\Channels\\\\MauticChannel\\:\\:\\$settings \\(array\\) does not accept void\\.$#"
			count: 1
			path: app/Channels/MauticChannel.php

		-
			message: "#^Result of method App\\\\Channels\\\\MauticChannel\\:\\:SetSettings\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: app/Channels/MauticChannel.php

		-
			message: "#^Result of method App\\\\Channels\\\\MauticChannel\\:\\:_auth\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: app/Channels/MauticChannel.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$content$#"
			count: 1
			path: app/Channels/Messages/SendMailMessage.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$content$#"
			count: 1
			path: app/Channels/Messages/TelegramMessages.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Notifications\\\\Notification\\:\\:toSendMail\\(\\)\\.$#"
			count: 1
			path: app/Channels/SendMailChannel.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Notifications\\\\Notification\\:\\:toTelegram\\(\\)\\.$#"
			count: 1
			path: app/Channels/TelegramChannel.php

		-
			message: "#^Method App\\\\Channels\\\\TelegramChannel\\:\\:send\\(\\) with return type void returns array\\<int, mixed\\> but should not return anything\\.$#"
			count: 1
			path: app/Channels/TelegramChannel.php

		-
			message: "#^Undefined variable\\: \\$notifiable$#"
			count: 1
			path: app/Channels/TelegramChannel.php

		-
			message: "#^Undefined variable\\: \\$notification$#"
			count: 1
			path: app/Channels/TelegramChannel.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Notifications\\\\Notification\\:\\:toSendMailSystem\\(\\)\\.$#"
			count: 1
			path: app/Channels/TopDevMailChannel.php

		-
			message: "#^Method App\\\\Collection\\\\ElasticCollection\\:\\:aggregations\\(\\) has invalid return type App\\\\Collection\\\\collect\\.$#"
			count: 1
			path: app/Collection/ElasticCollection.php

		-
			message: "#^Method App\\\\Collection\\\\ElasticCollection\\:\\:aggregations\\(\\) should return App\\\\Collection\\\\collect but returns Illuminate\\\\Support\\\\Collection\\.$#"
			count: 1
			path: app/Collection/ElasticCollection.php

		-
			message: "#^Method App\\\\Collection\\\\ElasticCollection\\:\\:aggregations\\(\\) should return App\\\\Collection\\\\collect but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), mixed\\>\\.$#"
			count: 1
			path: app/Collection/ElasticCollection.php

		-
			message: "#^Method App\\\\Collection\\\\ElasticCollection\\:\\:all\\(\\) has invalid return type App\\\\Collection\\\\Collection\\.$#"
			count: 1
			path: app/Collection/ElasticCollection.php

		-
			message: "#^Method App\\\\Collection\\\\ElasticCollection\\:\\:first\\(\\) has invalid return type App\\\\Collection\\\\Collection\\.$#"
			count: 1
			path: app/Collection/ElasticCollection.php

		-
			message: "#^Parameter \\#3 \\$total of class App\\\\Helpers\\\\ElasticsearchPaginator constructor expects int, string given\\.$#"
			count: 1
			path: app/Collection/ElasticCollection.php

		-
			message: "#^Property App\\\\Console\\\\Commands\\\\ExportRecruitSheets\\:\\:\\$spearedSheetId has unknown class App\\\\Console\\\\Commands\\\\Google_Service_Sheets as its type\\.$#"
			count: 1
			path: app/Console/Commands/ExportRecruitSheets.php

		-
			message: "#^Parameter \\#1 \\$time of method Illuminate\\\\Console\\\\Scheduling\\\\Event\\:\\:dailyAt\\(\\) expects string, int given\\.$#"
			count: 1
			path: app/Console/Kernel.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:withTrashed\\(\\)\\.$#"
			count: 1
			path: app/Contracts/Form.php

		-
			message: "#^Call to an undefined method \\$this\\(App\\\\Contracts\\\\TopdevModel\\)\\:\\:addFile\\(\\)\\.$#"
			count: 1
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Call to an undefined method \\$this\\(App\\\\Contracts\\\\TopdevModel\\)\\:\\:addMetaCollection\\(\\)\\.$#"
			count: 1
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Call to an undefined method \\$this\\(App\\\\Contracts\\\\TopdevModel\\)\\:\\:addTaxonomies\\(\\)\\.$#"
			count: 1
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Call to an undefined method \\$this\\(App\\\\Contracts\\\\TopdevModel\\)\\:\\:fileAttribute\\(\\)\\.$#"
			count: 2
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Call to an undefined method \\$this\\(App\\\\Contracts\\\\TopdevModel\\)\\:\\:metaAttribute\\(\\)\\.$#"
			count: 2
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Call to an undefined method \\$this\\(App\\\\Contracts\\\\TopdevModel\\)\\:\\:termAttribute\\(\\)\\.$#"
			count: 2
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Call to method toArray\\(\\) on an unknown class App\\\\Contracts\\\\Arrayable\\.$#"
			count: 4
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Class App\\\\Contracts\\\\Arrayable not found\\.$#"
			count: 4
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Variable \\$dateInterval might not be defined\\.$#"
			count: 1
			path: app/Entities/DataLab/Analysis.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$dateRange$#"
			count: 1
			path: app/Entities/DataLab/AnalysisData.php

		-
			message: "#^Access to property \\$mimeType on an unknown class App\\\\Entities\\\\File\\.$#"
			count: 1
			path: app/Entities/Meta.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: app/Entities/Meta.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: app/Entities/Meta.php

		-
			message: "#^Parameter \\$file of anonymous function has invalid type App\\\\Entities\\\\File\\.$#"
			count: 1
			path: app/Entities/Meta.php

		-
			message: "#^Method App\\\\Exceptions\\\\Handler\\:\\:render\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\Http\\\\JsonResponse\\.$#"
			count: 3
			path: app/Exceptions/Handler.php

		-
			message: "#^Method App\\\\Exceptions\\\\Handler\\:\\:render\\(\\) should return Illuminate\\\\Http\\\\Response but returns Symfony\\\\Component\\\\HttpFoundation\\\\Response\\.$#"
			count: 1
			path: app/Exceptions/Handler.php

		-
			message: "#^Call to method get\\(\\) on an unknown class App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\.$#"
			count: 2
			path: app/Helpers/Cloner.php

		-
			message: "#^Call to method getCloneExceptAttributes\\(\\) on an unknown class App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Call to method getRelationsCloneable\\(\\) on an unknown class App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Call to method onCloned\\(\\) on an unknown class App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Call to method onCloning\\(\\) on an unknown class App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Call to method replicate\\(\\) on an unknown class App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Call to method save\\(\\) on an unknown class App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Call to method save\\(\\) on an unknown class App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Method App\\\\Helpers\\\\Cloner\\:\\:cloneModel\\(\\) has invalid return type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Method App\\\\Helpers\\\\Cloner\\:\\:duplicate\\(\\) has invalid return type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\#1 \\$relation of method App\\\\Helpers\\\\Cloner\\:\\:duplicatePivotedRelation\\(\\) expects App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation, Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany given\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$clone of method App\\\\Helpers\\\\Cloner\\:\\:cloneRelations\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$clone of method App\\\\Helpers\\\\Cloner\\:\\:duplicateDirectRelation\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$clone of method App\\\\Helpers\\\\Cloner\\:\\:duplicatePivotedRelation\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$clone of method App\\\\Helpers\\\\Cloner\\:\\:duplicateRelation\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$clone of method App\\\\Helpers\\\\Cloner\\:\\:saveClone\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$orginal of method App\\\\Helpers\\\\Cloner\\:\\:cloneModel\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$orginal of method App\\\\Helpers\\\\Cloner\\:\\:cloneRelations\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$orginal of method App\\\\Helpers\\\\Cloner\\:\\:duplicate\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$orginal of method App\\\\Helpers\\\\Cloner\\:\\:duplicateRelation\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$orginal of method App\\\\Helpers\\\\Cloner\\:\\:saveClone\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$relation of method App\\\\Helpers\\\\Cloner\\:\\:duplicate\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$relation of method App\\\\Helpers\\\\Cloner\\:\\:duplicateDirectRelation\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$relation of method App\\\\Helpers\\\\Cloner\\:\\:duplicatePivotedRelation\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Parameter \\$relation of method App\\\\Helpers\\\\Cloner\\:\\:saveClone\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\.$#"
			count: 1
			path: app/Helpers/Cloner.php

		-
			message: "#^Call to an undefined method App\\\\Helpers\\\\CustomRelation\\:\\:first\\(\\)\\.$#"
			count: 1
			path: app/Helpers/CustomRelation.php

		-
			message: "#^Access to an undefined property App\\\\Helpers\\\\ElasticsearchPaginator\\:\\:\\$hits\\.$#"
			count: 1
			path: app/Helpers/ElasticsearchPaginator.php

		-
			message: "#^Parameter \\#2 \\$pageName of method Illuminate\\\\Pagination\\\\LengthAwarePaginator\\:\\:setCurrentPage\\(\\) expects string, int given\\.$#"
			count: 1
			path: app/Helpers/ElasticsearchPaginator.php

		-
			message: "#^Call to static method findOrFail\\(\\) on an unknown class App\\\\Models\\\\Skill\\.$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Call to static method where\\(\\) on an unknown class App\\\\Models\\\\Skill\\.$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Cannot access offset 0 on true\\.$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Method App\\\\Helpers\\\\ExtractContactCv\\:\\:detectFileCvOCR\\(\\) has invalid return type App\\\\Helpers\\\\json\\.$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Method App\\\\Helpers\\\\ExtractContactCv\\:\\:detectFileCvOCR\\(\\) should return App\\\\Helpers\\\\json but returns array\\<string, array\\<mixed, mixed\\>\\>\\|string\\.$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Method App\\\\Helpers\\\\ExtractContactCv\\:\\:getEmail\\(\\) with return type void returns array\\<int, mixed\\>\\|null but should not return anything\\.$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Method App\\\\Helpers\\\\ExtractContactCv\\:\\:getPhone\\(\\) with return type void returns array\\<int, string\\> but should not return anything\\.$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Method App\\\\Helpers\\\\ExtractContactCv\\:\\:getSkillName\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 2
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Method App\\\\Helpers\\\\ExtractContactCv\\:\\:getSkillName\\(\\) with return type void returns null but should not return anything\\.$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Method App\\\\Helpers\\\\ExtractContactCv\\:\\:getSkillName\\(\\) with return type void returns string but should not return anything\\.$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Method App\\\\Helpers\\\\ExtractContactCv\\:\\:numberFormat\\(\\) with return type void returns string but should not return anything\\.$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: """
				#^PHPDoc tag @param has invalid value \\(\\[object\\] \\$params
				\\$params\\-\\>full_pathfile \\-\\> đường dẫn root đến file\\)\\: Unexpected token "\\[", expected type at offset 80$#
			"""
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$arr\\)\\: Unexpected token \"\\[\", expected type at offset 54$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$col\\)\\: Unexpected token \"\\[\", expected type at offset 80$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$content\\)\\: Unexpected token \"\\[\", expected type at offset 54$#"
			count: 3
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$dir\\)\\: Unexpected token \"\\[\", expected type at offset 106$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$id\\)\\: Unexpected token \"\\[\", expected type at offset 54$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$number\\)\\: Unexpected token \"\\[\", expected type at offset 54$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Result of static method App\\\\Helpers\\\\ExtractContactCv\\:\\:getEmail\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Result of static method App\\\\Helpers\\\\ExtractContactCv\\:\\:getPhone\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Variable \\$process might not be defined\\.$#"
			count: 4
			path: app/Helpers/ExtractContactCv.php

		-
			message: "#^Method App\\\\Helpers\\\\Helpers\\:\\:toSlug\\(\\) with return type void returns string\\|null but should not return anything\\.$#"
			count: 1
			path: app/Helpers/Helpers.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$str\\)\\: Unexpected token \"\\[\", expected type at offset 25$#"
			count: 1
			path: app/Helpers/Helpers.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$string\\)\\: Unexpected token \"\\[\", expected type at offset 25$#"
			count: 2
			path: app/Helpers/Helpers.php

		-
			message: "#^Variable \\$collection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 2
			path: app/Helpers/Helpers.php

		-
			message: "#^Method App\\\\Http\\\\Controllers\\\\API\\\\AnalysisDataController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: app/Http/Controllers/API/AnalysisDataController.php

		-
			message: "#^Method App\\\\Http\\\\Controllers\\\\API\\\\AnalysisDataController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: app/Http/Controllers/API/AnalysisDataController.php

		-
			message: "#^Method App\\\\Http\\\\Controllers\\\\API\\\\AnalysisDataController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: app/Http/Controllers/API/AnalysisDataController.php

		-
			message: "#^Method App\\\\Http\\\\Controllers\\\\API\\\\AnalysisDataController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: app/Http/Controllers/API/AnalysisDataController.php

		-
			message: "#^Call to static method findOrFail\\(\\) on an unknown class App\\\\Http\\\\Controllers\\\\Admin\\\\ExampleModel\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/ExampleController.php

		-
			message: "#^Instantiated class App\\\\Http\\\\Controllers\\\\Admin\\\\ExampleModel not found\\.$#"
			count: 2
			path: app/Http/Controllers/Admin/ExampleController.php

		-
			message: "#^Parameter \\#1 \\$model of class Amscore\\\\Admin\\\\Grid constructor expects Illuminate\\\\Database\\\\Eloquent\\\\Model, App\\\\Http\\\\Controllers\\\\Admin\\\\ExampleModel given\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/ExampleController.php

		-
			message: "#^Call to an undefined static method Maatwebsite\\\\Excel\\\\Facades\\\\Excel\\:\\:create\\(\\)\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/Extensions/ExcelExporter.php

		-
			message: "#^Call to method rows\\(\\) on an unknown class Maatwebsite\\\\Excel\\\\Classes\\\\LaravelExcelWorksheet\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/Extensions/ExcelExporter.php

		-
			message: "#^Function array_only not found\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/Extensions/ExcelExporter.php

		-
			message: "#^Parameter \\$sheet of anonymous function has invalid type Maatwebsite\\\\Excel\\\\Classes\\\\LaravelExcelWorksheet\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/Extensions/ExcelExporter.php

		-
			message: "#^Return type \\(Illuminate\\\\Contracts\\\\View\\\\Factory\\|Illuminate\\\\View\\\\View\\) of method App\\\\Http\\\\Controllers\\\\Admin\\\\Extensions\\\\Fields\\\\Multilingual\\\\Text\\:\\:render\\(\\) should be compatible with return type \\(string\\) of method Illuminate\\\\Contracts\\\\Support\\\\Renderable\\:\\:render\\(\\)$#"
			count: 1
			path: app/Http/Controllers/Admin/Extensions/Fields/Multilingual/Text.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$id\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/Extensions/Fields/PhoneValidateUnique.php

		-
			message: "#^Return type \\(Illuminate\\\\Contracts\\\\View\\\\Factory\\|Illuminate\\\\View\\\\View\\) of method App\\\\Http\\\\Controllers\\\\Admin\\\\Extensions\\\\Fields\\\\PhoneValidateUnique\\:\\:render\\(\\) should be compatible with return type \\(string\\) of method Illuminate\\\\Contracts\\\\Support\\\\Renderable\\:\\:render\\(\\)$#"
			count: 1
			path: app/Http/Controllers/Admin/Extensions/Fields/PhoneValidateUnique.php

		-
			message: "#^Method App\\\\Http\\\\Controllers\\\\Admin\\\\Extensions\\\\Fields\\\\SortableTable\\:\\:setMaxLimit\\(\\) with return type void returns \\$this\\(App\\\\Http\\\\Controllers\\\\Admin\\\\Extensions\\\\Fields\\\\SortableTable\\) but should not return anything\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/Extensions/Fields/SortableTable.php

		-
			message: "#^Function array_has not found\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/Extensions/Filter/MongodbBetween.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/Extensions/Nav/Link.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/Extensions/Nav/Shortcut.php

		-
			message: "#^Parameter \\#1 \\$width of method Amscore\\\\Admin\\\\Layout\\\\Row\\:\\:column\\(\\) expects int, float given\\.$#"
			count: 2
			path: app/Http/Controllers/Admin/HomeController.php

		-
			message: "#^Class Amscore\\\\JsEditor\\\\Json not found\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/bootstrap.php

		-
			message: "#^Parameter \\#1 \\$css of static method Amscore\\\\Admin\\\\Admin\\:\\:css\\(\\) expects null, string given\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/bootstrap.php

		-
			message: "#^Parameter \\#1 \\$js of static method Amscore\\\\Admin\\\\Admin\\:\\:js\\(\\) expects null, string given\\.$#"
			count: 6
			path: app/Http/Controllers/Admin/bootstrap.php

		-
			message: "#^Call to static method create\\(\\) on an unknown class App\\\\User\\.$#"
			count: 1
			path: app/Http/Controllers/Auth/RegisterController.php

		-
			message: "#^Method App\\\\Http\\\\Controllers\\\\Auth\\\\RegisterController\\:\\:create\\(\\) has invalid return type App\\\\User\\.$#"
			count: 1
			path: app/Http/Controllers/Auth/RegisterController.php

		-
			message: "#^Anonymous function has an unused use \\$analysis\\.$#"
			count: 1
			path: app/Http/Controllers/DataLab/AnalysisController.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: app/Http/Controllers/DataLab/AnalysisController.php

		-
			message: "#^Ternary operator condition is always true\\.$#"
			count: 1
			path: app/Http/Controllers/DataLab/AnalysisController.php

		-
			message: "#^Call to an undefined method Amscore\\\\Admin\\\\Grid\\\\Model\\:\\:orderByDesc\\(\\)\\.$#"
			count: 1
			path: app/Http/Controllers/DataLab/AnalysisDataController.php

		-
			message: "#^Parameter \\#1 \\$description of method Amscore\\\\Admin\\\\Layout\\\\Content\\:\\:description\\(\\) expects string, int given\\.$#"
			count: 1
			path: app/Http/Controllers/DataLab/AnalysisDataController.php

		-
			message: "#^Method Amscore\\\\Admin\\\\Grid\\\\Column\\:\\:editable\\(\\) invoked with 1 parameter, 0 required\\.$#"
			count: 1
			path: app/Http/Controllers/DataLab/AnalysisDefinitionController.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$analysisDefinition$#"
			count: 3
			path: app/Http/Controllers/DataLab/AnalysisDefinitionController.php

		-
			message: "#^Parameter \\#1 \\$builder of method Amscore\\\\Admin\\\\Grid\\\\Column\\:\\:filter\\(\\) expects null, array\\<int, string\\> given\\.$#"
			count: 1
			path: app/Http/Controllers/DataLab/AnalysisDefinitionController.php

		-
			message: "#^Parameter \\#1 \\$builder of method Amscore\\\\Admin\\\\Grid\\\\Column\\:\\:filter\\(\\) expects null, string given\\.$#"
			count: 3
			path: app/Http/Controllers/DataLab/AnalysisDefinitionController.php

		-
			message: "#^Parameter \\#1 \\$width of method Amscore\\\\Admin\\\\Form\\\\Field\\:\\:width\\(\\) expects string, int given\\.$#"
			count: 1
			path: app/Http/Controllers/DataLab/AnalysisDefinitionController.php

		-
			message: "#^Access to an undefined property Amscore\\\\Admin\\\\Auth\\\\Database\\\\Administrator\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Amscore\\\\Admin\\\\Auth\\\\Database\\\\Administrator\\>\\:\\:\\$name\\.$#"
			count: 1
			path: app/Http/Controllers/NoteController.php

		-
			message: "#^Method App\\\\Http\\\\Controllers\\\\NoteController\\:\\:grid\\(\\) has invalid return type App\\\\Http\\\\Controllers\\\\Grid\\.$#"
			count: 1
			path: app/Http/Controllers/NoteController.php

		-
			message: "#^Method App\\\\Http\\\\Controllers\\\\NoteController\\:\\:grid\\(\\) should return App\\\\Http\\\\Controllers\\\\Grid but return statement is missing\\.$#"
			count: 1
			path: app/Http/Controllers/NoteController.php

		-
			message: "#^Method App\\\\Http\\\\Middleware\\\\Authenticate\\:\\:redirectTo\\(\\) should return string but return statement is missing\\.$#"
			count: 1
			path: app/Http/Middleware/Authenticate.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\CompanySuggestResource\\:\\:\\$display_name\\.$#"
			count: 2
			path: app/Http/Resources/CompanySuggestResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\CompanySuggestResource\\:\\:\\$image_logo\\.$#"
			count: 2
			path: app/Http/Resources/CompanySuggestResource.php

		-
			message: "#^Call to an undefined method App\\\\Http\\\\Resources\\\\CompanySuggestResource\\:\\:getKey\\(\\)\\.$#"
			count: 1
			path: app/Http/Resources/CompanySuggestResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\JobSuggestResource\\:\\:\\$company\\.$#"
			count: 2
			path: app/Http/Resources/JobSuggestResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\JobSuggestResource\\:\\:\\$title\\.$#"
			count: 2
			path: app/Http/Resources/JobSuggestResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\JobSuggestResource\\:\\:\\$uuid\\.$#"
			count: 1
			path: app/Http/Resources/JobSuggestResource.php

		-
			message: "#^Method App\\\\Http\\\\Resources\\\\SuggestResource\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), App\\\\Http\\\\Resources\\\\CompanySuggestResource\\|App\\\\Http\\\\Resources\\\\JobSuggestResource\\|App\\\\Http\\\\Resources\\\\TaxonomySuggestResource\\>\\.$#"
			count: 1
			path: app/Http/Resources/SuggestResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\TaxonomySuggestResource\\:\\:\\$image\\.$#"
			count: 1
			path: app/Http/Resources/TaxonomySuggestResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\TaxonomySuggestResource\\:\\:\\$term\\.$#"
			count: 2
			path: app/Http/Resources/TaxonomySuggestResource.php

		-
			message: "#^Call to an undefined method App\\\\Http\\\\Resources\\\\TaxonomySuggestResource\\:\\:getKey\\(\\)\\.$#"
			count: 1
			path: app/Http/Resources/TaxonomySuggestResource.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Modules\\\\Job\\\\Entities\\\\Candidate\\>\\:\\:searchable\\(\\)\\.$#"
			count: 1
			path: app/Jobs/CandidateIsUpdated.php

		-
			message: "#^Method App\\\\Jobs\\\\DispatchNewEvent\\:\\:handle\\(\\) with return type void returns Exception but should not return anything\\.$#"
			count: 2
			path: app/Jobs/DispatchNewEvent.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: app/Jobs/EmployerDash/AMSProcessCompanyForEmployerDash.php

		-
			message: "#^Parameter \\#1 \\$job of class Modules\\\\Job\\\\Events\\\\NewJobProcessed constructor expects Modules\\\\Job\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job, Modules\\\\Job\\\\Entities\\\\Job given\\.$#"
			count: 1
			path: app/Jobs/EmployerDash/AMSProcessJobForEmployerDash.php

		-
			message: "#^Property App\\\\Jobs\\\\MakeGoogleIndexing\\:\\:\\$models is never read, only written\\.$#"
			count: 1
			path: app/Jobs/MakeGoogleIndexing.php

		-
			message: "#^Method App\\\\Jobs\\\\ScriptedDocumentUpdate\\:\\:buildScripts\\(\\) should return array but returns string\\.$#"
			count: 1
			path: app/Jobs/ScriptedDocumentUpdate.php

		-
			message: "#^Property App\\\\Jobs\\\\SendMail\\:\\:\\$priority is never read, only written\\.$#"
			count: 1
			path: app/Jobs/SendMail.php

		-
			message: "#^Property App\\\\Jobs\\\\SendMail\\:\\:\\$telegram has unknown class App\\\\Jobs\\\\Telegram\\\\Bot\\\\Api as its type\\.$#"
			count: 1
			path: app/Jobs/SendMail.php

		-
			message: "#^Property App\\\\Jobs\\\\SendMail\\:\\:\\$telegram is unused\\.$#"
			count: 1
			path: app/Jobs/SendMail.php

		-
			message: "#^Property App\\\\Jobs\\\\SendMail\\:\\:\\$type is unused\\.$#"
			count: 1
			path: app/Jobs/SendMail.php

		-
			message: "#^Property App\\\\Jobs\\\\SendRabbitmqEvent\\:\\:\\$origin is never read, only written\\.$#"
			count: 1
			path: app/Jobs/SendRabbitmqEvent.php

		-
			message: "#^Call to static method is\\(\\) on an unknown class Illuminate\\\\Support\\\\Facades\\\\Str\\.$#"
			count: 1
			path: app/Providers/AppServiceProvider.php

		-
			message: "#^Method App\\\\Tools\\\\MarkdownTool\\:\\:getEditPath\\(\\) has invalid return type App\\\\Tools\\\\Builder\\.$#"
			count: 1
			path: app/Tools/MarkdownTool.php

		-
			message: "#^Method App\\\\Tools\\\\MarkdownTool\\:\\:getEditPath\\(\\) should return App\\\\Tools\\\\Builder but returns string\\.$#"
			count: 1
			path: app/Tools/MarkdownTool.php

		-
			message: "#^Method App\\\\Tools\\\\MarkdownTool\\:\\:getMarkdownPath\\(\\) has invalid return type App\\\\Tools\\\\Builder\\.$#"
			count: 1
			path: app/Tools/MarkdownTool.php

		-
			message: "#^Method App\\\\Tools\\\\MarkdownTool\\:\\:getMarkdownPath\\(\\) should return App\\\\Tools\\\\Builder but returns string\\.$#"
			count: 1
			path: app/Tools/MarkdownTool.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$builder$#"
			count: 1
			path: app/Tools/MarkdownTool.php

		-
			message: "#^Undefined variable\\: \\$key$#"
			count: 1
			path: app/Tools/MarkdownTool.php

		-
			message: "#^Method App\\\\Tools\\\\NoteTool\\:\\:getDeletePath\\(\\) has invalid return type App\\\\Tools\\\\Builder\\.$#"
			count: 1
			path: app/Tools/NoteTool.php

		-
			message: "#^Method App\\\\Tools\\\\NoteTool\\:\\:getDeletePath\\(\\) should return App\\\\Tools\\\\Builder but returns string\\.$#"
			count: 1
			path: app/Tools/NoteTool.php

		-
			message: "#^Method App\\\\Tools\\\\NoteTool\\:\\:getEditPath\\(\\) has invalid return type App\\\\Tools\\\\Builder\\.$#"
			count: 1
			path: app/Tools/NoteTool.php

		-
			message: "#^Method App\\\\Tools\\\\NoteTool\\:\\:getEditPath\\(\\) should return App\\\\Tools\\\\Builder but returns string\\.$#"
			count: 1
			path: app/Tools/NoteTool.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$builder$#"
			count: 1
			path: app/Tools/NoteTool.php

		-
			message: "#^Method App\\\\Tools\\\\SendEmailCandidate\\:\\:getResource\\(\\) is unused\\.$#"
			count: 1
			path: app/Tools/SendEmailCandidate.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$builder$#"
			count: 1
			path: app/Tools/SendEmailCandidate.php

		-
			message: "#^Right side of && is always true\\.$#"
			count: 1
			path: app/Traits/Concerns/Salary.php
