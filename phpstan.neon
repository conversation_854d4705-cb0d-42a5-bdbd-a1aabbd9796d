includes:
    - ./vendor/larastan/larastan/extension.neon

    # These errors in this file come from old code so we skip it now
    # Ref https://phpstan.org/user-guide/baseline
    - phpstan-baseline.neon

parameters:
    # Tmp paths for cache
    tmpDir: .phpstan

    # Error not match in ignore file should be ignore :))
    reportUnmatchedIgnoredErrors: false

    # Paths to analyse
    paths:
        - app
        - Modules

    # The level 9 is the highest level
    level: 5

    # Ignore the following errors
    ignoreErrors:
        # - '#Unsafe usage of new static#'

    # Ignore the following paths
    excludePaths:
        - Modules/Blog
        - Modules/VWS
        - Modules/*/Migrations/*
        - Modules/*/factories/*

    checkMissingIterableValueType: false
