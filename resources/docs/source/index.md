---
title: API Reference

language_tabs:
- bash
- javascript

includes:

search: true

toc_footers:
- <a href='http://github.com/mpociot/documentarian'>Documentation Powered by Documentarian</a>
---
<!-- START_INFO -->
# Info

Welcome to the generated API reference.
[Get Postman Collection](http://localhost/docs/collection.json)

<!-- END_INFO -->

#Booking management


<!-- START_5ffc046d1549f7c9c65554bf7f27e1dc -->
## Display a listing of the Bookings.

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/lms/booking?event_type=event&page_size=2&page=1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/lms/booking"
);

let params = {
    "event_type": "event",
    "page_size": "2",
    "page": "1",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": null,
    "data": {
        "pagination": {
            "total": 9,
            "per_page": 10,
            "current_page": 1,
            "last_page": 1,
            "from": 1,
            "to": 9
        },
        "data": [
            {
                "id": 2,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "testing",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": null,
                "end_time": null,
                "created_at": "2020-08-27 23:50:08",
                "updated_at": "2020-08-27 23:50:08",
                "deleted_at": null,
                "metas": []
            },
            {
                "id": 3,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "testing",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": null,
                "end_time": null,
                "created_at": "2020-08-27 23:51:18",
                "updated_at": "2020-08-27 23:51:18",
                "deleted_at": null,
                "metas": []
            },
            {
                "id": 4,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "testing",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": null,
                "end_time": null,
                "created_at": "2020-08-27 23:51:46",
                "updated_at": "2020-08-27 23:51:46",
                "deleted_at": null,
                "metas": []
            },
            {
                "id": 40,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "giapta - <EMAIL>",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": null,
                "end_time": null,
                "created_at": "2020-09-16 16:44:05",
                "updated_at": "2020-09-16 16:44:05",
                "deleted_at": null,
                "metas": [
                    {
                        "id": 21,
                        "event_id": 40,
                        "meta_key": "time1",
                        "meta_value": "value 01",
                        "created_at": "2020-09-16 16:44:06",
                        "updated_at": "2020-09-16 16:44:06"
                    },
                    {
                        "id": 22,
                        "event_id": 40,
                        "meta_key": "time2",
                        "meta_value": "value 02",
                        "created_at": "2020-09-16 16:44:06",
                        "updated_at": "2020-09-16 16:44:06"
                    }
                ]
            },
            {
                "id": 41,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "giapta - <EMAIL>",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": null,
                "end_time": null,
                "created_at": "2020-09-18 22:08:43",
                "updated_at": "2020-09-18 22:08:43",
                "deleted_at": null,
                "metas": [
                    {
                        "id": 23,
                        "event_id": 41,
                        "meta_key": "event_id",
                        "meta_value": "26",
                        "created_at": "2020-09-18 22:08:43",
                        "updated_at": "2020-09-18 22:08:43"
                    },
                    {
                        "id": 24,
                        "event_id": 41,
                        "meta_key": "time1",
                        "meta_value": "value 01",
                        "created_at": "2020-09-18 22:08:43",
                        "updated_at": "2020-09-18 22:08:43"
                    },
                    {
                        "id": 25,
                        "event_id": 41,
                        "meta_key": "time2",
                        "meta_value": "value 02",
                        "created_at": "2020-09-18 22:08:43",
                        "updated_at": "2020-09-18 22:08:43"
                    }
                ]
            },
            {
                "id": 42,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "giapta - <EMAIL>",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": null,
                "end_time": null,
                "created_at": "2020-09-18 22:46:40",
                "updated_at": "2020-09-18 22:46:40",
                "deleted_at": null,
                "metas": [
                    {
                        "id": 26,
                        "event_id": 42,
                        "meta_key": "event_id",
                        "meta_value": "26",
                        "created_at": "2020-09-18 22:46:40",
                        "updated_at": "2020-09-18 22:46:40"
                    },
                    {
                        "id": 27,
                        "event_id": 42,
                        "meta_key": "time1",
                        "meta_value": "value 01",
                        "created_at": "2020-09-18 22:46:40",
                        "updated_at": "2020-09-18 22:46:40"
                    },
                    {
                        "id": 28,
                        "event_id": 42,
                        "meta_key": "time2",
                        "meta_value": "value 02",
                        "created_at": "2020-09-18 22:46:40",
                        "updated_at": "2020-09-18 22:46:40"
                    }
                ]
            },
            {
                "id": 43,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "giapta - <EMAIL>",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": null,
                "end_time": null,
                "created_at": "2020-09-19 00:07:01",
                "updated_at": "2020-09-19 00:07:01",
                "deleted_at": null,
                "metas": [
                    {
                        "id": 29,
                        "event_id": 43,
                        "meta_key": "book_event_id",
                        "meta_value": "26",
                        "created_at": "2020-09-19 00:07:01",
                        "updated_at": "2020-09-19 00:07:01"
                    },
                    {
                        "id": 30,
                        "event_id": 43,
                        "meta_key": "book_ticket_id",
                        "meta_value": "1",
                        "created_at": "2020-09-19 00:07:01",
                        "updated_at": "2020-09-19 00:07:01"
                    },
                    {
                        "id": 31,
                        "event_id": 43,
                        "meta_key": "time1",
                        "meta_value": "value 01",
                        "created_at": "2020-09-19 00:07:01",
                        "updated_at": "2020-09-19 00:07:01"
                    },
                    {
                        "id": 32,
                        "event_id": 43,
                        "meta_key": "time2",
                        "meta_value": "value 02",
                        "created_at": "2020-09-19 00:07:01",
                        "updated_at": "2020-09-19 00:07:01"
                    }
                ]
            },
            {
                "id": 44,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "Event testing",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": "2020-09-19 12:00:00",
                "end_time": "2020-09-19 12:00:00",
                "created_at": "2020-09-19 01:11:31",
                "updated_at": "2020-09-19 01:11:31",
                "deleted_at": null,
                "metas": [
                    {
                        "id": 33,
                        "event_id": 44,
                        "meta_key": "tickets",
                        "meta_value": "[{\"name\":\"\",\"start_time\":\"\",\"end_time\":\"\",\"stop_time\":3,\"stop_time_type\":\"Hour\"}]",
                        "created_at": "2020-09-19 01:11:31",
                        "updated_at": "2020-09-19 01:11:31"
                    },
                    {
                        "id": 34,
                        "event_id": 44,
                        "meta_key": "unlimited",
                        "meta_value": "1",
                        "created_at": "2020-09-19 01:11:31",
                        "updated_at": "2020-09-19 01:11:31"
                    },
                    {
                        "id": 35,
                        "event_id": 44,
                        "meta_key": "limit",
                        "meta_value": "0",
                        "created_at": "2020-09-19 01:11:31",
                        "updated_at": "2020-09-19 01:11:31"
                    }
                ],
                "teacher": [
                    {
                        "id": 11,
                        "term_id": 28,
                        "taxonomy": "teacher",
                        "description": "testing",
                        "created_at": "2020-09-12 08:36:40",
                        "updated_at": "2020-09-12 08:36:40",
                        "pivot": {
                            "object_id": 44,
                            "term_taxonomy_id": 11
                        },
                        "term": {
                            "id": 28,
                            "term_name": "Co Hoa",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-09-12 08:36:40",
                            "updated_at": "2020-09-12 08:36:40"
                        }
                    }
                ]
            },
            {
                "id": 58,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "Review & Practice",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": "2020-09-22 12:00:00",
                "end_time": "2020-09-22 12:00:00",
                "created_at": "2020-09-21 16:57:30",
                "updated_at": "2020-09-21 16:57:30",
                "deleted_at": null,
                "metas": [
                    {
                        "id": 58,
                        "event_id": 58,
                        "meta_key": "tickets",
                        "meta_value": "[{\"name\":\"\",\"start_time\":\"\",\"end_time\":\"\",\"stop_time\":3,\"stop_time_type\":\"Hour\"}]",
                        "created_at": "2020-09-21 16:57:31",
                        "updated_at": "2020-09-21 16:57:31"
                    },
                    {
                        "id": 59,
                        "event_id": 58,
                        "meta_key": "unlimited",
                        "meta_value": "0",
                        "created_at": "2020-09-21 16:57:31",
                        "updated_at": "2020-09-21 16:57:31"
                    },
                    {
                        "id": 60,
                        "event_id": 58,
                        "meta_key": "limit",
                        "meta_value": "12",
                        "created_at": "2020-09-21 16:57:31",
                        "updated_at": "2020-09-21 16:57:31"
                    }
                ],
                "level": [
                    {
                        "id": 15,
                        "term_id": 32,
                        "taxonomy": "level",
                        "description": "Beginner",
                        "created_at": "2020-09-21 16:50:30",
                        "updated_at": "2020-09-21 16:50:30",
                        "pivot": {
                            "object_id": 58,
                            "term_taxonomy_id": 15
                        },
                        "term": {
                            "id": 32,
                            "term_name": "Beginner",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-09-21 16:50:30",
                            "updated_at": "2020-09-21 16:50:30"
                        }
                    },
                    {
                        "id": 19,
                        "term_id": 36,
                        "taxonomy": "level",
                        "description": "Pre Beginner",
                        "created_at": "2020-09-21 16:51:58",
                        "updated_at": "2020-09-21 16:51:58",
                        "pivot": {
                            "object_id": 58,
                            "term_taxonomy_id": 19
                        },
                        "term": {
                            "id": 36,
                            "term_name": "Pre Beginner",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-09-21 16:51:58",
                            "updated_at": "2020-09-21 16:51:58"
                        }
                    }
                ],
                "campus": [
                    {
                        "id": 6,
                        "term_id": 23,
                        "taxonomy": "campus",
                        "description": "campus new",
                        "created_at": "2020-08-22 05:18:17",
                        "updated_at": "2020-08-22 05:18:17",
                        "pivot": {
                            "object_id": 58,
                            "term_taxonomy_id": 6
                        },
                        "term": {
                            "id": 23,
                            "term_name": "D3",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-08-22 05:18:17",
                            "updated_at": "2020-08-22 05:18:17"
                        }
                    }
                ],
                "tag": [
                    {
                        "id": 12,
                        "term_id": 29,
                        "taxonomy": "tag",
                        "description": "English in Use",
                        "created_at": "2020-09-21 16:48:54",
                        "updated_at": "2020-09-21 16:48:54",
                        "pivot": {
                            "object_id": 58,
                            "term_taxonomy_id": 12
                        },
                        "term": {
                            "id": 29,
                            "term_name": "English in Use",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-09-21 16:48:54",
                            "updated_at": "2020-09-21 16:48:54"
                        }
                    }
                ],
                "location": [
                    {
                        "id": 10,
                        "term_id": 27,
                        "taxonomy": "location",
                        "description": "testing",
                        "created_at": "2020-09-12 08:33:14",
                        "updated_at": "2020-09-12 08:33:14",
                        "pivot": {
                            "object_id": 58,
                            "term_taxonomy_id": 10
                        },
                        "term": {
                            "id": 27,
                            "term_name": "Phong Da lat",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-09-12 08:33:14",
                            "updated_at": "2020-09-12 08:33:14"
                        }
                    }
                ],
                "teacher": [
                    {
                        "id": 11,
                        "term_id": 28,
                        "taxonomy": "teacher",
                        "description": "testing",
                        "created_at": "2020-09-12 08:36:40",
                        "updated_at": "2020-09-12 08:36:40",
                        "pivot": {
                            "object_id": 58,
                            "term_taxonomy_id": 11
                        },
                        "term": {
                            "id": 28,
                            "term_name": "Co Hoa",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-09-12 08:36:40",
                            "updated_at": "2020-09-12 08:36:40"
                        }
                    }
                ]
            }
        ]
    }
}
```

### HTTP Request
`GET api/lms/booking`

#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `event_type` |  optional  | Event Type.
    `page_size` |  optional  | Page Size.
    `page` |  optional  | Current Page.

<!-- END_5ffc046d1549f7c9c65554bf7f27e1dc -->

<!-- START_f94c6fdcc78a95cb608b9d6def4af7a8 -->
## Store a newly created Booking in storage.

> Example request:

```bash
curl -X POST \
    "http://localhost/api/lms/booking" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}" \
    -d '{"event_name":"Event name 01","event_status":0,"event_type":"event","event_content":"Content of Booking","metas":"[{\"book_event_id\" : 123, \"book_ticket_id\" : 1, \"meta_value\" : \"value 01\"}]"}'

```

```javascript
const url = new URL(
    "http://localhost/api/lms/booking"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

let body = {
    "event_name": "Event name 01",
    "event_status": 0,
    "event_type": "event",
    "event_content": "Content of Booking",
    "metas": "[{\"book_event_id\" : 123, \"book_ticket_id\" : 1, \"meta_value\" : \"value 01\"}]"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": "Successful",
    "data": {
        "event_name": "Event name 01",
        "user_id": 1,
        "event_status": 0,
        "event_type": "booking",
        "updated_at": "2020-08-30 01:32:34",
        "created_at": "2020-08-30 01:32:34",
        "id": 29
    }
}
```

### HTTP Request
`POST api/lms/booking`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `event_name` | string |  required  | Event Name.
        `event_status` | integer |  optional  | Event status.
        `event_type` | string |  optional  | Event Type.
        `event_content` | string |  optional  | Event Content.
        `metas` | json |  optional  | Meta Value.
    
<!-- END_f94c6fdcc78a95cb608b9d6def4af7a8 -->

<!-- START_ebe8751a99a100cb7bb41adde4537473 -->
## Display the specified Booking.

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/lms/booking/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/lms/booking/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": null,
    "data": {
        "id": 41,
        "user_id": 1,
        "teacher_id": null,
        "event_name": "giapta - <EMAIL>",
        "event_status": 0,
        "event_content": null,
        "event_type": "event",
        "start_time": null,
        "end_time": null,
        "created_at": "2020-09-18 22:08:43",
        "updated_at": "2020-09-18 22:08:43",
        "deleted_at": null,
        "metas": [
            {
                "id": 23,
                "event_id": 41,
                "meta_key": "event_id",
                "meta_value": "26",
                "created_at": "2020-09-18 22:08:43",
                "updated_at": "2020-09-18 22:08:43"
            },
            {
                "id": 24,
                "event_id": 41,
                "meta_key": "time1",
                "meta_value": "value 01",
                "created_at": "2020-09-18 22:08:43",
                "updated_at": "2020-09-18 22:08:43"
            },
            {
                "id": 25,
                "event_id": 41,
                "meta_key": "time2",
                "meta_value": "value 02",
                "created_at": "2020-09-18 22:08:43",
                "updated_at": "2020-09-18 22:08:43"
            }
        ]
    }
}
```

### HTTP Request
`GET api/lms/booking/{booking}`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `event` |  required  | Event Id.

<!-- END_ebe8751a99a100cb7bb41adde4537473 -->

<!-- START_8b7f0c3681659401c1e2fdb97c650883 -->
## Update the specified Booking in storage.

> Example request:

```bash
curl -X PUT \
    "http://localhost/api/lms/booking/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}" \
    -d '{"event_name":"Event name 01","event_status":0,"event_type":"event","event_content":"Content of Event","metas":"[{\"book_event_id\" : 123, \"book_ticket_id\" : 1, \"meta_value\" : \"value 01\"}]"}'

```

```javascript
const url = new URL(
    "http://localhost/api/lms/booking/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

let body = {
    "event_name": "Event name 01",
    "event_status": 0,
    "event_type": "event",
    "event_content": "Content of Event",
    "metas": "[{\"book_event_id\" : 123, \"book_ticket_id\" : 1, \"meta_value\" : \"value 01\"}]"
}

fetch(url, {
    method: "PUT",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": "Successful",
    "data": true
}
```

### HTTP Request
`PUT api/lms/booking/{booking}`

`PATCH api/lms/booking/{booking}`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `event` |  required  | Booking Id.
#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `event_name` | string |  required  | Event Name.
        `event_status` | integer |  optional  | Event status.
        `event_type` | string |  optional  | Event Type.
        `event_content` | string |  optional  | Event Content.
        `metas` | json |  optional  | Meta Value.
    
<!-- END_8b7f0c3681659401c1e2fdb97c650883 -->

<!-- START_73cedca9f46976f8878555389e624687 -->
## Remove the specified Booking from storage.

> Example request:

```bash
curl -X DELETE \
    "http://localhost/api/lms/booking/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/lms/booking/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": "Successful",
    "data": true
}
```

### HTTP Request
`DELETE api/lms/booking/{booking}`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `event` |  required  | Booking Id.

<!-- END_73cedca9f46976f8878555389e624687 -->

#Events management


<!-- START_977b88dde085b504dd278b030e8d163f -->
## Display a listing of the Events.

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/lms/events?event_type=event&page_size=2&page=1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/lms/events"
);

let params = {
    "event_type": "event",
    "page_size": "2",
    "page": "1",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": null,
    "data": {
        "pagination": {
            "total": 7,
            "per_page": 10,
            "current_page": 1,
            "last_page": 1,
            "from": 1,
            "to": 7
        },
        "data": [
            {
                "id": 2,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "testing",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": null,
                "end_time": null,
                "created_at": "2020-08-27 23:50:08",
                "updated_at": "2020-08-27 23:50:08",
                "deleted_at": null,
                "metas": []
            },
            {
                "id": 3,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "testing",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": null,
                "end_time": null,
                "created_at": "2020-08-27 23:51:18",
                "updated_at": "2020-08-27 23:51:18",
                "deleted_at": null,
                "metas": []
            },
            {
                "id": 4,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "testing",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": null,
                "end_time": null,
                "created_at": "2020-08-27 23:51:46",
                "updated_at": "2020-08-27 23:51:46",
                "deleted_at": null,
                "metas": []
            },
            {
                "id": 40,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "giapta - <EMAIL>",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": null,
                "end_time": null,
                "created_at": "2020-09-16 16:44:05",
                "updated_at": "2020-09-16 16:44:05",
                "deleted_at": null,
                "metas": [
                    {
                        "id": 21,
                        "event_id": 40,
                        "meta_key": "time1",
                        "meta_value": "value 01",
                        "created_at": "2020-09-16 16:44:06",
                        "updated_at": "2020-09-16 16:44:06"
                    },
                    {
                        "id": 22,
                        "event_id": 40,
                        "meta_key": "time2",
                        "meta_value": "value 02",
                        "created_at": "2020-09-16 16:44:06",
                        "updated_at": "2020-09-16 16:44:06"
                    }
                ]
            },
            {
                "id": 41,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "giapta - <EMAIL>",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": null,
                "end_time": null,
                "created_at": "2020-09-18 22:08:43",
                "updated_at": "2020-09-18 22:08:43",
                "deleted_at": null,
                "metas": [
                    {
                        "id": 23,
                        "event_id": 41,
                        "meta_key": "event_id",
                        "meta_value": "26",
                        "created_at": "2020-09-18 22:08:43",
                        "updated_at": "2020-09-18 22:08:43"
                    },
                    {
                        "id": 24,
                        "event_id": 41,
                        "meta_key": "time1",
                        "meta_value": "value 01",
                        "created_at": "2020-09-18 22:08:43",
                        "updated_at": "2020-09-18 22:08:43"
                    },
                    {
                        "id": 25,
                        "event_id": 41,
                        "meta_key": "time2",
                        "meta_value": "value 02",
                        "created_at": "2020-09-18 22:08:43",
                        "updated_at": "2020-09-18 22:08:43"
                    }
                ]
            },
            {
                "id": 44,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "Event testing",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": "2020-09-19 12:00:00",
                "end_time": "2020-09-19 12:00:00",
                "created_at": "2020-09-19 01:11:31",
                "updated_at": "2020-09-19 01:11:31",
                "deleted_at": null,
                "metas": [
                    {
                        "id": 33,
                        "event_id": 44,
                        "meta_key": "tickets",
                        "meta_value": "[{\"name\":\"\",\"start_time\":\"\",\"end_time\":\"\",\"stop_time\":3,\"stop_time_type\":\"Hour\"}]",
                        "created_at": "2020-09-19 01:11:31",
                        "updated_at": "2020-09-19 01:11:31"
                    },
                    {
                        "id": 34,
                        "event_id": 44,
                        "meta_key": "unlimited",
                        "meta_value": "1",
                        "created_at": "2020-09-19 01:11:31",
                        "updated_at": "2020-09-19 01:11:31"
                    },
                    {
                        "id": 35,
                        "event_id": 44,
                        "meta_key": "limit",
                        "meta_value": "0",
                        "created_at": "2020-09-19 01:11:31",
                        "updated_at": "2020-09-19 01:11:31"
                    }
                ],
                "teacher": [
                    {
                        "id": 11,
                        "term_id": 28,
                        "taxonomy": "teacher",
                        "description": "testing",
                        "created_at": "2020-09-12 08:36:40",
                        "updated_at": "2020-09-12 08:36:40",
                        "pivot": {
                            "object_id": 44,
                            "term_taxonomy_id": 11
                        },
                        "term": {
                            "id": 28,
                            "term_name": "Co Hoa",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-09-12 08:36:40",
                            "updated_at": "2020-09-12 08:36:40"
                        }
                    }
                ]
            },
            {
                "id": 58,
                "user_id": 1,
                "teacher_id": null,
                "event_name": "Review & Practice",
                "event_status": 0,
                "event_content": null,
                "event_type": "event",
                "start_time": "2020-09-22 12:00:00",
                "end_time": "2020-09-22 12:00:00",
                "created_at": "2020-09-21 16:57:30",
                "updated_at": "2020-09-21 16:57:30",
                "deleted_at": null,
                "metas": [
                    {
                        "id": 58,
                        "event_id": 58,
                        "meta_key": "tickets",
                        "meta_value": "[{\"name\":\"\",\"start_time\":\"\",\"end_time\":\"\",\"stop_time\":3,\"stop_time_type\":\"Hour\"}]",
                        "created_at": "2020-09-21 16:57:31",
                        "updated_at": "2020-09-21 16:57:31"
                    },
                    {
                        "id": 59,
                        "event_id": 58,
                        "meta_key": "unlimited",
                        "meta_value": "0",
                        "created_at": "2020-09-21 16:57:31",
                        "updated_at": "2020-09-21 16:57:31"
                    },
                    {
                        "id": 60,
                        "event_id": 58,
                        "meta_key": "limit",
                        "meta_value": "12",
                        "created_at": "2020-09-21 16:57:31",
                        "updated_at": "2020-09-21 16:57:31"
                    }
                ],
                "level": [
                    {
                        "id": 15,
                        "term_id": 32,
                        "taxonomy": "level",
                        "description": "Beginner",
                        "created_at": "2020-09-21 16:50:30",
                        "updated_at": "2020-09-21 16:50:30",
                        "pivot": {
                            "object_id": 58,
                            "term_taxonomy_id": 15
                        },
                        "term": {
                            "id": 32,
                            "term_name": "Beginner",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-09-21 16:50:30",
                            "updated_at": "2020-09-21 16:50:30"
                        }
                    },
                    {
                        "id": 19,
                        "term_id": 36,
                        "taxonomy": "level",
                        "description": "Pre Beginner",
                        "created_at": "2020-09-21 16:51:58",
                        "updated_at": "2020-09-21 16:51:58",
                        "pivot": {
                            "object_id": 58,
                            "term_taxonomy_id": 19
                        },
                        "term": {
                            "id": 36,
                            "term_name": "Pre Beginner",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-09-21 16:51:58",
                            "updated_at": "2020-09-21 16:51:58"
                        }
                    }
                ],
                "campus": [
                    {
                        "id": 6,
                        "term_id": 23,
                        "taxonomy": "campus",
                        "description": "campus new",
                        "created_at": "2020-08-22 05:18:17",
                        "updated_at": "2020-08-22 05:18:17",
                        "pivot": {
                            "object_id": 58,
                            "term_taxonomy_id": 6
                        },
                        "term": {
                            "id": 23,
                            "term_name": "D3",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-08-22 05:18:17",
                            "updated_at": "2020-08-22 05:18:17"
                        }
                    }
                ],
                "tag": [
                    {
                        "id": 12,
                        "term_id": 29,
                        "taxonomy": "tag",
                        "description": "English in Use",
                        "created_at": "2020-09-21 16:48:54",
                        "updated_at": "2020-09-21 16:48:54",
                        "pivot": {
                            "object_id": 58,
                            "term_taxonomy_id": 12
                        },
                        "term": {
                            "id": 29,
                            "term_name": "English in Use",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-09-21 16:48:54",
                            "updated_at": "2020-09-21 16:48:54"
                        }
                    }
                ],
                "location": [
                    {
                        "id": 10,
                        "term_id": 27,
                        "taxonomy": "location",
                        "description": "testing",
                        "created_at": "2020-09-12 08:33:14",
                        "updated_at": "2020-09-12 08:33:14",
                        "pivot": {
                            "object_id": 58,
                            "term_taxonomy_id": 10
                        },
                        "term": {
                            "id": 27,
                            "term_name": "Phong Da lat",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-09-12 08:33:14",
                            "updated_at": "2020-09-12 08:33:14"
                        }
                    }
                ],
                "teacher": [
                    {
                        "id": 11,
                        "term_id": 28,
                        "taxonomy": "teacher",
                        "description": "testing",
                        "created_at": "2020-09-12 08:36:40",
                        "updated_at": "2020-09-12 08:36:40",
                        "pivot": {
                            "object_id": 58,
                            "term_taxonomy_id": 11
                        },
                        "term": {
                            "id": 28,
                            "term_name": "Co Hoa",
                            "term_alias": null,
                            "term_group": null,
                            "created_at": "2020-09-12 08:36:40",
                            "updated_at": "2020-09-12 08:36:40"
                        }
                    }
                ]
            }
        ]
    }
}
```

### HTTP Request
`GET api/lms/events`

#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `event_type` |  optional  | Event Type.
    `page_size` |  optional  | Page Size.
    `page` |  optional  | Current Page.

<!-- END_977b88dde085b504dd278b030e8d163f -->

<!-- START_8be252d7e592e4cf1cd9e03604eea6b8 -->
## Store a newly created Event in storage.

> Example request:

```bash
curl -X POST \
    "http://localhost/api/lms/events" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}" \
    -d '{"event_name":"Event name 01","event_status":0,"event_type":"event","event_content":"Content of Event","start_time":"2020-08-30 09:30:00","end_time":"2020-08-30 10:45:00","metas":"[{\"meta_key\" : \"time1\", \"meta_value\" : \"value 01\"}]"}'

```

```javascript
const url = new URL(
    "http://localhost/api/lms/events"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

let body = {
    "event_name": "Event name 01",
    "event_status": 0,
    "event_type": "event",
    "event_content": "Content of Event",
    "start_time": "2020-08-30 09:30:00",
    "end_time": "2020-08-30 10:45:00",
    "metas": "[{\"meta_key\" : \"time1\", \"meta_value\" : \"value 01\"}]"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": "Successful",
    "data": {
        "event_name": "Event name 01",
        "user_id": 1,
        "event_status": 0,
        "event_type": "booking",
        "updated_at": "2020-08-30 01:32:34",
        "created_at": "2020-08-30 01:32:34",
        "id": 29
    }
}
```

### HTTP Request
`POST api/lms/events`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `event_name` | string |  required  | Event Name.
        `event_status` | integer |  optional  | Event status.
        `event_type` | string |  optional  | Event Type.
        `event_content` | string |  optional  | Event Content.
        `start_time` | datetime |  optional  | Event start date.
        `end_time` | datetime |  optional  | Event end date.
        `metas` | json |  optional  | Meta Value.
    
<!-- END_8be252d7e592e4cf1cd9e03604eea6b8 -->

<!-- START_bcf08c75648a6593d8dc6e04edeab730 -->
## Display the specified Event.

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/lms/events/28" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/lms/events/28"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": null,
    "data": {
        "id": 58,
        "user_id": 1,
        "teacher_id": null,
        "event_name": "Review & Practice",
        "event_status": 0,
        "event_content": null,
        "event_type": "event",
        "start_time": "2020-09-22 12:00:00",
        "end_time": "2020-09-22 12:00:00",
        "created_at": "2020-09-21 16:57:30",
        "updated_at": "2020-09-21 16:57:30",
        "deleted_at": null,
        "booking": [],
        "metas": [
            {
                "id": 58,
                "event_id": 58,
                "meta_key": "tickets",
                "meta_value": "[{\"name\":\"\",\"start_time\":\"\",\"end_time\":\"\",\"stop_time\":3,\"stop_time_type\":\"Hour\"}]",
                "created_at": "2020-09-21 16:57:31",
                "updated_at": "2020-09-21 16:57:31"
            },
            {
                "id": 59,
                "event_id": 58,
                "meta_key": "unlimited",
                "meta_value": "0",
                "created_at": "2020-09-21 16:57:31",
                "updated_at": "2020-09-21 16:57:31"
            },
            {
                "id": 60,
                "event_id": 58,
                "meta_key": "limit",
                "meta_value": "12",
                "created_at": "2020-09-21 16:57:31",
                "updated_at": "2020-09-21 16:57:31"
            }
        ],
        "level": [
            {
                "id": 15,
                "term_id": 32,
                "taxonomy": "level",
                "description": "Beginner",
                "created_at": "2020-09-21 16:50:30",
                "updated_at": "2020-09-21 16:50:30",
                "pivot": {
                    "object_id": 58,
                    "term_taxonomy_id": 15
                },
                "term": {
                    "id": 32,
                    "term_name": "Beginner",
                    "term_alias": null,
                    "term_group": null,
                    "created_at": "2020-09-21 16:50:30",
                    "updated_at": "2020-09-21 16:50:30"
                }
            },
            {
                "id": 19,
                "term_id": 36,
                "taxonomy": "level",
                "description": "Pre Beginner",
                "created_at": "2020-09-21 16:51:58",
                "updated_at": "2020-09-21 16:51:58",
                "pivot": {
                    "object_id": 58,
                    "term_taxonomy_id": 19
                },
                "term": {
                    "id": 36,
                    "term_name": "Pre Beginner",
                    "term_alias": null,
                    "term_group": null,
                    "created_at": "2020-09-21 16:51:58",
                    "updated_at": "2020-09-21 16:51:58"
                }
            }
        ],
        "campus": [
            {
                "id": 6,
                "term_id": 23,
                "taxonomy": "campus",
                "description": "campus new",
                "created_at": "2020-08-22 05:18:17",
                "updated_at": "2020-08-22 05:18:17",
                "pivot": {
                    "object_id": 58,
                    "term_taxonomy_id": 6
                },
                "term": {
                    "id": 23,
                    "term_name": "D3",
                    "term_alias": null,
                    "term_group": null,
                    "created_at": "2020-08-22 05:18:17",
                    "updated_at": "2020-08-22 05:18:17"
                }
            }
        ],
        "tag": [
            {
                "id": 12,
                "term_id": 29,
                "taxonomy": "tag",
                "description": "English in Use",
                "created_at": "2020-09-21 16:48:54",
                "updated_at": "2020-09-21 16:48:54",
                "pivot": {
                    "object_id": 58,
                    "term_taxonomy_id": 12
                },
                "term": {
                    "id": 29,
                    "term_name": "English in Use",
                    "term_alias": null,
                    "term_group": null,
                    "created_at": "2020-09-21 16:48:54",
                    "updated_at": "2020-09-21 16:48:54"
                }
            }
        ],
        "location": [
            {
                "id": 10,
                "term_id": 27,
                "taxonomy": "location",
                "description": "testing",
                "created_at": "2020-09-12 08:33:14",
                "updated_at": "2020-09-12 08:33:14",
                "pivot": {
                    "object_id": 58,
                    "term_taxonomy_id": 10
                },
                "term": {
                    "id": 27,
                    "term_name": "Phong Da lat",
                    "term_alias": null,
                    "term_group": null,
                    "created_at": "2020-09-12 08:33:14",
                    "updated_at": "2020-09-12 08:33:14"
                }
            }
        ],
        "teacher": [
            {
                "id": 11,
                "term_id": 28,
                "taxonomy": "teacher",
                "description": "testing",
                "created_at": "2020-09-12 08:36:40",
                "updated_at": "2020-09-12 08:36:40",
                "pivot": {
                    "object_id": 58,
                    "term_taxonomy_id": 11
                },
                "term": {
                    "id": 28,
                    "term_name": "Co Hoa",
                    "term_alias": null,
                    "term_group": null,
                    "created_at": "2020-09-12 08:36:40",
                    "updated_at": "2020-09-12 08:36:40"
                }
            }
        ]
    }
}
```

### HTTP Request
`GET api/lms/events/{event}`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `event` |  required  | Event Id.

<!-- END_bcf08c75648a6593d8dc6e04edeab730 -->

<!-- START_af59d71acda2efe958e1cc22e74a434d -->
## Update the specified Event in storage.

> Example request:

```bash
curl -X PUT \
    "http://localhost/api/lms/events/28" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}" \
    -d '{"event_name":"Event name 01","event_status":0,"event_type":"event","event_content":"Content of Event","start_time":"2020-08-30 09:30:00","end_time":"2020-08-30 10:45:00","metas":"[{\"meta_key\" : \"time1\", \"meta_value\" : \"value 01\"}]"}'

```

```javascript
const url = new URL(
    "http://localhost/api/lms/events/28"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

let body = {
    "event_name": "Event name 01",
    "event_status": 0,
    "event_type": "event",
    "event_content": "Content of Event",
    "start_time": "2020-08-30 09:30:00",
    "end_time": "2020-08-30 10:45:00",
    "metas": "[{\"meta_key\" : \"time1\", \"meta_value\" : \"value 01\"}]"
}

fetch(url, {
    method: "PUT",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": "Successful",
    "data": true
}
```

### HTTP Request
`PUT api/lms/events/{event}`

`PATCH api/lms/events/{event}`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `event` |  required  | Event Id.
#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `event_name` | string |  required  | Event Name.
        `event_status` | integer |  optional  | Event status.
        `event_type` | string |  optional  | Event Type.
        `event_content` | string |  optional  | Event Content.
        `start_time` | datetime |  optional  | Event start date.
        `end_time` | datetime |  optional  | Event end date.
        `metas` | json |  optional  | Meta Value.
    
<!-- END_af59d71acda2efe958e1cc22e74a434d -->

<!-- START_2fd26f3455d48bf1d137593c57e0252e -->
## Remove the specified resource from storage.

> Example request:

```bash
curl -X DELETE \
    "http://localhost/api/lms/events/28" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/lms/events/28"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": "Successful",
    "data": true
}
```

### HTTP Request
`DELETE api/lms/events/{event}`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `event` |  required  | Event Id.

<!-- END_2fd26f3455d48bf1d137593c57e0252e -->

#Reset Password


APIs for reset password
<!-- START_2b59239cd44bb1881a9ca7965bb588e6 -->
## Create token password reset.

> Example request:

```bash
curl -X POST \
    "http://localhost/api/auth/forgot-password" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}" \
    -d '{"email":"<EMAIL>"}'

```

```javascript
const url = new URL(
    "http://localhost/api/auth/forgot-password"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

let body = {
    "email": "<EMAIL>"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (404):

```json
{
    "message": "Not found user"
}
```
> Example response (200):

```json
{
    "message": "We have e-mailed your password reset link!"
}
```

### HTTP Request
`POST api/auth/forgot-password`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `email` | string |  required  | Email login.
    
<!-- END_2b59239cd44bb1881a9ca7965bb588e6 -->

<!-- START_bee4c3298d5e5aa2acddb5486921e94b -->
## Creat new password

> Example request:

```bash
curl -X POST \
    "http://localhost/api/auth/reset-password" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}" \
    -d '{"token":569021,"password":"123@123a"}'

```

```javascript
const url = new URL(
    "http://localhost/api/auth/reset-password"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

let body = {
    "token": 569021,
    "password": "123@123a"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (404):

```json
{
    "message": "This password reset token was not found"
}
```
> Example response (422):

```json
{
    "message": "This password reset token is invalid."
}
```
> Example response (200):

```json
null
```

### HTTP Request
`POST api/auth/reset-password`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `token` | number |  required  | token password reset.
        `password` | string |  required  | New password.
    
<!-- END_bee4c3298d5e5aa2acddb5486921e94b -->

#Role management


APIs for managing role
<!-- START_65b6b5b61b3513f228f76bab268ff3be -->
## Get list role

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/auth/roles" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/auth/roles"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "data": [
        {
            "id": 1,
            "role_name": "Admin",
            "role_description": "Admin is the role with the highest level of access to your website",
            "created_at": "2020-08-14 17:18:33",
            "updated_at": "2020-08-14 17:18:33"
        },
        {
            "id": 2,
            "role_name": "User",
            "role_description": "",
            "created_at": "2020-08-14 17:18:33",
            "updated_at": "2020-08-14 17:18:33"
        }
    ]
}
```

### HTTP Request
`GET api/auth/roles`


<!-- END_65b6b5b61b3513f228f76bab268ff3be -->

<!-- START_6c0282a8fb7e2398f1ce27519a4facee -->
## Show role

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/auth/role/1?id=9" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/auth/role/1"
);

let params = {
    "id": "9",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (404):

```json
{
    "message": "Not found role"
}
```
> Example response (200):

```json
{
    "id": 1,
    "role_name": "Admin",
    "role_description": "",
    "created_at": "2020-08-05 14:52:15",
    "updated_at": "2020-08-05 14:52:15"
}
```

### HTTP Request
`GET api/auth/role/{id}`

#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `id` |  required  | The id of the role.

<!-- END_6c0282a8fb7e2398f1ce27519a4facee -->

<!-- START_6e69189bb25a477f9fc9d1a273cdd807 -->
## Create role

> Example request:

```bash
curl -X POST \
    "http://localhost/api/auth/role" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}" \
    -d '{"role_name":"Admin","role_description":"Admin is the role with the highest level of access to your website"}'

```

```javascript
const url = new URL(
    "http://localhost/api/auth/role"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

let body = {
    "role_name": "Admin",
    "role_description": "Admin is the role with the highest level of access to your website"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (400):

```json
{
    "message": {
        "role_name": [
            "The role name field is required."
        ]
    }
}
```
> Example response (200):

```json
{
    "message": "Role created successfully!",
    "data": {
        "id": 1,
        "role_name": "Admin",
        "role_description": "Admin is the role with the highest level of access to your website",
        "created_at": "2020-08-05 14:52:15",
        "updated_at": "2020-08-05 14:52:15"
    }
}
```

### HTTP Request
`POST api/auth/role`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `role_name` | string |  required  | Name Role.
        `role_description` | string |  required  | Description Role.
    
<!-- END_6e69189bb25a477f9fc9d1a273cdd807 -->

<!-- START_8800d48a67dcd16f8122ce0b20f70ab1 -->
## Update role

> Example request:

```bash
curl -X PUT \
    "http://localhost/api/auth/role/1?id=9" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}" \
    -d '{"role_name":"Admin","role_description":"Admin is the role with the highest level of access to your website"}'

```

```javascript
const url = new URL(
    "http://localhost/api/auth/role/1"
);

let params = {
    "id": "9",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

let body = {
    "role_name": "Admin",
    "role_description": "Admin is the role with the highest level of access to your website"
}

fetch(url, {
    method: "PUT",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (400):

```json
{
    "message": {
        "role_name": [
            "The role name field is required."
        ]
    }
}
```
> Example response (404):

```json
{
    "message": "Not found role"
}
```
> Example response (200):

```json
{
    "message": "Role updated successfully!",
    "data": {
        "id": 1,
        "role_name": "Admin",
        "role_description": "Admin is the role with the highest level of access to your website",
        "created_at": "2020-08-05 14:52:15",
        "updated_at": "2020-08-05 14:52:15"
    }
}
```

### HTTP Request
`PUT api/auth/role/{id}`

#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `id` |  required  | The id of the user.
#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `role_name` | string |  required  | Name Role.
        `role_description` | string |  required  | Description Role.
    
<!-- END_8800d48a67dcd16f8122ce0b20f70ab1 -->

<!-- START_aa1624f40286e92967d60f031ce792f3 -->
## Delete role

> Example request:

```bash
curl -X DELETE \
    "http://localhost/api/auth/role/1?id=9" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/auth/role/1"
);

let params = {
    "id": "9",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (404):

```json
{
    "message": "Not found role"
}
```
> Example response (200):

```json
{
    "message": "Role deleted successfully!"
}
```

### HTTP Request
`DELETE api/auth/role/{id}`

#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `id` |  required  | The id of the role.

<!-- END_aa1624f40286e92967d60f031ce792f3 -->

#Terms management


<!-- START_00b2f3de3174d03ea07268e7048875e1 -->
## List Terms.

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/lms/taxonomy/campus" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/lms/taxonomy/campus"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": null,
    "data": {
        "pagination": {
            "total": 0,
            "per_page": 10,
            "current_page": 1,
            "last_page": 1,
            "from": null,
            "to": null
        },
        "data": []
    }
}
```

### HTTP Request
`GET api/lms/taxonomy/{taxonomy}`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `taxonomy` |  required  | Taxonomy of Term.

<!-- END_00b2f3de3174d03ea07268e7048875e1 -->

<!-- START_8cb2102b60159192c2dcdb338be530b5 -->
## Show a Term.

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/lms/taxonomy/campus/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/lms/taxonomy/campus/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": null,
    "data": {
        "id": 2,
        "term_id": 11,
        "taxonomy": "campus",
        "description": "Testing update *********",
        "created_at": null,
        "updated_at": "2020-08-22 05:02:50",
        "term_name": "D3"
    }
}
```

### HTTP Request
`GET api/lms/taxonomy/{taxonomy}/{id}`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `taxonomy` |  required  | Taxonomy of Term.
    `id` |  required  | id of Term taxonomy.

<!-- END_8cb2102b60159192c2dcdb338be530b5 -->

<!-- START_15cc5db315dbf79411976d510ce56da6 -->
## Store a Term.

> Example request:

```bash
curl -X POST \
    "http://localhost/api/lms/taxonomy/campus" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/lms/taxonomy/campus"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": "Successful",
    "data": true
}
```
> Example response (200):

```json
{
    "message": "Failed",
    "data": false
}
```

### HTTP Request
`POST api/lms/taxonomy/{taxonomy}`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `taxonomy` |  required  | Taxonomy of Term.

<!-- END_15cc5db315dbf79411976d510ce56da6 -->

<!-- START_bb04e212ea81f3a11e0fb0e9f2d890ac -->
## Update the Term.

> Example request:

```bash
curl -X PUT \
    "http://localhost/api/lms/taxonomy/campus/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/lms/taxonomy/campus/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "PUT",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": "Successful",
    "data": true
}
```
> Example response (200):

```json
{
    "message": "Failed",
    "data": false
}
```

### HTTP Request
`PUT api/lms/taxonomy/{taxonomy}/{id}`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `taxonomy` |  required  | Taxonomy of Term.
    `id` |  required  | id of Term taxonomy.

<!-- END_bb04e212ea81f3a11e0fb0e9f2d890ac -->

<!-- START_f44b32283841b51bbaaefef9199afec9 -->
## Remove the Term.

> Example request:

```bash
curl -X DELETE \
    "http://localhost/api/lms/taxonomy/campus/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/lms/taxonomy/campus/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "message": "Successful",
    "data": true
}
```
> Example response (200):

```json
{
    "message": "Failed",
    "data": false
}
```

### HTTP Request
`DELETE api/lms/taxonomy/{taxonomy}/{id}`

#### URL Parameters

Parameter | Status | Description
--------- | ------- | ------- | -------
    `taxonomy` |  required  | Taxonomy of Term.
    `id` |  required  | id of Term taxonomy.

<!-- END_f44b32283841b51bbaaefef9199afec9 -->

#User management


APIs for managing users
<!-- START_a925a8d22b3615f12fca79456d286859 -->
## Login user and create token

> Example request:

```bash
curl -X POST \
    "http://localhost/api/auth/login" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}" \
    -d '{"email":"<EMAIL>","password":"123@123a","remember_me":true}'

```

```javascript
const url = new URL(
    "http://localhost/api/auth/login"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

let body = {
    "email": "<EMAIL>",
    "password": "123@123a",
    "remember_me": true
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (404):

```json
{
    "message": "No query results for model"
}
```
> Example response (200):

```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJhdWQiOiIxIiwianRpIjoiNT",
    "token_type": "Bearer",
    "expires_at": "2021-08-08 07:14:39"
}
```

### HTTP Request
`POST api/auth/login`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `email` | string |  required  | Email login.
        `password` | string |  required  | Password login.
        `remember_me` | boolean |  optional  | 
    
<!-- END_a925a8d22b3615f12fca79456d286859 -->

<!-- START_9357c0a600c785fe4f708897facae8b8 -->
## Sign up user

> Example request:

```bash
curl -X POST \
    "http://localhost/api/auth/signup" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}" \
    -d '{"name":"John","email":"<EMAIL>","password":"123@123a"}'

```

```javascript
const url = new URL(
    "http://localhost/api/auth/signup"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

let body = {
    "name": "John",
    "email": "<EMAIL>",
    "password": "123@123a"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (400):

```json
{
    "message": {
        "email": [
            "The email field is required."
        ]
    }
}
```
> Example response (200):

```json
{
    "message": "User created successfully!"
}
```

### HTTP Request
`POST api/auth/signup`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `name` | string |  required  | Name login.
        `email` | string |  required  | Email login.
        `password` | string |  required  | Password login.
    
<!-- END_9357c0a600c785fe4f708897facae8b8 -->

<!-- START_16928cb8fc6adf2d9bb675d62a2095c5 -->
## Logout user (Revoke the token)

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/auth/logout" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/auth/logout"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (401):

```json
{
    "message": "Unauthenticated."
}
```

### HTTP Request
`GET api/auth/logout`


<!-- END_16928cb8fc6adf2d9bb675d62a2095c5 -->

<!-- START_ff6d656b6d81a61adda963b8702034d2 -->
## Get the authenticated User

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/auth/user" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/auth/user"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (401):

```json
{
    "message": "Unauthenticated."
}
```

### HTTP Request
`GET api/auth/user`


<!-- END_ff6d656b6d81a61adda963b8702034d2 -->

<!-- START_2ca19fbb1607f7a4da6d7852540ca98f -->
## Get list user

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/auth/users?page=12" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/auth/users"
);

let params = {
    "page": "12",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
{
    "pagination": {
        "total": 0,
        "per_page": 10,
        "current_page": 1,
        "last_page": 1,
        "from": 0,
        "to": 0
    },
    "data": []
}
```

### HTTP Request
`GET api/auth/users`

#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `page` |  optional  | The page number to return

<!-- END_2ca19fbb1607f7a4da6d7852540ca98f -->

<!-- START_8f7969da5e39e992e053f44cc298c134 -->
## Show user

> Example request:

```bash
curl -X GET \
    -G "http://localhost/api/auth/user/1?id=9" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/auth/user/1"
);

let params = {
    "id": "9",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (404):

```json
{
    "message": "Not found user"
}
```
> Example response (200):

```json
{
    "id": 1,
    "name": "John",
    "email": "<EMAIL>",
    "email_verified_at": null,
    "created_at": "2020-08-05 14:52:15",
    "updated_at": "2020-08-05 14:52:15"
}
```

### HTTP Request
`GET api/auth/user/{id}`

#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `id` |  required  | The id of the user.

<!-- END_8f7969da5e39e992e053f44cc298c134 -->

<!-- START_4994aa47e0771aac5f00e8d3522d31bf -->
## Create user

> Example request:

```bash
curl -X POST \
    "http://localhost/api/auth/user" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}" \
    -d '{"name":"John","email":"<EMAIL>","password":"123@123a"}'

```

```javascript
const url = new URL(
    "http://localhost/api/auth/user"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

let body = {
    "name": "John",
    "email": "<EMAIL>",
    "password": "123@123a"
}

fetch(url, {
    method: "POST",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (400):

```json
{
    "message": {
        "email": [
            "The email field is required."
        ]
    }
}
```
> Example response (200):

```json
{
    "message": "User created successfully!",
    "data": {
        "id": 1,
        "name": "Test",
        "email": "<EMAIL>",
        "created_at": "2020-08-05 14:52:15",
        "updated_at": "2020-08-05 14:52:15"
    }
}
```

### HTTP Request
`POST api/auth/user`

#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `name` | string |  required  | Name login.
        `email` | string |  required  | Email login.
        `password` | string |  required  | Password login.
    
<!-- END_4994aa47e0771aac5f00e8d3522d31bf -->

<!-- START_2a27c740beb6ec5f3d462af4a62e2294 -->
## Update user

> Example request:

```bash
curl -X PUT \
    "http://localhost/api/auth/user/1?id=9" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}" \
    -d '{"name":"John","email":"<EMAIL>","password":"123@123a"}'

```

```javascript
const url = new URL(
    "http://localhost/api/auth/user/1"
);

let params = {
    "id": "9",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

let body = {
    "name": "John",
    "email": "<EMAIL>",
    "password": "123@123a"
}

fetch(url, {
    method: "PUT",
    headers: headers,
    body: body
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (400):

```json
{
    "message": {
        "email": [
            "The email field is required."
        ]
    }
}
```
> Example response (404):

```json
{
    "message": "Not found user"
}
```
> Example response (200):

```json
{
    "message": "User updated successfully!",
    "data": {
        "id": 1,
        "name": "Test",
        "email": "<EMAIL>",
        "created_at": "2020-08-05 14:52:15",
        "updated_at": "2020-08-05 14:52:15"
    }
}
```

### HTTP Request
`PUT api/auth/user/{id}`

#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `id` |  required  | The id of the user.
#### Body Parameters
Parameter | Type | Status | Description
--------- | ------- | ------- | ------- | -----------
    `name` | string |  required  | Name login.
        `email` | string |  required  | Email login.
        `password` | string |  required  | Password login.
    
<!-- END_2a27c740beb6ec5f3d462af4a62e2294 -->

<!-- START_4368cf767f4a7e759d9fd757edb1d7af -->
## Delete user

> Example request:

```bash
curl -X DELETE \
    "http://localhost/api/auth/user/1?id=9" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/api/auth/user/1"
);

let params = {
    "id": "9",
};
Object.keys(params)
    .forEach(key => url.searchParams.append(key, params[key]));

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (404):

```json
{
    "message": "Not found user"
}
```
> Example response (200):

```json
{
    "message": "User deleted successfully!"
}
```

### HTTP Request
`DELETE api/auth/user/{id}`

#### Query Parameters

Parameter | Status | Description
--------- | ------- | ------- | -----------
    `id` |  required  | The id of the user.

<!-- END_4368cf767f4a7e759d9fd757edb1d7af -->

#general


<!-- START_0c068b4037fb2e47e71bd44bd36e3e2a -->
## Authorize a client to access the user&#039;s account.

> Example request:

```bash
curl -X GET \
    -G "http://localhost/oauth/authorize" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/authorize"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (401):

```json
{
    "message": "Unauthenticated."
}
```

### HTTP Request
`GET oauth/authorize`


<!-- END_0c068b4037fb2e47e71bd44bd36e3e2a -->

<!-- START_e48cc6a0b45dd21b7076ab2c03908687 -->
## Approve the authorization request.

> Example request:

```bash
curl -X POST \
    "http://localhost/oauth/authorize" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/authorize"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`POST oauth/authorize`


<!-- END_e48cc6a0b45dd21b7076ab2c03908687 -->

<!-- START_de5d7581ef1275fce2a229b6b6eaad9c -->
## Deny the authorization request.

> Example request:

```bash
curl -X DELETE \
    "http://localhost/oauth/authorize" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/authorize"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`DELETE oauth/authorize`


<!-- END_de5d7581ef1275fce2a229b6b6eaad9c -->

<!-- START_a09d20357336aa979ecd8e3972ac9168 -->
## Authorize a client to access the user&#039;s account.

> Example request:

```bash
curl -X POST \
    "http://localhost/oauth/token" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/token"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`POST oauth/token`


<!-- END_a09d20357336aa979ecd8e3972ac9168 -->

<!-- START_d6a56149547e03307199e39e03e12d1c -->
## Get all of the authorized tokens for the authenticated user.

> Example request:

```bash
curl -X GET \
    -G "http://localhost/oauth/tokens" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/tokens"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (401):

```json
{
    "message": "Unauthenticated."
}
```

### HTTP Request
`GET oauth/tokens`


<!-- END_d6a56149547e03307199e39e03e12d1c -->

<!-- START_a9a802c25737cca5324125e5f60b72a5 -->
## Delete the given token.

> Example request:

```bash
curl -X DELETE \
    "http://localhost/oauth/tokens/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/tokens/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`DELETE oauth/tokens/{token_id}`


<!-- END_a9a802c25737cca5324125e5f60b72a5 -->

<!-- START_abe905e69f5d002aa7d26f433676d623 -->
## Get a fresh transient token cookie for the authenticated user.

> Example request:

```bash
curl -X POST \
    "http://localhost/oauth/token/refresh" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/token/refresh"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`POST oauth/token/refresh`


<!-- END_abe905e69f5d002aa7d26f433676d623 -->

<!-- START_babcfe12d87b8708f5985e9d39ba8f2c -->
## Get all of the clients for the authenticated user.

> Example request:

```bash
curl -X GET \
    -G "http://localhost/oauth/clients" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/clients"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (401):

```json
{
    "message": "Unauthenticated."
}
```

### HTTP Request
`GET oauth/clients`


<!-- END_babcfe12d87b8708f5985e9d39ba8f2c -->

<!-- START_9eabf8d6e4ab449c24c503fcb42fba82 -->
## Store a new client.

> Example request:

```bash
curl -X POST \
    "http://localhost/oauth/clients" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/clients"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`POST oauth/clients`


<!-- END_9eabf8d6e4ab449c24c503fcb42fba82 -->

<!-- START_784aec390a455073fc7464335c1defa1 -->
## Update the given client.

> Example request:

```bash
curl -X PUT \
    "http://localhost/oauth/clients/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/clients/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "PUT",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`PUT oauth/clients/{client_id}`


<!-- END_784aec390a455073fc7464335c1defa1 -->

<!-- START_1f65a511dd86ba0541d7ba13ca57e364 -->
## Delete the given client.

> Example request:

```bash
curl -X DELETE \
    "http://localhost/oauth/clients/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/clients/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`DELETE oauth/clients/{client_id}`


<!-- END_1f65a511dd86ba0541d7ba13ca57e364 -->

<!-- START_9e281bd3a1eb1d9eb63190c8effb607c -->
## Get all of the available scopes for the application.

> Example request:

```bash
curl -X GET \
    -G "http://localhost/oauth/scopes" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/scopes"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (401):

```json
{
    "message": "Unauthenticated."
}
```

### HTTP Request
`GET oauth/scopes`


<!-- END_9e281bd3a1eb1d9eb63190c8effb607c -->

<!-- START_9b2a7699ce6214a79e0fd8107f8b1c9e -->
## Get all of the personal access tokens for the authenticated user.

> Example request:

```bash
curl -X GET \
    -G "http://localhost/oauth/personal-access-tokens" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/personal-access-tokens"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (401):

```json
{
    "message": "Unauthenticated."
}
```

### HTTP Request
`GET oauth/personal-access-tokens`


<!-- END_9b2a7699ce6214a79e0fd8107f8b1c9e -->

<!-- START_a8dd9c0a5583742e671711f9bb3ee406 -->
## Create a new personal access token for the user.

> Example request:

```bash
curl -X POST \
    "http://localhost/oauth/personal-access-tokens" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/personal-access-tokens"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "POST",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`POST oauth/personal-access-tokens`


<!-- END_a8dd9c0a5583742e671711f9bb3ee406 -->

<!-- START_bae65df80fd9d72a01439241a9ea20d0 -->
## Delete the given token.

> Example request:

```bash
curl -X DELETE \
    "http://localhost/oauth/personal-access-tokens/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/oauth/personal-access-tokens/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "DELETE",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```



### HTTP Request
`DELETE oauth/personal-access-tokens/{token_id}`


<!-- END_bae65df80fd9d72a01439241a9ea20d0 -->

<!-- START_2ecd2c34871333ab0f1e6108147335fc -->
## {any}
> Example request:

```bash
curl -X GET \
    -G "http://localhost/1" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Authorization: Bearer {token}"
```

```javascript
const url = new URL(
    "http://localhost/1"
);

let headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Authorization": "Bearer {token}",
};

fetch(url, {
    method: "GET",
    headers: headers,
})
    .then(response => response.json())
    .then(json => console.log(json));
```


> Example response (200):

```json
null
```

### HTTP Request
`GET {any}`


<!-- END_2ecd2c34871333ab0f1e6108147335fc -->


