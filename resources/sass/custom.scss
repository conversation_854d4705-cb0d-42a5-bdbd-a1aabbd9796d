.ag-header-cell-label {
    .ag-header-cell-text  {
        color: #333;
    }
}

.note-list {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 20px;
}

#simple-calendar-app .theme-default .cv-day.today .cv-day-number {
    max-height: 1em;
}

.schedule {
    &-tab {
        &-filter {
            padding-left: 20px;
            padding-top: 20px;
        }
    }
}

.timeline-list {
    overflow-y: auto;
    max-height: 500px;
}

.vs-button {
    font-family: "Roboto", Helvetica, Arial, sans-serif !important;
}

.ag-cell {
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.ag-header-cell {
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.ag-header-group-cell {
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.demo-alignment > * {
    margin-right: 0.5rem !important;
}

.add-student-popup .vs-sidebar {
    width: 1000px !important;
}

.scroll-content-calendar {
    height: 700px;
}

.badge {
    padding: .3rem .5rem;
    text-align: center;
    border-radius: .358rem
}

//.badge {
//    display: inline-block;
//    font-size: 85%;
//    font-weight: 600;
//    line-height: 1;
//    white-space: nowrap;
//    vertical-align: baseline;
//    -webkit-transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,background 0s,border 0s,-webkit-box-shadow .15s ease-in-out;
//    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,background 0s,border 0s,-webkit-box-shadow .15s ease-in-out;
//    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,background 0s,border 0s;
//    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,background 0s,border 0s,-webkit-box-shadow .15s ease-in-out
//}

//[dir] .badge {
//    padding: .3rem .5rem;
//    text-align: center;
//    border-radius: .358rem;
//    color: #fff
//}


//.badge-primary {
//    background-color: #7367f0;
//}
//
//.badge-success {
//    background-color: #28c76f;
//}

//.badge-warning {
//    background-color: rgba(var(--vs-warning), 1);
//}
//
//.badge-danger {
//    background-color: rgba(var(--vs-danger), 1);
//}
//
//[dir] .badge-dark {
//    background-color: #4b4b4b;
//}

.badge {
    display: inline-block;
    font-size: 85%;
    font-weight: 600;
    line-height: 1;
    white-space: nowrap;
    vertical-align: baseline;
    -webkit-transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,background 0s,border 0s,-webkit-box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,background 0s,border 0s,-webkit-box-shadow .15s ease-in-out;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,background 0s,border 0s;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,background 0s,border 0s,-webkit-box-shadow .15s ease-in-out;
    padding: .3rem .5rem;
    text-align: center;
    border-radius: .358rem;
}

.badge-success {
    color: #28c76f !important;
    background-color: rgba(40,199,111,.12);
}

.badge-danger {
    color: #ea5455 !important;
    background-color: rgba(234,84,85,.12);
}

.badge-secondary {
    color: #82868b !important;
    background-color: rgba(130,134,139,.12);
}

.prepend-secondary {
    color: #82868b !important;
    background-color: rgba(130,134,139,.12);
    border: 1px solid #ccc;
    border-right: 0;
}

[dir] .flatpickr-calendar .flatpickr-day.selected, [dir] .flatpickr-calendar .flatpickr-day.startRange, [dir] .flatpickr-calendar .flatpickr-day.endRange, [dir] .flatpickr-calendar .flatpickr-day.selected.inRange, [dir] .flatpickr-calendar .flatpickr-day.startRange.inRange, [dir] .flatpickr-calendar .flatpickr-day.endRange.inRange, [dir] .flatpickr-calendar .flatpickr-day.selected:focus, [dir] .flatpickr-calendar .flatpickr-day.startRange:focus, [dir] .flatpickr-calendar .flatpickr-day.endRange:focus, [dir] .flatpickr-calendar .flatpickr-day.selected:hover, [dir] .flatpickr-calendar .flatpickr-day.startRange:hover, [dir] .flatpickr-calendar .flatpickr-day.endRange:hover, [dir] .flatpickr-calendar .flatpickr-day.selected.prevMonthDay, [dir] .flatpickr-calendar .flatpickr-day.startRange.prevMonthDay, [dir] .flatpickr-calendar .flatpickr-day.endRange.prevMonthDay, [dir] .flatpickr-calendar .flatpickr-day.selected.nextMonthDay, [dir] .flatpickr-calendar .flatpickr-day.startRange.nextMonthDay, [dir] .flatpickr-calendar .flatpickr-day.endRange.nextMonthDay {
    background: rgb(255, 159, 67) !important;
    box-shadow: none;
    border-color: rgb(255, 159, 67) !important;
}

[dir] .vs__dropdown-option--highlight {
    background: rgb(255, 159, 67);
}

[dir] .dropdown-custom .vs-dropdown--menu--after {
    background: rgb(255, 159, 67) !important;
}

[dir] .btn-secondary-border {
    border: 1px solid #82868b !important;
}

.btn-secondary-border {
    color: #82868b !important;
    font-weight: 500;
}

.color-box {
    width: 16px;
    height: 16px;
    border-radius: 30%;
    display: inline-block;
}

.add-new-data-sidebar {
    ::v-deep .vs-sidebar--background {
        z-index: 52010;
    }

    ::v-deep .vs-sidebar {
        z-index: 52010;
        width: 680px;
        max-width: 90vw;

        .img-upload {
            margin-top: 2rem;

            .con-img-upload {
                padding: 0;
            }

            .con-input-upload {
                width: 100%;
                margin: 0;
            }
        }
    }
}

.scroll-area--data-list-add-new {
    // height: calc(var(--vh, 1vh) * 100 - 4.3rem);
    height: calc(var(--vh, 1vh) * 100 - 16px - 45px - 82px);

    &:not(.ps) {
        overflow-y: auto;
    }
}

.chip-item {
    height: 3vh;
}

.student-rank {
    color: #77D3F0;
}


.label-require:after {
    content:" *";
    color: red;
}

textarea {
    height: 100%;
}

.form-add-lesson {
    .vs-popup {
        width: 680px;
    }
}
