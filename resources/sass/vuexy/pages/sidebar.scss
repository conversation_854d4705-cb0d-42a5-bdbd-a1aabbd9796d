/*=========================================================================================
    File Name: sidebar.scss
    Description: Sidebar component style
    ----------------------------------------------------------------------------------------
    Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
      Author: Pixinvent
    Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/


@import "../_variables.scss";

// Note: This styles are regrading demo/default sidebar. If you are creating your custom sidebar or changing something. Be sure to take care of CSS.
.vs-content-sidebar.sidebarpage {
  .vs-sidebar {
    .vs-sidebar--header {
      .header-sidebar {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        width: 100%;

        h4 {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;

          >button {
            margin-left: 10px;
          }
        }
      }
    }

    .vs-sidebar--footer {
      .footer-sidebar {
        display: flex;
        align-items: center;
        justify-content: space-between;

        >button {
          border: 0px solid rgba(0, 0, 0, 0) !important;
          border-left: 1px solid rgba(0, 0, 0, .07) !important;
          border-radius: 0px !important;
        }
      }
    }

    .vs-sidebar--item {
      i.material-icons {
        font-size: 1.2rem;
      }
    }
  }

  &.sidebar-demo-parent {
    .vs-sidebar {
      z-index: 10000 !important;
    }
  }

  .vs-sidebar--background {
    z-index: 52000;
  }
}


.theme-dark{
  .vs-content-sidebar.sidebarpage {
    &.bordered-sidebar {
      .vs-sidebar {
        border: 1px solid $theme-dark-border-color;
      }
    }

  }
}
