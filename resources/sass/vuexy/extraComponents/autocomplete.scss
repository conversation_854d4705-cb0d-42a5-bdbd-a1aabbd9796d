@import '../_variables.scss';

pre.selected.hljs {
  margin: 8px 0;
  height: 295px;
  overflow: auto;
  border: 1px solid rgba(0,0,0,.2);
  border-radius: 5px;
  padding: 1rem;
}

.vue-simple-suggest.designed{
  .input-wrapper{
    input{
        color: inherit;
        position: relative;
        padding: .7rem;
        font-size: 1rem;
        border-radius: 5px;
        border: 1px solid rgba(0,0,0,.2);
        box-shadow: 0 0 0 0 rgba(0,0,0,.15);
        transition: all .3s ease;
        width: 100%;
    }
  }
  &.focus{
    .input-wrapper{
      input{
        border: 1px solid rgba(var(--vs-primary),1)!important;
      }
    }
  }
  .suggestions{
    border-radius: 5px;
    .suggest-item{
      padding: .8rem 1rem;
      &.hover, &.selected{
        background-color: rgba(var(--vs-primary),1)!important;
      }
    }
  }
}

.theme-dark {
  .vx-card {
    .vue-simple-suggest {
      input {
        background: $theme-light-dark-bg !important;
      }

      .suggestions {
        background: $theme-light-dark-bg !important;
      }

    }

    pre.selected.hljs {
      border-color: #414561 !important;
    }
  }

  .vue-simple-suggest {
    input {
      background: $theme-dark-bg !important;
    }

    .suggestions {
      background: $theme-dark-bg !important;
    }

  }

  pre.selected.hljs {
    border-color: #414561 !important;
  }

}
