@import '../_variables.scss';

// TREE COMPONENT SCSS
@-webkit-keyframes "opacity-data-v-25a85bf2" {
	3% {
		fill-opacity: 1;
	}
	75% {
		fill-opacity: 0;
	}
}
@keyframes "opacity-data-v-25a85bf2" {
	3% {
		fill-opacity: 1;
	}
	75% {
		fill-opacity: 0;
	}
}
@-webkit-keyframes "opacity-stroke-data-v-25a85bf2" {
	10% {
		stroke-opacity: 1;
	}
	85% {
		stroke-opacity: 0;
	}
}
@keyframes "opacity-stroke-data-v-25a85bf2" {
	10% {
		stroke-opacity: 1;
	}
	85% {
		stroke-opacity: 0;
	}
}
@-webkit-keyframes "colors-data-v-25a85bf2" {
	0% {
		fill: #9acd32;
	}
	10% {
		fill: gold;
	}
	75% {
		fill: crimson;
	}
}
@keyframes "colors-data-v-25a85bf2" {
	0% {
		fill: #9acd32;
	}
	10% {
		fill: gold;
	}
	75% {
		fill: crimson;
	}
}
@-webkit-keyframes "colors-stroke-data-v-25a85bf2" {
	0% {
		stroke: #9acd32;
	}
	10% {
		stroke: gold;
	}
	75% {
		stroke: crimson;
	}
}
@keyframes "colors-stroke-data-v-25a85bf2" {
	0% {
		stroke: #9acd32;
	}
	10% {
		stroke: gold;
	}
	75% {
		stroke: crimson;
	}
}
@-webkit-keyframes "colors-2-data-v-25a85bf2" {
	0% {
		fill: #ff0;
	}
	50% {
		fill: red;
	}
	65% {
		fill: #ff4500;
	}
	95% {
		fill: gold;
	}
}
@keyframes "colors-2-data-v-25a85bf2" {
	0% {
		fill: #ff0;
	}
	50% {
		fill: red;
	}
	65% {
		fill: #ff4500;
	}
	95% {
		fill: gold;
	}
}
@-webkit-keyframes "colors-3-data-v-25a85bf2" {
	0% {
		fill: #9acd32;
	}
	50% {
		fill: #40e0d0;
	}
	65% {
		fill: #ff0;
	}
	95% {
		fill: orange;
	}
}
@keyframes "colors-3-data-v-25a85bf2" {
	0% {
		fill: #9acd32;
	}
	50% {
		fill: #40e0d0;
	}
	65% {
		fill: #ff0;
	}
	95% {
		fill: orange;
	}
}
@-webkit-keyframes "transform-data-v-25a85bf2" {
	10% {
		-webkit-transform: scale(.75);
		transform: scale(.75);
	}
}
@keyframes "transform-data-v-25a85bf2" {
	10% {
		-webkit-transform: scale(.75);
		transform: scale(.75);
	}
}
@-webkit-keyframes "transform-2-data-v-25a85bf2" {
	40% {
		-webkit-transform: scale(.85);
		transform: scale(.85);
	}
	60% {
		stroke-width: 20;
	}
}
@keyframes "transform-2-data-v-25a85bf2" {
	40% {
		-webkit-transform: scale(.85);
		transform: scale(.85);
	}
	60% {
		stroke-width: 20;
	}
}
.halo-tree {
	li {
		span {
			&:hover {
				background-color: transparent;
			}
		}
		list-style-type: none;
		text-align: left;
		margin: 0;
		padding: 5px 5px 5px 15px;
		position: relative;
		list-style: none;
		&:after {
			content: "";
			left: -8px;
			position: absolute;
			right: auto;
			border-width: 1px;
			border-top: 1px dashed #999;
			height: 20px;
			top: 17px;
			width: 28px;
		}
		&:before {
			content: "";
			left: -8px;
			position: absolute;
			right: auto;
			border-width: 1px;
			border-left: 1px dashed #999;
			bottom: 50px;
			height: 100%;
			top: -8px;
			width: 1px;
		}
		&:last-child {
			&:before {
				height: 26px;
			}
		}
	}
	.expand-enter-active {
		transition: all 3s ease;
		height: 50px;
		overflow: hidden;
	}
	.expand-leave-active {
		transition: all 3s ease;
		height: 0;
		overflow: hidden;
	}
	.expand-enter {
		height: 0;
		opacity: 0;
	}
	.expand-leave {
		height: 0;
		opacity: 0;
	}
	font-size: 14px;
	-webkit-transition: height .3s ease-in-out,padding-top .3s ease-in-out,padding-bottom .3s ease-in-out;
	transition: height .3s ease-in-out,padding-top .3s ease-in-out,padding-bottom .3s ease-in-out;
	ul {
		box-sizing: border-box;
		list-style-type: none;
		text-align: left;
		padding-left: 17px;
		padding-top: 10px;
	}
	.inputCheck {
		display: inline-block;
		position: relative;
		width: 14px;
		height: 14px;
		border: 1px solid #888;
		border-radius: 2px;
		top: 4px;
		text-align: center;
		font-size: 14px;
		line-height: 14px;
	}
	.inputCheck.notAllNodes {
		&:before {
			content: "\2713";
			display: block;
			position: absolute;
			width: 100%;
			height: 100%;
			background-color: #888;
			z-index: 1;
			color: #fff;
		}
	}
	.inputCheck.box-checked {
		&:after {
			content: "\2713";
			display: block;
			position: absolute;
			z-index: 1;
			width: 100%;
			text-align: center;
		}
	}
	.box-halfchecked {
		background-color: #888;
		&:after {
			content: "\2713";
			display: block;
			position: absolute;
			z-index: 1;
			width: 100%;
			text-align: center;
			color: #fff;
		}
	}
	.check {
		display: block;
		position: absolute;
		font-size: 14px;
		width: 16px;
		height: 16px;
		left: -5px;
		top: -4px;
		border: 1px solid #000;
		opacity: 0;
		cursor: pointer;
		-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
		filter: alpha(opacity=0);
		z-index: 2;
	}
	.chkDisabled {
		background-color: #f5f5f5;
		opacity: 1;
		cursor: not-allowed;
	}
	>li.first-node {
		&:before {
			top: 17px;
		}
	}
	>li.second-node {
		&:before {
			top: 4px;
		}
	}
	>li.first-node.only-node {
		&:before {
			border-left: none;
		}
	}
	>li.only-node {
		&:after {
			border-top: none;
		}
	}
	>ul {
		padding-left: 0;
	}
	.tree-expand {
		display: inline-block;
		width: 14px;
		height: 14px;
		text-align: center;
		line-height: 13px;
		border: 1px solid #888;
		border-radius: 2px;
		background: #fff;
		font-style: normal;
	}
	.tree-open {
		line-height: 13px;
		&:after {
			content: "\2013";
		}
	}
	.tree-close {
		&:after {
			content: "+";
		}
	}
	.tree-empty {
		&:after {
			content: "";
		}
	}
	.tree-node-el {
		background-color: #fff;
		padding-left: 2px;
		position: relative;
		z-index: 3;
	}
	li.leaf {
		padding-left: 15px;
		&:after {
			content: "";
			left: -7px;
			position: absolute;
			right: auto;
			border-width: 1px;
			border-top: 1px dashed #999;
			height: 20px;
			top: 17px;
			width: 25px;
		}
	}
	.node-title {
		padding: 3px;
		border-radius: 3px;
		cursor: pointer;
		margin: 0 2px;
		&:hover {
			background-color: #ccc;
		}
	}
	.node-title-disabled {
		padding: 3px;
		border-radius: 3px;
		background-color: #f5f5f5;
		opacity: 1;
		cursor: not-allowed;
		margin: 0 2px;
		&:hover {
			background-color: #f5f5f5;
		}
	}
	.node-selected {
		border: 1px solid #ddd;
		background-color: #ddd;
	}
	.node-title.node-searched {
		border: 1px solid #ff8247;
	}
}
.fade-enter-active {
	transition: opacity .2s;
	transition: opacity .5s,transform .5s;
}
.fade-leave-active {
	transition: opacity .2s;
	transition: opacity .5s,transform .5s;
	opacity: 0;
	transform: translateY(-10px);
}
.fade-enter {
	opacity: 0;
	opacity: 0;
	transform: translateY(-10px);
}
.fade-leave-to {
	opacity: 0;
}
.halo-tree-search-box {
	height: 18px;
	line-height: 18px;
	outline: none;
	border: 1px solid #888;
	border-radius: 3px;
	&:focus {
		border: 1px solid #108ee9;
		-webkit-box-shadow: 0 2px 2px rgba(16,142,233,.2);
		box-shadow: 0 2px 2px rgba(16,142,233,.2);
		-webkit-transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
		-o-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
		transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
	}
}
svg[data-v-25a85bf2] {
	height: 14px;
	width: 14px;
	overflow: visible;
	line-height: 14px;
}
circle[data-v-25a85bf2] {
	fill: #1e90ff;
	fill-opacity: 0;
	-webkit-animation: opacity-data-v-25a85bf2 1.2s linear infinite;
	animation: opacity-data-v-25a85bf2 1.2s linear infinite;
	&:nth-child(12n+1) {
		-webkit-animation-delay: -.1s;
		animation-delay: -.1s;
	}
	&:nth-child(12n+2) {
		-webkit-animation-delay: -.2s;
		animation-delay: -.2s;
	}
	&:nth-child(12n+3) {
		-webkit-animation-delay: -.3s;
		animation-delay: -.3s;
	}
	&:nth-child(12n+4) {
		-webkit-animation-delay: -.4s;
		animation-delay: -.4s;
	}
	&:nth-child(12n+5) {
		-webkit-animation-delay: -.5s;
		animation-delay: -.5s;
	}
	&:nth-child(12n+6) {
		-webkit-animation-delay: -.6s;
		animation-delay: -.6s;
	}
	&:nth-child(12n+7) {
		-webkit-animation-delay: -.7s;
		animation-delay: -.7s;
	}
	&:nth-child(12n+8) {
		-webkit-animation-delay: -.8s;
		animation-delay: -.8s;
	}
	&:nth-child(12n+9) {
		-webkit-animation-delay: -.9s;
		animation-delay: -.9s;
	}
	&:nth-child(12n+10) {
		-webkit-animation-delay: -1s;
		animation-delay: -1s;
	}
	&:nth-child(12n+11) {
		-webkit-animation-delay: -1.1s;
		animation-delay: -1.1s;
	}
	&:nth-child(12n+12) {
		-webkit-animation-delay: -1.2s;
		animation-delay: -1.2s;
	}
}
.g-circles--v2 {
	circle[data-v-25a85bf2] {
		fill-opacity: 0;
		stroke-opacity: 0;
		stroke-width: 1;
		stroke: #9acd32;
		-webkit-animation-name: opacity-stroke-data-v-25a85bf2,colors-data-v-25a85bf2,colors-stroke-data-v-25a85bf2,transform-2-data-v-25a85bf2;
		animation-name: opacity-stroke-data-v-25a85bf2,colors-data-v-25a85bf2,colors-stroke-data-v-25a85bf2,transform-2-data-v-25a85bf2;
	}
}
.g-circles--v3 {
	circle[data-v-25a85bf2] {
		fill-opacity: 1;
		-webkit-animation-name: opacity-data-v-25a85bf2,colors-data-v-25a85bf2;
		animation-name: opacity-data-v-25a85bf2,colors-data-v-25a85bf2;
	}
}
.g-circles--v4 {
	circle[data-v-25a85bf2] {
		fill-opacity: 1;
		fill: orange;
		-webkit-transform-origin: 60px 60px;
		-ms-transform-origin: 60px 60px;
		transform-origin: 60px 60px;
		-webkit-animation-name: opacity-data-v-25a85bf2,colors-3-data-v-25a85bf2,transform-data-v-25a85bf2;
		animation-name: opacity-data-v-25a85bf2,colors-3-data-v-25a85bf2,transform-data-v-25a85bf2;
	}
}
.tree-container {
	position: relative;
	width: 90%;
	height: 36px;
	border: 1px solid #ccc;
  border-radius: 6px;
  .search-input {
    width: 96%;
    height: 30px;
    box-sizing: border-box;
    margin: 5px auto 0;
    font-size: 14px;
    text-indent: 1em;
    outline: none;
    border: 1px solid #ccc;
    border-radius: 6px;
  }
  .tag {
    border: 1px solid #ccc;
    border-radius: 6px;
    float: left;
    position: relative;
    min-width: 50px;
    height: 26px;
    margin: 4px;
    padding: 0 10px;
    line-height: 26px;
    text-align: center;
    background-color: #fff;
    user-select: none;
    cursor: default;
    transition: padding .3s;
    .rmNode {
      display: none;
    }
    &:hover {
      padding-right: 25px;
      >.rmNode {
        display: block !important;
      }
    }
  }
}
.tag-box-container {
	position: relative;
	width: 100%;
	height: 36px;
	overflow: hidden;
}
.tag-box {
	width: 2000%;
	height: 36px;
}
.tree-box {
	margin-top: 3px;
	border-radius: 6px;
	border: 1px solid #ccc;
	box-shadow: 0 0 5px rgba(0,0,0,.4);
	ul {
		margin-left: 0;
		-webkit-padding-start: 10px;
	}
}
.blank {
	background-color: #fff;
	border: 0;
}
.rmNode {
	position: absolute;
	right: 5px;
	top: 5px;
	width: 15px;
	height: 15px;
	line-height: 15px;
	font-size: 12px;
	background-color: #b3b3b3;
	color: #ececec;
	border-radius: 50%;
	cursor: pointer;
}



// CUSTOM CSS
.tree-container {
  .tag {
    background-color: transparent;
  }
}

.halo-tree{

  .first-node {
    padding-left: 0;
  }

  .tree-node-el {
    display: contents;
  }

  .tree-expand {
    background: content-box;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .halo-tree {
    li {
      padding-left: 26px;
    }
  }


  ul {
    padding-left: 17px !important;
  }

  > li.first-node.only-node::after {
    z-index: -10;
  }

  .box-halfchecked:after {
    top: -1px;
  }

  .inputCheck {
    top: 2px;

    &.box-checked:after {
      top: -1px;
    }
  }

  .node-selected {
    background-color: #f8f8f8;
  }
  .node-title{
    &:hover{
      background-color: #f8f8f8;
    }
    padding: 3px 6px;
    margin: 0 4px;
  }
}


.theme-dark {
  .tree-container {
    .tree-box {
      background-color: $theme-dark-bg;
    }
  }
}
