<template>
    <vx-card title="Event detail">
        <div class="vx-row mb-6">
            <div class="vx-col w-full">
                <vs-input class="w-full" v-model="event.event_name" label="Name" name="name" v-validate="'required'"/>
                <span class="text-danger text-sm" v-show="errors.has('name')">{{ errors.first('name') }}</span>
            </div>
        </div>
        <div class="vx-row mb-5">
            <div class="vx-col w-full">
                <template>
                    <quill-editor v-model="event.event_content"></quill-editor>
                </template>
            </div>
        </div>
    </vx-card>
</template>

<script>
// require styles
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import { quillEditor } from 'vue-quill-editor'
export default{
  props: {
    event: {
      type: Object,
      required: true
    },
    validator :{},
    file: {
      type: String
    },
    fileName: {
      type: String
    }
  },
  components: {
    quillEditor
  },
  // data () {
  //   return {
  //     documnent: ''
  //   }
  // },
  computed: {
    fileData: {
      get () {
        return this.file
      },
      set (value) {
        this.$emit('fileChange', value)
      }
    },
    fileNameData: {
      get () {
        return this.fileName
      },
      set (value) {
        this.$emit('fileNameChange', value)
      }
    },
    setUrl () {
      return location.protocol + this.document
    }
  },
  created () {
    if (this.validator) {
      this.$validator = this.validator
    }
  }
  
}
</script>