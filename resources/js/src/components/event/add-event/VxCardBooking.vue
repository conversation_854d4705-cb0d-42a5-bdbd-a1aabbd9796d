<template>
    <vx-card title="Booking" >
        <vs-tabs :position="isSmallerScreen ? 'top' : 'left'" class="tabs-shadow-none" id="profile-tabs" :key="isSmallerScreen">
            <!-- GENERAL -->
            <vs-tab label="Booking Option">
                <div class="tab-general md:ml-4 md:mt-0 mt-4 ml-0">
                    <div class="vx-row mb-6">
                        <div class="vx-col w-full">
                            <span class="mt-5">Total Booking Limit  </span>
                        </div>
                        <vs-checkbox v-model="unlimited"  class="ml-5 mt-5">Unlimited</vs-checkbox>
                        <div class="vx-col sm:w-1/6 w-full mt-3">
                            <vs-input :class="{ hidden: unlimited }" v-model="limit" />
                        </div>
                    </div>
                </div>
            </vs-tab>
            <vs-tab label="Tickets">
                <div class="tab-general md:ml-4 md:mt-0 mt-4 ml-0 px-10">
                    <div class="vx-row mb-6">
                        <div class="vx-col w-full">
                            <span class="mt-3">Slot </span>
                        </div>
                        <div class="vx-col w-full mt-5">
                            <vs-button color="primary" type="border" @click="addTicket()">Add tickets</vs-button>
                        </div>
                        <div class="vx-col w-full mt-5">
                            <vx-card v-for="(item,key) in tickets" :key="key"  class="mt-5">
                                <vs-button radius color="danger" type="flat" @click="removeTicket(key)" icon="close" class="absolute right-0 top-0 mt-3 mr-2"></vs-button>
                                <div class="vx-row mb-6">
                                    <vs-input class="w-3/6 vx-col" v-model="item.name" label="Ticket name"  />
                                </div>
                                <div class="vx-row mb-6">
                                    <div class="vx-col w-full flex">
                                        <span class="mt-3">Start Time </span>
                                        <flat-pickr :config="configTimePicker" v-model="item.start_time"  placeholder="Start time" class="ml-5" />
                                    </div>
                                    <div class="vx-col w-full mt-3">
                                        <span class="mt-3">End Time </span>
                                        <flat-pickr :config="configTimePicker" v-model="item.end_time" placeholder="End time" class="ml-5"/>
                                    </div>
                                </div>
                                <div class="vx-row mb-6">
                                    <div class="vx-col w-full flex">
                                        <vs-textarea label="Description" name="description"/>
                                    </div>
                                </div>
                                <div class="vx-row mb-6">
                                    <div class="vx-col sm:w-1/3 w-full mt-3">
                                        <vs-input :disabled="item.unlimited" v-model="item.limit" /> 
                                    </div>
                                    <vs-checkbox v-model="item.unlimited">Unlimited</vs-checkbox>
                                </div>
                                <div class="vx-row mb-6">
                                    <div class="vx-col w-full flex">
                                        <span class="mt-4">Stop selling ticket</span>
                                        <vs-input v-model="item.stop_time" class="w-3/6 vx-col mx-3" />
                                        <vs-select 
                                            v-model="item.stop_time_type" 
                                            class="selectExample w-1/6 mx-3"
                                        >
                                            <vs-select-item :key="index" :value="item.value" :text="item.text" v-for="(item,index) in timeOption" />
                                        </vs-select>
                                        <span class="mt-4 ml-5"> before events start</span>
                                    </div>
                                </div>
                            </vx-card>
                        </div>
                    </div>
                </div>
            </vs-tab>
        </vs-tabs>
    </vx-card>
</template>

<script>
import flatPickr from 'vue-flatpickr-component'
import 'flatpickr/dist/flatpickr.css'
export default{
  props: {
    event: {
      type: Object,
      required: true
    },
    isSmallerScreen: {
      type: Boolean,
      required: true
    },
    tickets: {
      type: Array,
      required: true
    },
    eventUnlimited: {
      type: Boolean,
      required: true
    },
    eventLimit: {
      type: String
    }
  },
  components: {
    flatPickr
  },
  data () {
    return {
      configTimePicker: {
        enableTime: true,
        enableSeconds: true,
        noCalendar: true
      },
      timeOption: [
        {text: 'Hour', value: 'Hour'},
        {text: 'Day', value: 'Day'},
        {text: 'Week', value: 'Week'},
        {text: 'Month', value: 'Month'}
      ]
    }
  },
  methods: {
    remove (id) {
      this.$emit('remove', id)
    },
    removeTicket (index) {
      this.$emit('removeTicket', index)
    },
    addTicket () {
      this.$emit('addTicket')
    }
  },
  computed: {
    unlimited: {
      get () {
        return this.eventUnlimited
      },
      set (value) {
        this.$emit('unlimitedChange', value)
      }
    },
    limit: {
      get () {
        return this.eventLimit
      },
      set (value) {
        this.$emit('limitChange', value)
      }
    }
  }
}
</script>