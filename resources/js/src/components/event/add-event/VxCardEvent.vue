<template>
    <vx-card title="Event">
        <vs-tabs :position="isSmallerScreen ? 'top' : 'left'" class="tabs-shadow-none" id="profile-tabs" :key="isSmallerScreen">

            <!-- GENERAL -->
            <vs-tab label="Date and Time">
                <div class="tab-general md:ml-4 md:mt-0 mt-4 ml-0">
                    <div class="vx-row mb-6">
                        <div class="vx-col w-full mb-10">
                            <p class="mb-2">Start Date </p>
                            <flat-pickr :config="configdateTimePicker" v-model="event.start_time" placeholder="Start date" name="start_date" v-validate="'required'" />
                            <span class="text-danger text-sm" v-show="errors.has('start_date')">{{ errors.first('start_date') }}</span>
                        </div>
                        <div class="vx-col w-full">
                            <p class="mb-2">End Date </p>
                            <flat-pickr :config="configdateTimePicker" v-model="event.end_time" placeholder="Start date" name="end_date" v-validate="'required'" />
                            <span class="text-danger text-sm" v-show="errors.has('end_date')">{{ errors.first('end_date') }}</span>
                        </div>
                    </div>
                </div>
            </vs-tab>
            <vs-tab label="Lesson">
                <div class="tab-general md:ml-4 md:mt-0 mt-4 ml-0">
                    <div class="vx-row mb-6">
                        <div class="vx-col sm:w-3/6 w-full">
                            <span class="mt-3">Lesson </span>
                        </div>
                        <div class="vx-col sm:w-3/6 w-full">
                            <vs-select autocomplete v-model="eventLesson">
                                <vs-select-item
                                    v-for="(item,index) in lessonOptions"
                                    :key="index"
                                    :value="item.value"
                                    :text="item.text" 
                                />
                            </vs-select>
                        </div>
                    </div>
                </div>
            </vs-tab>
            <vs-tab label="Location">
                <div class="tab-general md:ml-4 md:mt-0 mt-4 ml-0">
                    <div class="vx-row mb-6">
                        <div class="vx-col sm:w-3/6 w-full">
                            <span class="mt-3">Event Location </span>
                        </div>
                        <div class="vx-col sm:w-3/6 w-full">
                            <vs-select autocomplete v-model="eventLocation">
                                <vs-select-item
                                    v-for="(item,index) in locationOptions"
                                    :key="index"
                                    :value="item.value"
                                    :text="item.text" 
                                />
                            </vs-select>
                        </div>
                    </div>
                </div>
            </vs-tab>
            <vs-tab label="Campus">
                <div class="tab-change-pwd md:ml-4 md:mt-0 mt-4 ml-0">
                    <div class="vx-row mb-6">
                        <div class="vx-col w-full">
                            <span class="mt-3">Event Campus </span>
                        </div>
                        <div class="vx-col w-full">
                            <ul class="">
                                <li v-for="item in campusOptions" :key="item.id" class="mt-5" >
                                    <vs-checkbox v-model="eventCampus" :vs-value="item.id">{{item.term_name}}</vs-checkbox>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </vs-tab>
            <vs-tab label="Tags">
                <div class="tab-info md:ml-4 md:mt-0 mt-4 ml-0">
                    <div class="vx-row mb-6">
                        <div class="vx-col w-full">
                            <span class="mt-3">Tags </span>
                        </div>
                        <div class="vx-col w-full">
            
                                <!-- <vs-select
                                    multiple
                                    autocomplete
                                    class="selectExample"
                                    v-model="eventTag"
                                >
                                    <vs-select-item :key="index" :value="item.value" :text="item.text" v-for="(item,index) in tagOptions" />
                                </vs-select> -->
                                <v-select multiple :options="tagOptions" :clearable="false" :dir="$vs.rtl ? 'rtl' : 'ltr'" v-model="eventTag" class="mb-4 md:mb-0 w-1/4" />
                        </div>
                        <!-- <div class="vx-col w-full mt-5">
                            <vs-chip @click="remove(chip.value)" v-for="(chip, index) in tagChips" :key="index" closable> {{ chip.text }} </vs-chip>
                        </div> -->
                    </div>
                </div>
            </vs-tab>
            <vs-tab label="Teacher">
                <div class="tab-social-links md:ml-4 md:mt-0 mt-4 ml-0">
                    <div class="vx-row mb-6">
                        <div class="vx-col w-full">
                            <span class="mt-3">Teacher </span>
                        </div>
                        <div class="vx-col w-full">
                                <vs-select
                                    autocomplete
                                    class="selectExample"
                                    v-model="eventTeacher"
                                >
                                <vs-select-item :key="index" :value="item.value" :text="item.text" v-for="(item,index) in teacherOptions" />
                            </vs-select>
                        </div>
                    </div>
                </div>
            </vs-tab>
            <vs-tab label="Level">
                <div class="tab-text md:ml-4 md:mt-0 mt-4 ml-0">
                    <div class="vx-row mb-6">
                        <div class="vx-col  w-full">
                            <span class="mt-3">Event Level </span>
                        </div>
                        <div class="vx-col w-full">
                            <ul class="">
                                <li v-for="item in levelOptions" :key="item.id" class="mt-5" >
                                    <vs-checkbox v-model="eventLevel" :vs-value="item.id" >{{item.term_name}}</vs-checkbox>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </vs-tab>
            <vs-tab label="Course">
                <div class="tab-text md:ml-4 md:mt-0 mt-4 ml-0">
                    <div class="vx-row mb-6">
                        <div class="vx-col  w-full">
                            <span class="mt-3">Event Course </span>
                        </div>
                        <div class="vx-col w-full">
                            <ul class="">
                                <li v-for="item in courseOptions" :key="item.id" class="mt-5" >
                                    <vs-checkbox v-model="eventCourse" :vs-value="item.id" >{{item.term_name}}</vs-checkbox>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </vs-tab>
            <vs-tab label="Color">
                <div class="tab-text md:ml-4 md:mt-0 mt-4 ml-0">
                    <div class="vx-row mb-6">
                        <div class="vx-col  w-full">
                            <span class="mt-3">Event color </span>
                        </div>
                        <div class="vx-col w-full">
                              <div class="my-5">
                                  <label>Color</label>
                                  <input v-model="color" type="color" name="" value=""/><br>
                              </div>
                        </div>
                    </div>
                </div>
            </vs-tab>
        </vs-tabs>
    </vx-card>
</template>

<script>
import flatPickr from 'vue-flatpickr-component'
import 'flatpickr/dist/flatpickr.css'
import vSelect from 'vue-select'
export default{
  props: {
    event: {
      type: Object,
      required: true
    },
    isSmallerScreen: {
      type: Boolean,
      required: true
    },
    eventColor: {
      type: String
    },
    validator :{}
  },
  components: {
    flatPickr,
    vSelect
  },
  data () {
    return {
      configdateTimePicker: {
        enableTime: true,
        dateFormat: 'd-m-Y H:i',
        defaultDate: 'today'
      },
      configTimePicker: {
        enableTime: true,
        enableSeconds: true,
        noCalendar: true
      },
      params: {
        page_size: -1
      }
    }
  },
  computed: {
    color: {
      get () {
        return this.eventColor
      },
      set (value) {
        this.$emit('colorChange', value)
      }
    },
    eventTeacher: {
      get () {
        return this.event.teacher_id
      },
      set (value) {
        this.$emit('changeEvent', {teacher_id: value})
      }
    },
    eventTag: {
      get () {
        return this.event.tag_ids
      },
      set (value) {
        // const tags = []
        // value.map(item => {
        //   tags.push(item.value)
        // })
        this.$emit('changeEvent', {tag_ids: value})
      }
    },
    eventLocation: {
      get () {
        return this.event.location_ids
      },
      set (value) {
        this.$emit('changeEvent', {location_ids: value})
      }
    },
    eventLesson: {
      get () {
        return this.event.lesson_id
      },
      set (value) {
        this.$emit('changeEvent', {lesson_id: value})
      }
    },
    eventCampus: {
      get () {
        return this.event.campus_ids
      },
      set (value) {
        this.$emit('changeEvent', {campus_ids: value})
      }
    },
    eventLevel: {
      get () {
        return this.event.level_ids
      },
      set (value) {
        this.$emit('changeEvent', {level_ids: value})
      }
    },
    eventCourse: {
      get () {
        return this.event.course_ids
      },
      set (value) {
        this.$emit('changeEvent', {course_ids: value})
      }
    },
    locationOptions () {
      const eventLocation = this.$store.state.eventLocation.eventLocations
      const location = []
      eventLocation.map((item) => {
        location.push({text: item.term_name, value: item.id})
      })
      return location
    },
    lessonOptions () {
      const eventLesson = this.$store.state.lesson.lessons
      const lessons = []
      eventLesson.map((item) => {
        lessons.push({text: item.name, value: item.id})
      })
      return lessons
    },
    campusOptions () {
      return this.$store.state.eventCampus.eventCampus
    },
    tagOptions () {
      const eventTag = this.$store.state.eventTag.eventTags
      const tag = []
      eventTag.map((item) => {
        tag.push({label: item.term_name, value: item.id})
      })
      return tag
    },
    teacherOptions () {
      const eventTeacher = this.$store.state.user.users
      const teachers = []
      eventTeacher.map((item) => {
        teachers.push({text: item.name, value: item.id})
      })
      return teachers
    },
    levelOptions () {
      return this.$store.state.eventLevel.eventLevels
    },
    courseOptions () {
      return this.$store.state.eventCourse.eventCourses
    },
    tagChips () {
      const eventTag = this.$store.state.eventTag.eventTags
      const tag = []
      eventTag.map((item) => {
        if (this.event.tag_ids && this.event.tag_ids.includes(item.id)) {
          tag.push({text: item.term_name, value: item.id})
        }
      })
      return tag
    }
  },
  methods: {
    remove (id) {
      this.$emit('remove', id)
    },
    getEventLocations () {
      this.$store
        .dispatch('eventLocation/fetchEventLocations', this.params)
        .catch((err) => {
          console.error(err)
        })
    },
    getLessons () {
      this.$store
        .dispatch('lesson/fetchLessons', this.params)
        .catch((err) => {
          console.error(err)
        })
    },
    getEventCampus () {
      this.$store
        .dispatch('eventCampus/fetchEventCampus', this.params)
        .catch((err) => {
          console.error(err)
        })
    },
    getEventTag () {
      this.$store
        .dispatch('eventTag/fetchEventTags', this.params)
        .catch((err) => {
          console.error(err)
        })
    },
    getEventTeacher () {
      const params = {
        page_size: -1,
        type: 'teacher'
      }
      this.$store
        .dispatch('user/fetchUsers', params)
        .catch((err) => {
          console.error(err)
        })
    },
    getEventLevel () {
      this.$store
        .dispatch('eventLevel/fetchEventLevels', this.params)
        .catch((err) => {
          console.error(err)
        })
    },
    getEventCourse () {
      this.$store
        .dispatch('eventCourse/fetchEventCourses', this.params)
        .catch((err) => {
          console.error(err)
        })
    }
  },
  created () {
    this.getEventLocations()
    this.getEventCampus()
    this.getEventTag()
    this.getEventTeacher()
    this.getEventLevel()
    this.getEventCourse()
    this.getLessons()
    if (this.validator) {
      this.$validator = this.validator
    }
  }
}
</script>
<style scoped>
.vs--open /deep/ .vs__dropdown-menu{
  overflow-y: scroll;
  height: 200px;
}
</style>