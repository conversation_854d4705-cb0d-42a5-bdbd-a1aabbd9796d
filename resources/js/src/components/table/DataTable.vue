<template>
    <div id="data-table" class="vs-con-loading__container mt-12">
        <ag-grid-vue
            ref="agGridTable"
            :gridOptions="gridOptions"
            :components="components"
            class="ag-theme-material w-100 my-4 ag-grid-table"
            :columnDefs="columnDefs"
            :defaultColDef="defaultColDef"
            :rowData="data"
            rowSelection="multiple"
            colResizeDefault="shift"
            :animateRows="true"
            :floatingFilter="filter"
            :pagination="true"
            :paginationPageSize="paginationPageSize"
            :suppressPaginationPanel="true"
            :enableRtl="$vs.rtl"
        >
        </ag-grid-vue>
        <!-- ITEMS PER PAGE -->
        <div class="flex">
            <div class="flex-grow pagination-size">
                <vs-dropdown vs-trigger-click class="cursor-pointer">
                    <div class="p-4 border border-solid d-theme-border-grey-light rounded-full d-theme-dark-bg cursor-pointer flex items-center justify-between font-medium">
                        <span class="mr-2">{{ pagination.from || 0 }} - {{ pagination.to || 0 }} of {{ pagination.total || 0 }}</span>
                        <feather-icon icon="ChevronDownIcon" svgClasses="h-4 w-4" />
                    </div>
                    <vs-dropdown-menu >
                        <vs-dropdown-item v-for="(item,key) in itemPerPage" v-bind:key="key" @click="getDataTable(item)">
                        <span>{{item}}</span>
                        </vs-dropdown-item>
                    </vs-dropdown-menu>
                </vs-dropdown>
            </div>
            <vs-pagination
                :total="totalPages"
                :max="7"
                v-model="curPage"/>
        </div>
    </div>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue'
import '@sass/vuexy/extraComponents/agGridStyleOverride.scss'
export default {
  name: 'data-table',
  props: {
    data: {
      type: Array,
      required: true
    },
    gridOptions: {},
    components: {},
    columnDefs: {},
    defaultColDef: {},
    paginationPageSize: {},
    pagination: {},
    itemPerPage: {},
    totalPages: {},
    currentPage: {},
    filter: false
  },
  computed: {
    curPage: {
      get () {
        return this.currentPage
      },
      set (value) {
        this.$emit('changeCurrentPage', value)
      }
    },
    gridOption: {
      get () {
        return this.gridOptions
      },
      set (value) {
        this.$emit('changeGridOption', value)
      }
    }
  },
  components: {
    AgGridVue
  },
  created () {
    console.log(this.gridOptions)
  },
  data () {
    return {
      activePrompt:false,
      file: null,
      fileName: null
    }
  },
  methods: {
    getDataTable (item) {
      this.$emit('getData', item)
    }
  }
}
</script>
<style>
#page-user-list .user-list-filters .vs__actions {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-58%);
}
.pagination-size{
    min-width: max-content;
}
</style>