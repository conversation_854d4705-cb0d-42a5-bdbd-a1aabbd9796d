<template>
  <div v-if="attribute" class="flex item-centers">
    <a :href="attribute" target="_blank" class="mr-2"><i>{{ attribute.split('/').pop() }}</i></a>
    <a @click="$emit('update:attribute', '')" style="cursor: pointer;color: #cccccc">
      <feather-icon icon="XIcon" svgClasses="w-5 h-5 stroke-current"/>
    </a>
  </div>
  <vue-dropzone
    v-else
    id="upload-file=dropzone"
    class="vue-dropzone-media"
    :options="dropzoneOptions"
    @vdropzone-removed-file="$emit('update:attribute', '')"
    @vdropzone-success="upload">
  </vue-dropzone>
</template>

<script>
import vue2Dropzone from 'vue2-dropzone'
import 'vue2-dropzone/dist/vue2Dropzone.min.css'
import Common from '../mixins/Common'
const apiToken = localStorage.getItem('accessToken')

export default {
  name: 'DropzoneFileCustom',
  mixins: [Common],
  components: {
    vueDropzone: vue2Dropzone
  },
  props: {
    attribute: {
      type: String,
      default: ''
    },
    dictDefaultMessage: {
      type: String,
      default: 'Upload File (.pdf)'
    },
    maxFiles: {
      type: Number,
      default: 1
    },
    maxFilesize: {
      type: Number,
      default: 10
    }
  },
  data () {
    return {
      dropzoneOptions: {
        url: '/api/upload/file',
        thumbnailWidth: 140,
        thumbnailHeight: 140,
        maxFilesize: this.maxFilesize,
        maxFiles: this.maxFiles,
        dictDefaultMessage: this.dictDefaultMessage,
        addRemoveLinks: true,
        acceptedFiles: 'application/pdf',
        headers: {
          Authorization: `Bearer ${apiToken}`
        }
      }
    }
  },
  methods: {
    upload (file, response) {
      if (response.data) {
        this.$emit('update:attribute', response.data)
      } else {
        this.toastError('Upload error')
      }
    }
  }
}
</script>

<style lang="scss">
.vue-dropzone-media {
  .preview {
    position: relative;
    height: 150px;
    border-radius: 6px;
    border: 1px solid #ccc !important;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;

    a {
      display: block;
      color: #ccc;
      position: absolute;
      bottom: 5px;
    }
  }

  border: 1px solid #ccc !important;
  border-radius: 6px;
  display: flex;
  align-items: center;

  .dz-message {
    width: 100%;
  }
}
</style>
