<template>

  <div class="flex items-center justify-between">
    <div class="flex">
      <vs-button class="ml-3" color="warning" type="filled" icon-pack="feather" icon="icon-plus" v-if="actions.includes('add')" @click="createData">Add</vs-button>
      <vs-button class="ml-3" color="danger" type="filled" icon-pack="feather" icon="icon-trash" v-if="actions.includes('delete')" @click="confirmDeleteRecord">Delete</vs-button>
      <vs-button class="ml-3" color="rgb(173, 159, 159)" text-color="rgb(173, 159, 159)" type="border" icon-pack="feather" icon="icon-edit" v-if="actions.includes('custom-column')" @click="$emit('customColumn')" >Edit Column</vs-button>
      <slot name="left"></slot>
    </div>
    <!-- ACTION - DROPDOWN -->
    <div class="flex">
      <vs-button class="ml-3" color="rgb(158, 204, 56)" type="border" icon-pack="feather" icon="icon-upload" v-if="actions.includes('import')" @click="activePrompt = true">Import</vs-button>
      <vs-button class="ml-3" color="rgb(158, 204, 56)" type="border" icon-pack="feather" icon="icon-download" v-if="actions.includes('export')" @click="$emit('export')">Export</vs-button>
      <slot name="right"></slot>
    </div>
    <vs-dropdown vs-trigger-click class="cursor-pointer" v-if="false">
      <div class="p-3 shadow-drop rounded-lg d-theme-dark-light-bg cursor-pointer flex items-end justify-center text-lg font-medium w-32">
        <span class="mr-2 leading-none">Actions</span>
        <feather-icon icon="ChevronDownIcon" svgClasses="h-4 w-4" />
      </div>

      <vs-dropdown-menu>
        <vs-dropdown-item v-if="actions.includes('delete')">
            <span @click="confirmDeleteRecord" class="flex items-center">
                <feather-icon icon="TrashIcon" svgClasses="h-4 w-4" class="mr-2" />
                <span>Delete</span>
            </span>
        </vs-dropdown-item>

        <vs-dropdown-item v-if="actions.includes('import')">
            <span class="flex items-center" @click="activePrompt = true">
                <feather-icon icon="FilePlusIcon" svgClasses="h-4 w-4" class="mr-2" />
                <span>Import</span>
            </span>
        </vs-dropdown-item>

        <vs-dropdown-item v-if="actions.includes('export')">
            <span class="flex items-center" @click="$emit('export')">
                <feather-icon icon="ShareIcon" svgClasses="h-4 w-4" class="mr-2" />
                <span>Export</span>
            </span>
        </vs-dropdown-item>

        <vs-dropdown-item v-if="actions.includes('custom-column')">
            <span class="flex items-center" @click="$emit('customColumn')">
                <feather-icon icon="SlidersIcon" svgClasses="h-4 w-4" class="mr-2" />
                <span>Custom column</span>
            </span>
        </vs-dropdown-item>


        <!-- <vs-dropdown-item>
        <span class="flex items-center">
            <feather-icon icon="FileIcon" svgClasses="h-4 w-4" class="mr-2" />
            <span>Print</span>
        </span>
        </vs-dropdown-item>

        <vs-dropdown-item>
        <span class="flex items-center">
            <feather-icon icon="SaveIcon" svgClasses="h-4 w-4" class="mr-2" />
            <span>CSV</span>
        </span>
        </vs-dropdown-item> -->
      </vs-dropdown-menu>
    </vs-dropdown>
    <vs-prompt
      @cancel="val=''"
      @accept="upload"
      accept-text="Upload"
      :active.sync="activePrompt">
      <div class="con-exemple-prompt text-center">
        <p>Select the file you want to import</p>
        <div v-if="!fileName">
          <input type="file" class="hidden" ref="uploadImgInput" @change="updateCurrImg" accept=".xlsx, .xls">
          <vs-button color="primary" type="border" icon="cloud_upload" class="mt-5 mx-auto" @click="$refs.uploadImgInput.click()">Select file</vs-button>
        </div>
        <div v-else class="mt-5">
          <vs-icon icon="insert_drive_file" size="small" color="primary" class="mt-3 mr-2"></vs-icon>
          <span>{{fileName}}</span>
        </div>
      </div>
    </vs-prompt>
  </div>
</template>

<script>
export default {
  name: 'vx-action-table',
  props: {
    actions: {type: Array}
  },
  computed: {

  },
  data () {
    return {
      activePrompt:false,
      file: null,
      fileName: null
    }
  },
  methods: {
    confirmDeleteRecord () {
      this.$vs.dialog({
        type: 'confirm',
        color: 'danger',
        title: 'Confirm Delete',
        text: 'Are you sure you want to delete the selected items?',
        accept: this.deleteRecord,
        acceptText: 'Delete'
      })
    },
    successUpload () {
      this.$vs.notify({color:'success', title:'Upload Success', text:'The file was imported successfully'})
    },
    updateCurrImg (input) {
      if (input.target.files && input.target.files[0]) {
        // const reader = new FileReader()
        this.file = input.target.files[0]
        // const reader = new FileReader()
        // const vm = this
        this.fileName = this.file.name
        // reader.onload = () => {
        //   vm.fileName = input.name
        // }
        // reader.readAsDataURL(input)
      }
    },
    upload () {
      this.$emit('importData', this.file)
    },
    deleteRecord () {
      /* Below two lines are just for demo purpose */
      // this.showDeleteSuccess()

      /* UnComment below lines for enabling true flow if deleting user */
      this.$emit('deleteRecord')
    },
    showDeleteSuccess () {
      this.$vs.notify({
        color: 'success',
        title: 'User Deleted',
        text: 'The selected user was successfully deleted'
      })
    },
    close () {
      this.$vs.notify({
        color:'danger',
        title:'Closed',
        text:'You close a dialog!'
      })
    },
    createData() {
      this.$emit('addNewData')
    }
  }
}
</script>
<style scoped>
.vs-dropdown-menu /deep/ .vs-dropdown--menu {
  width: max-content;
}
</style>
