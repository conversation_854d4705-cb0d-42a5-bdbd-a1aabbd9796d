<template>
  <date-range-picker v-model="dateRange" class="vue-daterange-picker">
    <template #input="picker" style="min-width: 350px;">
      {{ picker.startDate | date }} - {{ picker.endDate | date }}
    </template>
    <template #date="data">
      <span class="small">{{ data.date | dateCell }}</span>
    </template>
    <template #ranges="ranges">
      <div class="ranges">
        <ul>
          <li v-for="(range, name) in ranges.ranges" :key="name" @click="ranges.clickRange(range)">
            <b>{{ name }}</b> <small class="text-muted">{{ range[0].toDateString() }} -
            {{ range[1].toDateString() }}</small>
          </li>
        </ul>
      </div>
    </template>
  </date-range-picker>
</template>

<script>
import DateRangePicker from 'vue2-daterange-picker'
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css'

export default {
  name: "VueDateRangePicker",
  components: {
    DateRangePicker
  },
  computed: {
    dateRange() {
      let startDate = new Date();
      let endDate = new Date();
      endDate.setDate(endDate.getDate() + 6)
      return {startDate, endDate}
    }
  },
  filters: {
    dateCell (value) {
      let dt = new Date(value)

      return dt.getDate()
    },
    date (val) {
      return val ? val.toLocaleString() : ''
    }
  },
}
</script>

<style scoped lang="scss">
  .vue-daterange-picker .daterangepicker{
    left: 100% !important;
  }
</style>
