<script>
import Config from "@/config/Config";
export default {
  name: "Common",
  data() {
    return {
      scrollSettings: Config.SCROLL_SETTING
    }
  },
  methods: {
    formatPrice(amount, decimalCount = 2, decimal = ",", thousands = ",") {
      try {
        if (amount === null || amount === '') {
          return amount
        }
        decimalCount = Math.abs(decimalCount);
        decimalCount = isNaN(decimalCount) ? 2 : decimalCount;

        const negativeSign = amount < 0 ? "-" : "";

        let i = parseFloat(amount = Math.abs(Number(amount) || 0).toFixed(decimalCount)).toString();

        let temp = i
        if (temp.includes('.')) {
          let index = temp.indexOf('.')
          temp = i.substr(0, index)
        }
        let j = (temp.length > 3) ? temp.length % 3 : 0;

        return negativeSign + (j ? i.substr(0, j) + thousands : '') + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousands);
      } catch (e) {
        console.log(e)
      }
    },

    toastSuccess(message = 'Save Success', title = 'Success') {
      this.$vs.notify({
        title,
        text: message,
        color: 'success',
        iconPack: 'feather',
        position: 'bottom-right',
        icon:'icon-check-circle'
      })
    },

    toastError(message = 'Error', title = 'Failed') {
      this.$vs.notify({
        title,
        text: message,
        color: 'danger',
        iconPack: 'feather',
        position: 'bottom-right',
        icon:'icon-alert-circle'
      })
    },
    toastException(e) {
      const errors = e.response.data && e.response.data.error ? e.response.data.error : []
      if (Array.isArray(errors)) {
        errors.forEach(err => {
          this.toastError(err, 'Error')
        })
      } else if (typeof errors === 'object') {
        for (const [key, value] of Object.entries(errors)) {
          value.forEach(err => {
            this.toastError(err, 'Error')
          })
        }
      } else {
        this.toastError(errors, 'Error')
      }
    },

  }
}
</script>

<style scoped>

</style>
