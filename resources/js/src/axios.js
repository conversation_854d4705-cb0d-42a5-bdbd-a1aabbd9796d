// axios
import axios from 'axios'
import router from './router'

const baseURL = process.env.MIX_API_V2_URL

const instance = axios.create({
  baseURL,
  // You can add your headers here
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  },
  auth: {
    username: 'TopDevBeta',
    password: 'TopDev@Beta'
  },
  withCredentials: true
})

instance.interceptors.response.use(
  response => {
    return response
  },
  error => {
    if (error.response && error.response.status === 401) {
      router.push('/login').catch(() => {})
    }
    return Promise.reject(error)
  })

export default instance
