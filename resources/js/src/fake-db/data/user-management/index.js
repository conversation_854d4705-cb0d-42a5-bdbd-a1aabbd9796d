import mock from '@/fake-db/mock.js'

const data = {
  users: [
    {
      'id': 268,
      'username': 'adoptionism744',
      'avatar': require('@assets/images/portrait/small/avatar-s-3.jpg'),
      'email': '<EMAIL>',
      'name': '<PERSON>',
      'dob': '28 January 1998',
      'gender': 'male',
      'country': 'Bolivia',
      'role': 'admin',
      'status': 'active',
      'is_verified': true,
      'department': 'sales',
      'company': 'WinDon Technologies Pvt Ltd',
      'mobile': '+65958951757',
      'website': 'https://rowboat.com/insititious/Angelo',
      'languages_known': ['English', 'Arabic'],
      'contact_options': ['email', 'message', 'phone'],
      'location': {
        'add_line_1': 'A-65, Belvedere Streets',
        'add_line_2': '',
        'post_code': '1868',
        'city': 'New York',
        'state': 'New York',
        'country': 'United States'
      },
      'social_links': {
        'twitter': 'https://twitter.com/adoptionism744',
        'facebook': 'https://www.facebook.com/adoptionism664',
        'instagram': 'https://www.instagram.com/adopt-ionism744/',
        'github': 'https://github.com/madop818',
        'codepen': 'https://codepen.io/adoptism243',
        'slack': '@adoptionism744'
      },
      'permissions': {
        'users': {
          'read': true,
          'write': false,
          'create': false,
          'delete': false
        },
        'posts': {
          'read': true,
          'write': true,
          'create': true,
          'delete': true
        },
        'comments': {
          'read': true,
          'write': false,
          'create': true,
          'delete': false
        }

      }
    },
    {
      'id': 269,
      'username': 'demodulation463',
      'avatar': require('@assets/images/portrait/small/avatar-s-2.jpg'),
      'email': '<EMAIL>',
      'name': 'Rubi Ortwein',
      'country': 'Syria',
      'role': 'user',
      'status': 'blocked',
      'is_verified': false,
      'department': 'development'
    },
    {
      'id': 270,
      'username': 'undivorced341',
      'avatar': require('@assets/images/portrait/small/avatar-s-3.jpg'),
      'email': '<EMAIL>',
      'name': 'Donnette Charania',
      'country': 'Iraq',
      'role': 'staff',
      'status': 'deactivated',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 271,
      'username': 'bumbo426',
      'avatar': require('@assets/images/portrait/small/avatar-s-12.jpg'),
      'email': '<EMAIL>',
      'name': 'Ardith Duffett',
      'country': 'Estonia',
      'role': 'user',
      'status': 'active',
      'is_verified': false,
      'department': 'sales'
    },
    {
      'id': 272,
      'username': 'ectodactylism214',
      'avatar': require('@assets/images/portrait/small/avatar-s-16.jpg'),
      'email': '<EMAIL>',
      'name': 'Antone Berman',
      'country': 'India',
      'role': 'user',
      'status': 'blocked',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 273,
      'username': 'panathenaic825',
      'avatar': require('@assets/images/portrait/small/avatar-s-18.jpg'),
      'email': '<EMAIL>',
      'name': 'Maryann Gour',
      'country': 'Solomon Islands',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 274,
      'username': 'reptilious612',
      'avatar': require('@assets/images/portrait/small/avatar-s-7.jpg'),
      'email': '<EMAIL>',
      'name': 'Holli Vanduyne',
      'country': 'Lebanon',
      'role': 'staff',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 275,
      'username': 'candid910',
      'avatar': require('@assets/images/portrait/small/avatar-s-26.jpg'),
      'email': '<EMAIL>',
      'name': 'Juanita Sartoris',
      'country': 'Papua New Guinea',
      'role': 'staff',
      'status': 'active',
      'is_verified': true,
      'department': 'management'
    },
    {
      'id': 276,
      'username': 'ferrotungsten928',
      'avatar': require('@assets/images/portrait/small/avatar-s-20.jpg'),
      'email': '<EMAIL>',
      'name': 'Lia Morga',
      'country': 'Malaysia',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'development'
    },
    {
      'id': 277,
      'username': 'fibered345',
      'avatar': require('@assets/images/portrait/small/avatar-s-14.jpg'),
      'email': '<EMAIL>',
      'name': 'Theo Quatrevingt',
      'country': 'Nepal',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 278,
      'username': 'nonenclosure246',
      'avatar': require('@assets/images/portrait/small/avatar-s-10.jpg'),
      'email': '<EMAIL>',
      'name': 'Lynwood Flud',
      'country': 'Russia',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 279,
      'username': 'uncandied531',
      'avatar': require('@assets/images/portrait/small/avatar-s-13.jpg'),
      'email': '<EMAIL>',
      'name': 'Kaitlin Kahola',
      'country': 'Latvia',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 280,
      'username': 'errancy403',
      'avatar': require('@assets/images/portrait/small/avatar-s-4.jpg'),
      'email': '<EMAIL>',
      'name': 'Alvin Car',
      'country': 'Yemen',
      'role': 'admin',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 281,
      'username': 'renillidae759',
      'avatar': require('@assets/images/portrait/small/avatar-s-16.jpg'),
      'email': '<EMAIL>',
      'name': 'Justin Jacquelin',
      'country': 'Turkmenistan',
      'role': 'user',
      'status': 'blocked',
      'is_verified': true,
      'department': 'management'
    },
    {
      'id': 282,
      'username': 'jellylike89',
      'avatar': require('@assets/images/portrait/small/avatar-s-20.jpg'),
      'email': '<EMAIL>',
      'name': 'Chloe Tague',
      'country': 'Pakistan',
      'role': 'staff',
      'status': 'active',
      'is_verified': true,
      'department': 'development'
    },
    {
      'id': 283,
      'username': 'ocular772',
      'avatar': require('@assets/images/portrait/small/avatar-s-6.jpg'),
      'email': '<EMAIL>',
      'name': 'Zola Tauarez',
      'country': 'Dominica',
      'role': 'admin',
      'status': 'deactivated',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 284,
      'username': 'oxgang923',
      'avatar': require('@assets/images/portrait/small/avatar-s-11.jpg'),
      'email': '<EMAIL>',
      'name': 'Wm Cieszynski',
      'country': 'South Korea',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 285,
      'username': 'ideationally882',
      'avatar': require('@assets/images/portrait/small/avatar-s-1.jpg'),
      'email': '<EMAIL>',
      'name': 'Hope Mobus',
      'country': 'United States of America',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 286,
      'username': 'cynomorphous587',
      'avatar': require('@assets/images/portrait/small/avatar-s-25.jpg'),
      'email': '<EMAIL>',
      'name': 'Lee Wernimont',
      'country': 'South Africa',
      'role': 'admin',
      'status': 'active',
      'is_verified': true,
      'department': 'development'
    },
    {
      'id': 287,
      'username': 'windtight501',
      'avatar': require('@assets/images/portrait/small/avatar-s-3.jpg'),
      'email': '<EMAIL>',
      'name': 'Myesha Denman',
      'country': 'Cyprus',
      'role': 'staff',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 288,
      'username': 'strongylate147',
      'avatar': require('@assets/images/portrait/small/avatar-s-21.jpg'),
      'email': '<EMAIL>',
      'name': 'Cornell Roszell',
      'country': 'Algeria',
      'role': 'admin',
      'status': 'active',
      'is_verified': true,
      'department': 'development'
    },
    {
      'id': 289,
      'username': 'reblade348',
      'avatar': require('@assets/images/portrait/small/avatar-s-6.jpg'),
      'email': '<EMAIL>',
      'name': 'Vernon Ogrodowicz',
      'country': 'Botswana',
      'role': 'admin',
      'status': 'deactivated',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 290,
      'username': 'trapping909',
      'avatar': require('@assets/images/portrait/small/avatar-s-26.jpg'),
      'email': '<EMAIL>',
      'name': 'Rosy Litza',
      'country': 'Iran',
      'role': 'staff',
      'status': 'active',
      'is_verified': true,
      'department': 'development'
    },
    {
      'id': 291,
      'username': 'associatedness456',
      'avatar': require('@assets/images/portrait/small/avatar-s-16.jpg'),
      'email': '<EMAIL>',
      'name': 'Carl Lano',
      'country': 'Japan',
      'role': 'user',
      'status': 'blocked',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 292,
      'username': 'deformable333',
      'avatar': require('@assets/images/portrait/small/avatar-s-5.jpg'),
      'email': '<EMAIL>',
      'name': 'Jamika Overlee',
      'country': 'Colombia',
      'role': 'admin',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 293,
      'username': 'abaptiston684',
      'avatar': require('@assets/images/portrait/small/avatar-s-10.jpg'),
      'email': '<EMAIL>',
      'name': 'Lyle Pytko',
      'country': 'Somalia',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'management'
    },
    {
      'id': 294,
      'username': 'neglector719',
      'avatar': require('@assets/images/portrait/small/avatar-s-20.jpg'),
      'email': '<EMAIL>',
      'name': 'Latoria Josef',
      'country': 'Lithuania',
      'role': 'staff',
      'status': 'active',
      'is_verified': true,
      'department': 'development'
    },
    {
      'id': 295,
      'username': 'perameloid596',
      'avatar': require('@assets/images/portrait/small/avatar-s-18.jpg'),
      'email': '<EMAIL>',
      'name': 'Tennille Draft',
      'country': 'Somalia',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 296,
      'username': 'fetoplacental529',
      'avatar': require('@assets/images/portrait/small/avatar-s-20.jpg'),
      'email': '<EMAIL>',
      'name': 'Bernadette Ludovici',
      'country': 'Cameroon',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'development'
    },
    {
      'id': 297,
      'username': 'interdiffusion271',
      'avatar': require('@assets/images/portrait/small/avatar-s-8.jpg'),
      'email': '<EMAIL>',
      'name': 'Mui Melching',
      'country': 'Iran',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 298,
      'username': 'vernacularize342',
      'avatar': require('@assets/images/portrait/small/avatar-s-18.jpg'),
      'email': '<EMAIL>',
      'name': 'Mitsue Houlahan',
      'country': 'Malawi',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 299,
      'username': 'sassenach8',
      'avatar': require('@assets/images/portrait/small/avatar-s-6.jpg'),
      'email': '<EMAIL>',
      'name': 'Elsa Neubert',
      'country': 'Togo',
      'role': 'staff',
      'status': 'deactivated',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 300,
      'username': 'diplantidian91',
      'avatar': require('@assets/images/portrait/small/avatar-s-19.jpg'),
      'email': '<EMAIL>',
      'name': 'Kandice Mizelle',
      'country': 'Greece',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 301,
      'username': 'precognizant796',
      'avatar': require('@assets/images/portrait/small/avatar-s-22.jpg'),
      'email': '<EMAIL>',
      'name': 'Damian Hayzlett',
      'country': 'St. Lucia',
      'role': 'staff',
      'status': 'active',
      'is_verified': true,
      'department': 'management'
    },
    {
      'id': 302,
      'username': 'submaster902',
      'avatar': require('@assets/images/portrait/small/avatar-s-16.jpg'),
      'email': '<EMAIL>',
      'name': 'Aundrea Stempel',
      'country': 'Cyprus',
      'role': 'user',
      'status': 'blocked',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 303,
      'username': 'ladytide97',
      'avatar': require('@assets/images/portrait/small/avatar-s-21.jpg'),
      'email': '<EMAIL>',
      'name': 'Shiloh Spielmaker',
      'country': 'Palestine',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'development'
    },
    {
      'id': 304,
      'username': 'prayingly678',
      'avatar': require('@assets/images/portrait/small/avatar-s-24.jpg'),
      'email': '<EMAIL>',
      'name': 'Terese Dyreson',
      'country': 'Sao Tome and Principe',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'management'
    },
    {
      'id': 305,
      'username': 'unclotted302',
      'avatar': require('@assets/images/portrait/small/avatar-s-6.jpg'),
      'email': '<EMAIL>',
      'name': 'Vashti Kilton',
      'country': 'Portugal',
      'role': 'user',
      'status': 'deactivated',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 306,
      'username': 'unfascinating985',
      'avatar': require('@assets/images/portrait/small/avatar-s-19.jpg'),
      'email': '<EMAIL>',
      'name': 'Carter Mendes',
      'country': 'Armenia',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 307,
      'username': 'gardenwise712',
      'avatar': require('@assets/images/portrait/small/avatar-s-24.jpg'),
      'email': '<EMAIL>',
      'name': 'Helene Madden',
      'country': 'Finland',
      'role': 'staff',
      'status': 'active',
      'is_verified': true,
      'department': 'development'
    },
    {
      'id': 308,
      'username': 'interagree870',
      'avatar': require('@assets/images/portrait/small/avatar-s-13.jpg'),
      'email': '<EMAIL>',
      'name': 'Ashton Calderone',
      'country': 'Italy',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 309,
      'username': 'brutification848',
      'avatar': require('@assets/images/portrait/small/avatar-s-14.jpg'),
      'email': '<EMAIL>',
      'name': 'Robert Lyster',
      'country': 'Turkey',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 310,
      'username': 'unhypocritically536',
      'avatar': require('@assets/images/portrait/small/avatar-s-22.jpg'),
      'email': '<EMAIL>',
      'name': 'Delma Mewbourn',
      'country': 'Honduras',
      'role': 'staff',
      'status': 'deactivated',
      'is_verified': false,
      'department': 'development'
    },
    {
      'id': 311,
      'username': 'tarentine951',
      'avatar': require('@assets/images/portrait/small/avatar-s-17.jpg'),
      'email': '<EMAIL>',
      'name': 'Ja Alaibilla',
      'country': 'Italy',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 312,
      'username': 'mountainlike2',
      'avatar': require('@assets/images/portrait/small/avatar-s-22.jpg'),
      'email': '<EMAIL>',
      'name': 'Delinda Rosentrance',
      'country': 'Guinea',
      'role': 'user',
      'status': 'active',
      'is_verified': true,
      'department': 'development'
    },
    {
      'id': 313,
      'username': 'zoetic150',
      'avatar': require('@assets/images/portrait/small/avatar-s-18.jpg'),
      'email': '<EMAIL>',
      'name': 'Danae Demeter',
      'country': 'Gambia',
      'role': 'user',
      'status': 'deactivated',
      'is_verified': true,
      'department': 'sales'
    },
    {
      'id': 314,
      'username': 'addlepate37',
      'avatar': require('@assets/images/portrait/small/avatar-s-14.jpg'),
      'email': '<EMAIL>',
      'name': 'Kattie Joffrion',
      'country': 'Albania',
      'role': 'user',
      'status': 'blocked',
      'is_verified': true,
      'department': 'management'
    },
    {
      'id': 315,
      'username': 'pollinate51',
      'avatar': require('@assets/images/portrait/small/avatar-s-19.jpg'),
      'email': '<EMAIL>',
      'name': 'In Stjohns',
      'country': 'North Korea',
      'role': 'user',
      'status': 'active',
      'is_verified': false,
      'department': 'sales'
    },
    {
      'id': 316,
      'username': 'tournefortian626',
      'avatar': require('@assets/images/portrait/small/avatar-s-2.jpg'),
      'email': '<EMAIL>',
      'name': 'Van Laferney',
      'country': 'Finland',
      'role': 'staff',
      'status': 'active',
      'is_verified': true,
      'department': 'development'
    },
    {
      'id': 317,
      'username': 'aspersively497',
      'avatar': require('@assets/images/portrait/small/avatar-s-6.jpg'),
      'email': '<EMAIL>',
      'name': 'Sylvia Maharrey',
      'country': 'Turkmenistan',
      'role': 'staff',
      'status': 'deactivated',
      'is_verified': true,
      'department': 'sales'
    }

  ]
}


mock.onGet('/api/user-management/users').reply(() => {
  return [200, JSON.parse(JSON.stringify(data.users)).reverse()]
})

// GET: Fetch Single User Details
mock.onGet(/\/api\/user-management\/users\/\d+/).reply((request) => {

  const userId = request.url.substring(request.url.lastIndexOf('/') + 1)

  const user = data.users.find((user) => user.id == userId)

  return user ? [200, JSON.parse(JSON.stringify(user))] : [404]
})

// // POST : Add new Item
// mock.onPost("/api/data-list/products/").reply((request) => {

//   // Get event from post data
//   let item = JSON.parse(request.data).item

//   const length = data.products.length
//   let lastIndex = 0
//   if(length){
//     lastIndex = data.products[length - 1].id
//   }
//   item.id = lastIndex + 1

//   data.products.push(item)

//   return [201, {id: item.id}]
// })

// // Update Product
// mock.onPost(/\/api\/data-list\/products\/\d+/).reply((request) => {

//   const itemId = request.url.substring(request.url.lastIndexOf("/")+1)

//   let item = data.products.find((item) => item.id == itemId)
//   Object.assign(item, JSON.parse(request.data).item)

//   return [200, item]
// })

// // DELETE: Remove Item
mock.onDelete(/\/api\/user-management\/users\/\d+/).reply((request) => {

  const userId = request.url.substring(request.url.lastIndexOf('/') + 1)

  const itemIndex = data.users.findIndex((p) => p.id == userId)
  data.users.splice(itemIndex, 1)
  return [200]
})
