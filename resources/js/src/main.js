/*=========================================================================================
  File Name: main.js
  Description: main vue(js) file
  ----------------------------------------------------------------------------------------
  Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
  Author: Pixinvent
  Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/


import Vue from 'vue'
import App from './App.vue'

// Vuesax Component Framework
import Vuesax from 'vuesax'
import VueCookies from 'vue-cookies'

Vue.use(Vuesax)
Vue.use(VueCookies)


// axios
import axios from './axios.js'
Vue.prototype.$http = axios

// API Calls
import './http/requests'

// mock
import './fake-db/index.js'

// Theme Configurations
import '../themeConfig.js'


// Firebase
import '@/firebase/firebaseConfig'


// Auth0 Plugin
import AuthPlugin from './plugins/auth'
Vue.use(AuthPlugin)


// ACL
import acl from './acl/acl'


// Globally Registered Components
import './globalComponents.js'


// Vue Router
import router from './router'


// Vuex Store
import store from './store/store'


// i18n
import i18n from './i18n/i18n'


// Vuexy Admin Filters
import './filters/filters'


// Clipboard
import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)


// Tour
import VueTour from 'vue-tour'
Vue.use(VueTour)
require('vue-tour/dist/vue-tour.css')


// VeeValidate
import VeeValidate from 'vee-validate'
Vue.use(VeeValidate)


// Google Maps
import * as VueGoogleMaps from 'vue2-google-maps'
Vue.use(VueGoogleMaps, {
  load: {
    // Add your API key here
    key: 'AIzaSyB4DDathvvwuwlwnUu7F4Sow3oU22y5T1Y',
    libraries: 'places' // This is required if you use the Auto complete plug-in
  }
})

// Vuejs - Vue wrapper for hammerjs
import { VueHammer } from 'vue2-hammer'
Vue.use(VueHammer)

import EventBus from "./EventBus";
Vue.use(EventBus);



// PrismJS
import 'prismjs'


// Feather font icon
require('@assets/css/iconfont.css')


// Vue select css
// Note: In latest version you have to add it separately
// import 'vue-select/dist/vue-select.css';

// navigation guards before each
router.beforeEach((to, from, next) => {
  const notRequiresAuth = ['/', '/login', '/register', '/forgot-password', '/reset-password', '/admin/resumess', '/admin/resumess/statistic']
  const logged = JSON.parse(localStorage.getItem('userInfo'))
  const expiresAt = localStorage.getItem('expiresAt')
  const IsRequiresAuth = !notRequiresAuth.includes(to.path)

  if (!IsRequiresAuth) {
    next()
  } else if (logged) {
    if (new Date(expiresAt).getTime() < new Date().getTime()) {
      next({path: '/login'})
    }
    const api_token = localStorage.getItem('accessToken')
    axios.defaults.headers.Authorization = `Bearer ${api_token}`
    if (to.path === '/dashboard') {
      if (logged.userRole === 'admin') {
        next()
      } else {
        next({path: '/meeting'})
      }
    } else {
      next()
    }
    // }
  } else {
    next({path: '/login'}) // make sure to always call next()!
  }
})
// import { notification } from './helpers/notification'

// axios.interceptors.response.use(
//   response => {
//     return response
//   },
//   error => {
//     if (error.response && error.response.status === 401) {
//       router.push('/login').catch(() => {})
//     }
//     if (error.response && error.response.status === 403) {
//       router.push('/').catch(() => {})
//       notification(new Vue({
//         router,
//         store,
//         i18n,
//         acl,
//         render: h => h(App)
//       }).$mount('#app'),
//       'warning',
//       'Permission',
//       'You are not permission'
//       )

//       // self.$vs.notify({
//       //   color: 'warning'
//       //   title:,
//       //   text
//       // })

//     }
//     return Promise.reject(error)
//   })
//

Vue.config.productionTip = false

new Vue({
  router,
  store,
  i18n,
  acl,
  render: h => h(App)
}).$mount('#app')
