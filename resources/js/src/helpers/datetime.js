import moment from 'moment'

// Format datetime
export function formatDateTime (dateTime = null) {
  return dateTime ? moment(dateTime).format('DD-MM-YYYY HH:mm') : moment().format('DD-MM-YYYY HH:mm')
}

// Format datetime save
export function formatDateTimeSave (dateTime) {
  return moment(dateTime, 'DD-MM-YYYY HH:mm').format('YYYY-MM-DD HH:mm:ss')
}

// Format date save
export function formatDateSave (dateTime) {
  return moment(dateTime, 'DD/MM/YYYY').format('YYYY-MM-DD')
}

// Format datetime display
export function formatDateTimeDisplay (dateTime) {
  return moment(dateTime).format('DD-MM-YYYY HH:mm')
}

export function formatDateDisplay (dateTime) {
  return moment(dateTime).format('DD-MM-YYYY')
}

export function formatDateDisplay2 (dateTime) {
  return moment(dateTime).format('DD/MM/YYYY')
}


export function formatTime (date = null) {
  return moment(date).format('hh:mm A')
}
