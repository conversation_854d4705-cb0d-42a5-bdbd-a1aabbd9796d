export function setPagination (pagination, item) {
  pagination.total = pagination.total - (Array.isArray(item) ? item.length : 1)
  const perPage = parseInt(pagination.per_page)
  const totalPage = Math.ceil(pagination.total / perPage)
  if (pagination.current_page >= totalPage) {
    pagination.current_page = totalPage
    pagination.from = pagination.current_page !== 1 ? ((pagination.current_page - 1) * perPage) + 1 : 1
    pagination.to = pagination.total
  }
  return pagination
}