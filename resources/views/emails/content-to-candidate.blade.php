
        <tr style="border-collapse:collapse">
            <td style="padding: 0; margin: 0; position: relative; background-repeat: no-repeat; background-position: right top !important; background-size: 18%;" align="left" background="<?php echo $candidate->job->hasHackerrank() ? 'https://assets.topdev.vn/static/assets/desktop/images/HackerRank_Badge.png' : '' ?>">
                <p style="font-family:Helvetica, Arial, sans-serif; color:#374550">Xin chào <strong>{{$candidate->resume->display_name}}, </strong></p>

                <p style="text-align: justify font-family:Helvetica, Arial, sans-serif;font-size:14px;line-height:20px;text-align:left;color:#333333">
                <strong>TopDev</strong> ghi nhận bạn đã ứng tuyển vị trí <strong><span style="color: #d34127;">{{ $candidate->job->title}}</span></strong> tại <a href="{{ utm_generate($candidate->job->owner->detail_url, ['utm_source' => 'email', 'utm_term' => $candidate->job->owner->display_name, 'utm_medium' => 'candidate-confirm']) }}" style="text-decoration:none" ><strong>{{ $candidate->job->owner->display_name}}</strong></a>. Bạn có thể xem lại chi tiết công việc <a href="{{ utm_generate($candidate->job->detail_url, ['utm_source' => 'email', 'utm_term' => $candidate->job->title, 'utm_medium' => 'candidate-confirm']) }}">tại đây</a>.
                </p>

                @if ($candidate->job->hasHackerrank())
                    @php
                        $hkr_name = empty($candidate->job->hackerrank) ? null : $candidate->job->hackerrank['name'];
                    @endphp
                    @if (!empty($hkr_name))
                        <p style="text-align: justify">
                            <span style="font-family:arial,helvetica,sans-serif;font-size:14px;color:#333333;">
                                <span style="color:#d94127"><em><strong>Notice</strong></em></span>:
                            </span>
                            <span style="font-family:arial,helvetica,sans-serif;font-size:14px;">Vị trí này ưu tiên ứng viên hoàn thành <strong style="color:#000">HackerRank Test</strong> sau khi ứng tuyển. Bài Test <strong>{{ $hkr_name }}</strong> sẽ được gửi đến bạn trong email tiếp theo.</span>
                        </p>
                    @endif
                @endif

                {{-- @if(false)
                <p style="text-align: justify">
                    Bên cạnh đó, để cảm ơn bạn đã tin tưởng lựa chọn sử dụng dịch vụ, TopDev xin gửi tặng bạn 1 vé tham dự sự kiện <strong><a href="https://vietnamwebsummit.com/vi/home/">Vietnam Web Summit 2017 (VWS 2017)</a></strong> do TopDev tổ chức, diễn ra tại HN vào ngày <strong>8/12/2017</strong>
                </p>
                <p style="text-align: justify">
                    Thông tin chi tiết về vé tặng cũng như chương trình VWS 2017 bộ phận hỗ trợ từ sự kiện sẽ gửi đến bạn qua email trong vòng 24h tới.
                </p>
                <p style="text-align: justify">
                    <span style="color: #e44b31">(*)</span> Lưu ý: mỗi ứng viên chỉ nhận được 1 vé mời tham dự chương trình VWS 2017 khi ứng tuyển thành công trên hệ thống của TopDev.
                </p>
                @endif --}}
                <p style="text-align: justify">
                    Đính kèm email này là <a href="{{ !current($candidate->files_cv_url) ? current($candidate->resume->files_cv_url) : current($candidate->files_cv_url)}}" style="text-decoration: none; font-weight: 600;">liên kết đường dẫn hồ sơ</a> bạn đã ứng tuyển cho vị trí này, nếu có sai sót hay cần thay đổi, hãy liên hệ <b>TopDev</b> để được hỗ trợ ngay: <a href="mailto:<EMAIL>"><EMAIL></a> hoặc <strong>(028) 6273 3496</strong>
                </p>

                <p style="font-style: italic;padding-left:20px;"> TopDev Support Team, </p>
            </td>
        </tr>

        @if (!empty($suggestJobs) && count($suggestJobs) > 0)

        @include('emails.content-to-recommend-job')

        @endif

