<tr style="border-collapse:collapse">
    <td align="left" style="padding:0;Margin:0;padding-left:20px;padding-right:20px">
        <table cellpadding="0" cellspacing="0" width="100%" style="border-collapse:collapse;border-spacing:0px">
            <tbody>
                <tr style="border-collapse:collapse">
                    <td align="center" valign="top" style="padding:0;Margin:0;width:560px">
                        <table cellpadding="0" cellspacing="0" width="100%" style="border-collapse:collapse;border-spacing:0px">
                            <tbody>
                                <tr style="border-collapse:collapse">
                                    <td align="center" style="padding:0;Margin:0;padding-top:20px;padding-bottom:20px;font-size:0">
                                        <table border="0" width="100%" height="100%" cellpadding="0" cellspacing="0" style="border-collapse:collapse;border-spacing:0px">
                                            <tbody>
                                                <tr style="border-collapse:collapse">
                                                    <td style="padding:0;Margin:0;border-bottom:1px solid #cccccc;background:none;height:1px;width:100%;margin:0px"></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    </td>
</tr>
@if(isset($hidebutton))
<tr style="border-collapse:collapse">
    <td align="left" style="padding:0;Margin:0;padding-left:20px;padding-right:20px">
        <h2 style="text-align:center;margin:20px 0">NHỮNG CÔNG VIỆC HẤP DẪN TẠI <strong style="color:#d34127;">TOPDEV</strong>
        </h2>
    </td>
</tr>
@endif
<!-- jobs-recomend -->
<tr style="border-collapse:collapse">
    <td align="left" style="Margin:0;padding-top:5px;padding-bottom:5px;padding-left:20px;padding-right:30px">
        <table cellpadding="0" cellspacing="0" width="100%" style="border-collapse:collapse;border-spacing:0px">
            <tbody>
                @foreach(array_chunk($suggestJobs ?? [], 2) as $key => $jobs)
                <tr style="border-collapse:collapse">
                    <td align="center" valign="top" style="padding:0;Margin:0;width:550px">
                        <table cellpadding="0" cellspacing="0" width="100%" style="border-collapse:collapse;border-spacing:0px">
                            <tbody>
                                <tr style="border-collapse:collapse">
                                    <td align="left" style="Margin:0;padding-top:5px;padding-bottom:5px;padding-left:15px;padding-right:15px">
                                        <a href="{{$jobs[0]['detail_url'] ?? '#'}}" style="text-decoration:none;">
                                            <p style="Margin:0;font-size:14px;font-family:arial,'helvetica neue',helvetica,sans-serif;line-height:21px;color:#333333">
                                                <span style="color:#0000cd">{{$jobs[0]['title'] ?? 'Job title'}}</span><br>
                                                <span style="font-size:12px">{{$jobs[0]['company_name'] ?? ''}} - {{$jobs[0]['addresses']['address_region_list'] ?? ''}}
                                                    <br>Salary: {{$jobs[0]['salary']['value'] ?? 'Negotiable'}}
                                                    <br>{{$jobs[0]['skills_str'] ?? ''}}
                                                </span>
                                            </p>
                                        </a>
                                    </td>
                                </tr>
                                @if(isset($jobs[1]))
                                <tr style="border-collapse:collapse">
                                    <td align="left" bgcolor="#f6f6f6" style="Margin:0;padding-top:5px;padding-bottom:5px;padding-left:15px;padding-right:15px">
                                        <a href="{{$jobs[1]['detail_url'] ?? '#'}}" style="text-decoration:none;">
                                            <p style="Margin:0;font-size:14px;font-family:arial,'helvetica neue',helvetica,sans-serif;line-height:21px;color:#333333">
                                                <span style="color:#0000cd">{{$jobs[1]['title'] ?? 'Job title'}}</span><br>
                                                <span style="font-size:12px">{{$jobs[1]['company_name'] ?? ''}} - {{$jobs[1]['addresses']['address_region_list'] ?? ''}}
                                                    <br>Salary: {{$jobs[1]['salary']['value'] ?? 'Negotiable'}}
                                                    <br>{{$jobs[1]['skills_str'] ?? ''}}
                                                </span>
                                            </p>
                                        </a>
                                    </td>
                                </tr>
                                @endif
                            </tbody>
                        </table>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </td>
</tr>
@if(isset($hidebutton) && isset($short_address))
<tr style="border-collapse:collapse">
    <td align="left" style="padding:0;Margin:0;padding-bottom:20px;padding-left:20px;padding-right:20px">
        <table cellpadding="0" cellspacing="0" align="left" style="border-collapse:collapse;border-spacing:0px;float:left">
            <tbody>
                <tr style="border-collapse:collapse">
                    <td valign="top" align="center" style="padding:0;Margin:0;width:180px">
                        <table cellpadding="0" cellspacing="0" width="100%" style="border-collapse:collapse;border-spacing:0px">
                            <tbody>
                                <tr style="border-collapse:collapse">
                                    <td align="left" style="padding:0;Margin:0">
                                        <p style="Margin:0;font-size:14px;font-family:arial,'helvetica neue',helvetica,sans-serif;line-height:21px;color:#333333"><br></p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        <table cellpadding="0" cellspacing="0" align="center" style="border-collapse:collapse;border-spacing:0px">
            <tbody>
                <tr style="border-collapse:collapse">
                    <td align="left" style="padding:0;Margin:0;width:360px">
                        <table cellpadding="0" cellspacing="0" width="100%" style="border-collapse:collapse;border-spacing:0px">
                            <tbody>
                                <tr style="border-collapse:collapse">
                                    <td align="right" style="padding:0;Margin:0">
                                        <p style="Margin:0;font-size:12px;font-family:arial,'helvetica neue',helvetica,sans-serif;line-height:18px;color:#696969">
                                            <em>
                                                <a href="{{utm_generate('https://topdev.vn/it-jobs?cid=' . $short_address, $utm ?? null)}}" target="_blank" style="text-decoration:none;font-family:arial,'helvetica neue',helvetica,sans-serif;" >Tổng hợp việc làm tại {{$short_address}}</a>
                                            </em>
                                        </p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    </td>
</tr>
@endif
<!-- end jobs-recomend -->
@if(!isset($hidebutton))
<!-- button -->
<tr style="border-collapse:collapse">
    <td align="left" style="padding:0;Margin:0;padding-top:20px;padding-left:20px;padding-right:20px">

        @if(isset($urlSaveJob))
        <table cellpadding="0" cellspacing="0" align="left" class="btn__save" style="border-collapse:collapse;border-spacing:0px;margin-bottom: 10px !important;width: 25%;">
            <tbody>
                <tr style="border-collapse:collapse">
                    <td valign="top" align="center" style="padding:0;Margin:0;width:180px;">
                        <table cellpadding="0" cellspacing="0" width="100%" style="border-collapse:collapse;border-spacing:0px">
                            <tbody>
                                <tr style="border-collapse:collapse">
                                    <td align="center" style="padding:0;Margin:0">
                                        <span style="border-style:solid;border-color:#666666;background:#ffffff;border-width:2px;display:block;border-radius:0px;width:auto">
                                            <a class="a__btn" style="text-decoration:none;font-family:arial,'helvetica neue',helvetica,sans-serif;font-size:16px;color:#333333;border-style:solid;border-color:#ffffff;border-width:10px 55px;display:block;background:#ffffff;border-radius:0px;font-weight:normal;font-style:normal;line-height:19px;width:auto;text-align:center;border-width: 10px 10px !important;font-size: 12px!important;" href="{{$urlSaveJob ?? '#'}}">
                                                Lưu
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        @endif

        @if(isset($urlApplyJobs))
        <table cellpadding="0" cellspacing="0" align="<?php echo isset($urlSaveJob) ? 'right' : 'center' ?>" class="btn__finds" style="border-collapse:collapse;border-spacing:0px; @if(isset($urlSaveJob)) {{"margin-left: 10px !important;"}} @endif width: calc(75% - 10px);">
            <tbody>
                <tr style="border-collapse:collapse">
                    <td align="left" style="padding:0;Margin:0;width:360px">
                        <table cellpadding="0" cellspacing="0" width="100%" style="border-collapse:collapse;border-spacing:0px">
                            <tbody>
                                <tr style="border-collapse:collapse">
                                    <td align="center" style="padding:0;Margin:0">
                                        <span style="border-style:solid;border-color:#d34127;background:#ffffff;border-width:2px;display:block;border-radius:0px;width:auto">
                                            <a href="{{$urlApplyJobs ?? '#'}}" class="a__btn" style="text-decoration:none;font-family:arial,'helvetica neue',helvetica,sans-serif;font-size:16px;color:#d34127;border-style:solid;border-color:#ffffff;border-width:10px 55px;display:block;background:#ffffff;border-radius:0px;font-weight:bold;font-style:normal;line-height:19px;width:auto;text-align:center;border-width: 10px 8px !important;font-size: 12px !important;">
                                                Ứng tuyển nhanh tất cả
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        @elseif(isset($urlSearch))
        <table cellpadding="0" cellspacing="0" align="<?php echo isset($urlSaveJob) ? 'right' : 'center' ?>" class="btn__finds" style="border-collapse:collapse;border-spacing:0px; @if(isset($urlSaveJob)) {{"margin-left: 10px !important;"}} @endif width: calc(75% - 10px);">
            <tbody>
                <tr style="border-collapse:collapse">
                    <td align="left" style="padding:0;Margin:0;width:360px">
                        <table cellpadding="0" cellspacing="0" width="100%" style="border-collapse:collapse;border-spacing:0px">
                            <tbody>
                                <tr style="border-collapse:collapse">
                                    <td align="center" style="padding:0;Margin:0">
                                        <span style="border-style:solid;border-color:#d34127;background:#ffffff;border-width:2px;display:block;border-radius:0px;width:auto">
                                            <a href="{{$urlSearch ?? '#'}}" class="a__btn" style="text-decoration:none;font-family:arial,'helvetica neue',helvetica,sans-serif;font-size:16px;color:#d34127;border-style:solid;border-color:#ffffff;border-width:10px 55px;display:block;background:#ffffff;border-radius:0px;font-weight:bold;font-style:normal;line-height:19px;width:auto;text-align:center;border-width: 10px 8px !important;font-size: 12px !important;">
                                                Tìm việc làm phù hợp hơn >>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        @endif

    </td>
</tr>
<!-- end button -->
<tr style="border-collapse:collapse">
    <td align="left" style="padding:0;Margin:0;padding-bottom:20px;padding-left:20px;padding-right:20px">

        @if(isset($urlSaveJob) || isset($urlSearch) && isset($urlApplyJobs))
        <table cellpadding="0" cellspacing="0" align="<?php echo isset($urlSaveJob) ? (isset($urlCreateCvOnline) ? 'left' : 'right') : 'center' ?>" style="border-collapse:collapse;border-spacing:0px;">
            <tbody>
                <tr style="border-collapse:collapse">
                    <td valign="top" align="center" style="padding:0;Margin:0;width:185px">
                        <table cellpadding="0" cellspacing="0" width="100%" style="border-collapse:collapse;border-spacing:0px">
                            <tbody>
                                <tr style="border-collapse:collapse">
                                    <td align="left" style="padding:0;Margin:0">
                                        <p style="Margin:0;font-size:12px;font-family:arial,'helvetica neue',helvetica,sans-serif;line-height:18px;color:#696969">
                                        @if(isset($urlSearch) && isset($urlApplyJobs))
                                            <em style="margin-left: 10px !important;" >
                                                <a href="{{$urlSearch ?? '#'}}" style="text-decoration:none;font-family:arial,'helvetica neue',helvetica,sans-serif;" >Tìm việc làm phù hợp hơn >></a>
                                            </em>
                                        @endif
                                        </p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        @endif

        @if(isset($urlCreateCvOnline))
        <table cellpadding="0" cellspacing="0" align="<?php echo isset($urlSaveJob) ? 'right' : 'center' ?>" style="border-collapse:collapse;border-spacing:0px">
            <tbody>
                <tr style="border-collapse:collapse">
                    <td align="left" style="padding:0;Margin:0;width:360px">
                        <table cellpadding="0" cellspacing="0" width="100%" style="border-collapse:collapse;border-spacing:0px">
                            <tbody>
                                <tr style="border-collapse:collapse">
                                    <td align="center" style="padding:0;Margin:0">
                                        <p style="Margin:0;font-size:12px;font-family:arial,'helvetica neue',helvetica,sans-serif;line-height:18px;color:#696969">
                                            <em>
                                                <a href="{{$urlCreateCvOnline ?? '#'}}" style="text-decoration:none;font-family:arial,'helvetica neue',helvetica,sans-serif;" >Chưa có CV ứng tuyển, tạo ngay trên TopDev</a>
                                            </em>
                                        </p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        @endif

    </td>
</tr>
@endif
