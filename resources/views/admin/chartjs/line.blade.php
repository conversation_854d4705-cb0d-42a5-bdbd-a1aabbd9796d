<div class="w-100">
    <form id="chart-line-time" class="hidden">
        <div class="row">
            <div class="col-12 col-sm-6 col-md-4">
                <div class="form-group">
                    <label>Start Date</label>
                    <input type="text" id="chart-from-date" name="from_date" class="form-control"
                        value="{{ \Carbon\Carbon::today()->subDays(6)->format('d/m/Y') }}" />
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
                <div class="form-group">
                    <label>End Date</label>
                    <input type="text" id="chart-to-date" class="form-control" name="to_date"
                        value="{{ \Carbon\Carbon::today()->format('d/m/Y') }}" />
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
                <div class="form-group">
                    <label>Period</label>
                    <select class="form-control select2-period" name="period">
                        @foreach(config('constant.STATISTICAL_USER_CHART.PERIOD') as $text => $value)
                        <option value="{{ $text }}">{{ ucFirst($text) }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-12">
                <div class="col-12 col-sm-6 col-md-4">
                    <div class="form-group">
                        <button class="btn btn-primary btn-block btn-submit" type="submit">Submit</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="w-100" style="position: relative">
    <div id="chart-line-apply-loading" class="d-none align-items-center justify-content-center overlay"
        style="background-color: #fff;">
        <i class="fa fa-spinner fa-4x fa-spin m-auto" aria-hidden="true"></i>
    </div>

    <canvas id="line" style="width: 100%;">
    </canvas>

    <div id="chart-line-chart-error" class="d-none align-items-center justify-content-center overlay"
        style="background-color: rgba(255, 255, 255, 0.5);">
        <span class="text-danger text-center px-15 py-10"
            style="margin: auto; background-color: rgba(255, 255, 255, 1);">Have errors while loading because
            <strong>Timeout</strong>!.</span>
    </div>
</div>

<script>
$(function () {
    var lineApplyChart;

    $(document).ready(function() {
        $.ajax({
            url: "/admin/ajax/accounts/token",
            method: 'GET',
            data: {
            },
        })
        .done(function(token){
            initChart(false, token);
            $('#chart-line-time').on('submit', function(e) {
                e.preventDefault();
                initChart(true, token);
                $('#location_flag').attr('value',false);
            });
        });
    });

    $('#chart-from-date').datetimepicker({
        format: 'DD-MM-YYYY',
    });

    $('#chart-to-date').datetimepicker({
        format: 'DD-MM-YYYY',
        useCurrent: false,
    });

    $("#chart-from-date").on("dp.change", function (e) {
        $('#to-date').data("DateTimePicker").minDate(e.date);
    });
    $("#chart-to-date").on("dp.change", function (e) {
        $('#from-date').data("DateTimePicker").maxDate(e.date);
    });

    function getLineChartData(startDate, endDate, period, token) {
        return $.ajax({
            url: "https://accounts.topdev.vn/api/authenlogs/reports/",
            headers: {
                "Content-Type" : 'application/json',
                "Authorization" : "Bearer "+token
            },
            method: 'GET',
            data: {
                'from_date': startDate,
                'to_date': endDate,
                'period': period
            },
        });
    }

    window.chartColors = {
        red: 'rgb(255, 99, 132)',
        orange: 'rgb(255, 159, 64)',
        yellow: 'rgb(255, 205, 86)',
        green: 'rgb(75, 192, 192)',
        blue: 'rgb(54, 162, 235)',
        purple: 'rgb(153, 102, 255)',
        grey: 'rgb(201, 203, 207)'
    };

    var ctx = document.getElementById('line').getContext('2d');

    function initChart(updateChart = false, token) {
        $('#chart-line-chart-error').removeClass('d-flex').addClass('d-none');
        $('#chart-line-apply-loading').removeClass('d-none').addClass('d-flex');

        let data = $('#chart-line-time').serializeArray();
        let ajaxList = [];

        ajaxList.push(getLineChartData(data[0].value, data[1].value, data[2].value, token));

        Promise.all(ajaxList)
        .then((dataList) => {
            let dataChart = dataList[0].data;
            let labels = [];
            let loginCount = [];
            let userLoginCount = [];
            let firstLoginCount = [];
            let userFirstLoginCount = [];

            $.each(dataChart,function(index,value){
                labels = labels.concat(index);
                loginCount = loginCount.concat(value.login_count);
                userLoginCount = userLoginCount.concat(value.user_login_count);
                firstLoginCount = firstLoginCount.concat(value.first_login_count);
                userFirstLoginCount = userFirstLoginCount.concat(value.user_first_login_count);
            });

            $('#chart-line-apply-loading').removeClass('d-flex').addClass('d-none');
            createChart(labels, loginCount, userLoginCount, firstLoginCount, userFirstLoginCount, updateChart);
        })
        .catch((errorList) => {
            $('#chart-line-chart-error').removeClass('d-none').addClass('d-flex');
        })
    }

    function createChart(labels, loginCount, userLoginCount, firstLoginCount, userFirstLoginCount, updateChart = false) {
        var lineChartData = {
            labels: labels,
            datasets: [{
                label: 'Login',
                backgroundColor: window.chartColors.red,
                borderColor: window.chartColors.red,
                data: loginCount,
                fill: false,
            }, {
                label: 'User login',
                fill: false,
                backgroundColor: window.chartColors.blue,
                borderColor: window.chartColors.blue,
                data: userLoginCount,
            }, {
                label: 'First login',
                fill: false,
                backgroundColor: window.chartColors.yellow,
                borderColor: window.chartColors.yellow,
                data: firstLoginCount,
            }, {
                label: 'User first login',
                fill: false,
                backgroundColor: window.chartColors.purple,
                borderColor: window.chartColors.purple,
                data: userFirstLoginCount,
            }]
        };

        if(!updateChart) {
            $('#chart-line-time').removeClass('hidden');
            $('.select2-period').select2({
                minimumResultsForSearch: -1
            });

            var ctx = document.getElementById('line').getContext('2d');
            lineApplyChart = new Chart(ctx, {
                type: 'line',
                data: lineChartData,
                options: {
                    responsive: true,
                    tooltips: {
                        mode: 'index',
                        intersect: false,
                    },
                    hover: {
                        mode: 'nearest',
                        intersect: true
                    },
                    scales: {
                        xAxes: [{
                            display: true,
                            scaleLabel: {
                                display: true,
                                labelString: 'Month'
                            }
                        }],
                        yAxes: [{
                            display: true,
                            scaleLabel: {
                                display: true,
                                labelString: 'Value'
                            }
                        }]
                    }
                },
            });
        } else {
            lineApplyChart.data = lineChartData;
            lineApplyChart.update();
        }
    }
});
</script>
