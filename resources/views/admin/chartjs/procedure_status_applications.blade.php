<style>
    .d-flex {
        display: flex;
    }

    .flex-wrap {
        flex-wrap: wrap;
    }

    .dn {
        display: none;
    }

    .overlay {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    .align-items-center {
        align-items: center;
    }

    .justify-content {
        justify-content: center;
    }

    @media (min-width: 768px) {
        .btn-submit {
            margin-top: 25px;
        }
    }

    .row.d-flex > [class^="col-"] {
        width: 100%;
        flex-grow: 1;
    }

    @media (min-width: 992px) {
        .row.d-flex > .col-md-6 {
            width: 50%;
        }
    }

    .tooltip-inner {
        background-color: #FFFFFF; /* <PERSON><PERSON><PERSON> nền */
        color: #6D6D6D; /* <PERSON><PERSON><PERSON> chữ */
        font-size: 12px; /* <PERSON><PERSON><PERSON> thước chữ */
        border-radius: 5px; /* Bo góc */
        text-align: left;
        border: 1px solid #C2C2C2
    }

</style>
<div class="w-100">
    <div class="row">
        <div class="col-12 col-sm-6 col-md-6">
            <div class="form-group">
                <label>Start Date</label>
                <input type="text" id="from_date_procedure" name="from_date_procedure" class="form-control"
                       value="{{ \Carbon\Carbon::create(2024, 5, 1)->format('d/m/Y') }}"/>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-6">
            <div class="form-group">
                <label>End Date</label>
                <input type="text" id="to_date_procedure" class="form-control" name="to_date_procedure"
                       value="{{ \Carbon\Carbon::today()->format('d/m/Y') }}"/>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-6">
            <div class="form-group">
                <label>Job</label>
                <select class="form-control" name="job_procedure" id="job_procedure">
                </select>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-6">
            <div class="form-group">
                <label>Company</label>
                <select class="form-control select2" id="company_procedure" name="company_procedure">
                </select>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-sm-6 col-md-4 invisible">
        </div>
        <div class="col-12 col-sm-6 col-md-4 invisible">
        </div>
        <div class="col-12 col-sm-6 col-md-4 d-flex" style="justify-content: end">
            <div class="form-group">

                <button class="btn btn-primary btn-block" id="btn_search_procedure">
                    <i class="fa fa-spinner fa-spin fa-fw dn" id="spinner_submit"></i>
                    Submit
                </button>
            </div>
            <div class="form-group" style="margin-left: 5px">
                <button class="btn btn-default btn-block" id="btn_reset_procedure">
                    <i class="fa fa-repeat"></i>
                    Reset
                </button>
            </div>
        </div>
    </div>
    <hr style="margin-bottom: 40px;">
    <div id="build_procedure_status">
    </div>

</div>

<script>
    $(function () {
        // Const Defined
        const fromDate = '01/05/2024';
        const elFromDate = $('#from_date_procedure');
        const elToDate = $('#to_date_procedure');
        const elJobProcedure = $('#job_procedure');
        const elCompanyProcedure = $('#company_procedure');
        const btnResetProcedure = $('#btn_reset_procedure');
        const btnSubmitProcedure = $('#btn_search_procedure');
        const spinnerSubmit = $('#spinner_submit');
        const buildProcedureStatus = $('#build_procedure_status');
        // Defined function
        const formatDate = (date) => {
            const day = String(date.getDate()).padStart(2, '0'); // Lấy ngày và đảm bảo có 2 chữ số
            const month = String(date.getMonth() + 1).padStart(2, '0'); // Lấy tháng (cộng thêm 1 vì tháng trong JS bắt đầu từ 0)
            const year = date.getFullYear(); // Lấy năm
            return `${day}/${month}/${year}`; // Trả về chuỗi định dạng d/m/Y
        };
        const ajaxSearchDataProcedure = (formData) => {
            $.ajax({
                url: '/admin/ajax/search-status-procedure',
                type: 'GET',
                data: formData,
                dataType: 'json',
                beforeSend: function () {
                    spinnerSubmit.removeClass('dn');
                    btnSubmitProcedure.prop('disabled', true);
                    buildProcedureStatus.html(`<div id="procedure-loading" style="min-height:365px;text-align: center;background-color: #fff;padding-top: 15%;">
                                                <i class="fa fa-spinner fa-3x fa-spin m-auto" aria-hidden="true"></i>
                                            </div>`)
                },
                success: function (response) {
                    // Xử lý dữ liệu nhận được từ server
                    const html = `
                                <div class="row" id="container_procedure">
            <div class="col-12 col-sm-6 col-md-4">
                <div class="" style="background-color: #F6F6F6;margin-left: 16px;border-radius: 8px;height: 76px;">
                    <div style="margin-left: 16px;padding-top: 12px;">
                        <span>Companies used</span>
                        <i class="fa fa-question-circle-o" data-toggle="tooltip" data-placement="bottom"
                           title="The number of companies have at least 1 application marked with a procedure status."></i>
                    </div>
                    <div style="margin-top: 4px;margin-left: 16px;">
                        <span style="font-size: 24px;font-weight: 700">${response.companies_used || 0}</span>
                        <div
                            style="padding: 2px 4px 2px 4px;float: right;margin-right: 16px;background-color: #E1E1E1;border-radius: 8px;">
                            <span style="">${response.ratio_companies_used || 0}%</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
                <div class="" style="background-color: #F6F6F6;border-radius: 8px;height: 76px;">
                    <div style="margin-left: 16px;padding-top: 12px;">
                        <span>Jobs are marked</span>
                        <i class="fa fa-question-circle-o" data-toggle="tooltip" data-placement="bottom"
                           title="The number of jobs have at least 1 application marked with procedure status."></i>
                    </div>
                    <div style="margin-top: 4px;margin-left: 16px;">
                        <span style="font-size: 24px;font-weight: 700">${response.jobs_marked || 0}</span>
                        <div
                            style="padding: 2px 4px 2px 4px;float: right;margin-right: 16px;background-color: #E1E1E1;border-radius: 8px;">
                            <span style="">${response.ratio_jobs_marked || 0}%</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
                <div class="" style="background-color: #F6F6F6;border-radius: 8px;height: 76px;margin-right: 16px;">
                    <div style="margin-left: 16px;padding-top: 12px;">
                        <span>Applications are marked</span>
                        <i class="fa fa-question-circle-o" data-toggle="tooltip" data-placement="bottom"
                           title="The number of applications has procedure status."></i>

                    </div>
                    <div style="margin-top: 4px;margin-left: 16px;">
                        <span style="font-size: 24px;font-weight: 700">${response.applications_marked || 0}</span>
                        <div
                            style="padding: 2px 4px 2px 4px;float: right;margin-right: 16px;background-color: #E1E1E1;border-radius: 8px;">
                            <span style="">${response.ratio_applications_marked || 0}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" id="process_bar_procedure">
            <div style="margin-left: 20px;margin-right: 20px;margin-top: 32px;">
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: ${response.procedure.ratio_proceed_interview}%;background-color: #78B7FF"
                         aria-valuenow="15" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="progress-bar" role="progressbar" style="width: ${response.procedure.ratio_interviewed_passed}%;background-color: #2C6FB1"
                         aria-valuenow="30" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="progress-bar" role="progressbar" style="width: ${response.procedure.ratio_offer}%;background-color: #18A382"
                         aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="progress-bar" role="progressbar" style="width: ${response.procedure.ratio_hired}%;background-color: #F9C034"
                         aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="progress-bar" role="progressbar" style="width: ${response.procedure.ratio_not_matching}%;background-color: #C53837"
                         aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="progress-bar" role="progressbar" style="width: ${response.procedure.ratio_interviewed_failed}%;background-color: #EB782D"
                         aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="progress-bar" role="progressbar" style="width: ${response.procedure.ratio_failed}%;background-color: #A7A3FF"
                         aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                    <div class="progress-bar" role="progressbar" style="width: ${response.procedure.ratio_not_start}%;background-color: #CACACA"
                         aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
        </div>
        <div class="row" id="table_data_procedure">
            <div class="col-12 col-sm-6 col-md-6">
                <table class="table">
                    <thead>
                    <tr>
                        <th style="border: 0;">Status</th>
                        <th style="border: 0;text-align: right">Volume</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td style="border: 0;">
                            <i class="fa fa-circle" aria-hidden="true" style="color: #78B7FF;"></i>
                            <span>Proceed interview</span>
                        </td>
                        <td style="border: 0;text-align: right">
                            <div style="
    display: flex;
    justify-content: flex-end;
">
                                <div style="
    padding: 2px 4px 2px 4px;margin-right:8px;
">${response.procedure.proceed_interview || 0}
                                </div>
                                <div
                                    style="padding: 2px 4px 2px 4px;float: right;background-color: #E1E1E1;border-radius: 8px;">
                                    <span style="">${response.procedure.ratio_proceed_interview || 0}%</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="border: 0;">
                            <i class="fa fa-circle" aria-hidden="true" style="color: #2C6FB1;"></i>
                            <span>Passed interview</span>
                        </td>
                        <td style="border: 0;text-align: right">
                            <div style="
    display: flex;
    justify-content: flex-end;
">
                                <div style="
    padding: 2px 4px 2px 4px;margin-right:8px;
">${response.procedure.interviewed_passed || 0}
                                </div>
                                <div
                                    style="padding: 2px 4px 2px 4px;float: right;background-color: #E1E1E1;border-radius: 8px;">
                                    <span style="">${response.procedure.ratio_interviewed_passed || 0}%</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="border: 0;">
                            <i class="fa fa-circle" aria-hidden="true" style="color: #18A382;"></i>
                            <span>Offer</span>
                        </td>
                        <td style="border: 0;text-align: right">
                            <div style="
    display: flex;
    justify-content: flex-end;
">
                                <div style="
    padding: 2px 4px 2px 4px;margin-right:8px;
">${response.procedure.offer || 0}
                                </div>
                                <div
                                    style="padding: 2px 4px 2px 4px;float: right;background-color: #E1E1E1;border-radius: 8px;">
                                    <span style="">${response.procedure.ratio_offer || 0}%</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="border: 0;">
                            <i class="fa fa-circle" aria-hidden="true" style="color: #F9C034;"></i>
                            <span>Hired</span>
                        </td>
                        <td style="border: 0;text-align: right">
                            <div style="
    display: flex;
    justify-content: flex-end;
">
                                <div style="
    padding: 2px 4px 2px 4px;margin-right:8px;
">${response.procedure.hired || 0}
                                </div>
                                <div
                                    style="padding: 2px 4px 2px 4px;float: right;background-color: #E1E1E1;border-radius: 8px;">
                                    <span style="">${response.procedure.ratio_hired || 0}%</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="col-12 col-sm-6 col-md-6">
                <table class="table">
                    <thead>
                    <tr>
                        <th style="border: 0;">Status</th>
                        <th style="border: 0;text-align: right">Volume</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td style="border: 0;">
                            <i class="fa fa-circle" aria-hidden="true" style="color: #C53837;"></i>
                            <span>Not matching</span>
                        </td>
                        <td style="border: 0;text-align: right">
                            <div style="
    display: flex;
    justify-content: flex-end;
">
                                <div style="
    padding: 2px 4px 2px 4px;margin-right:8px;
">${response.procedure.not_matching || 0}
                                </div>
                                <div
                                    style="padding: 2px 4px 2px 4px;float: right;background-color: #E1E1E1;border-radius: 8px;">
                                    <span style="">${response.procedure.ratio_not_matching || 0}%</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="border: 0;">
                            <i class="fa fa-circle" aria-hidden="true" style="color: #EB782D;"></i>
                            <span>Failed interview</span>
                        </td>
                        <td style="border: 0;text-align: right">
                            <div style="
    display: flex;
    justify-content: flex-end;
">
                                <div style="
    padding: 2px 4px 2px 4px;margin-right:8px;
">${response.procedure.interviewed_failed || 0}
                                </div>
                                <div
                                    style="padding: 2px 4px 2px 4px;float: right;background-color: #E1E1E1;border-radius: 8px;">
                                    <span style="">${response.procedure.ratio_interviewed_failed || 0}%</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="border: 0;">
                            <i class="fa fa-circle" aria-hidden="true" style="color: #A7A3FF;"></i>
                            <span>Failed</span>
                        </td>
                        <td style="border: 0;text-align: right">
                            <div style="
    display: flex;
    justify-content: flex-end;
">
                                <div style="
    padding: 2px 4px 2px 4px;margin-right:8px;
">${response.procedure.failed || 0}
                                </div>
                                <div
                                    style="padding: 2px 4px 2px 4px;float: right;background-color: #E1E1E1;border-radius: 8px;">
                                    <span style="">${response.procedure.ratio_failed || 0}%</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="border: 0;">
                            <i class="fa fa-circle" aria-hidden="true" style="color: #CACACA;"></i>
                            <span>Not started</span>
                        </td>
                        <td style="border: 0;text-align: right">
                            <div style="
    display: flex;
    justify-content: flex-end;
">
                                <div style="
    padding: 2px 4px 2px 4px;margin-right:8px;
">${response.procedure.not_start || 0}
                                </div>
                                <div
                                    style="padding: 2px 4px 2px 4px;float: right;background-color: #E1E1E1;border-radius: 8px;">
                                    <span style="">${response.procedure.ratio_not_start || 0}%</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
                    `;
                    buildProcedureStatus.html(html);

                },
                complete: function () {
                    btnSubmitProcedure.prop('disabled', false);
                    spinnerSubmit.addClass('dn');
                },
                error: function (xhr, status, error) {
                    // Xử lý lỗi nếu có
                    console.error('AJAX Error: ' + status + error + xhr);
                }
            });
        };
        const loadSearchDataProcedure = () => {
            const jobId = elJobProcedure.val() || 0;
            const companyId = elCompanyProcedure.val() || 0;
            const fromDate = elFromDate.val() || '';
            const toDate = elToDate.val() || '';
            const formData = {
                jobId: jobId,
                companyId: companyId,
                fromDate: fromDate,
                toDate: toDate,
            };
            ajaxSearchDataProcedure(formData)
        };
        // Handle Logic
        $('[data-toggle="tooltip"]').tooltip()
        elFromDate.datetimepicker({
            format: "DD/MM/YYYY"
        });
        elToDate.datetimepicker({
            format: "DD/MM/YYYY"
        });
        elJobProcedure.select2({
            ajax: {
                delay: 500,
                url: '/admin/ajax/jobs',
                dataType: 'json',
                data: function (params) {
                    return {
                        q: params.term,
                        page: params.page || 1
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    return {
                        results: data.data,
                        pagination: {
                            more: (params.page * 15) < data.total
                        }
                    };
                }
            },
            placeholder: 'Search for Job',
            minimumInputLength: 1,
            allowClear: true
        });
        elCompanyProcedure.select2({
            ajax: {
                delay: 500,
                url: '/admin/ajax/companies',
                dataType: 'json',
                data: function (params) {
                    return {
                        q: params.term,
                        page: params.page || 1
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    return {
                        results: data.data,
                        pagination: {
                            more: (params.page * 15) < data.total
                        }
                    };
                }
            },
            placeholder: 'Search for Company',
            minimumInputLength: 1,
            allowClear: true
        });
        btnResetProcedure.off('click').on('click', function (e) {
            e.preventDefault();
            elFromDate.val(fromDate);
            elToDate.val(formatDate(new Date()));
            elJobProcedure.val(null).trigger("change")
            elCompanyProcedure.val(null).trigger("change")
        });
        btnSubmitProcedure.off('click').on('click', function (e) {
            e.preventDefault();
            loadSearchDataProcedure();
        });
        loadSearchDataProcedure();
    });
</script>
