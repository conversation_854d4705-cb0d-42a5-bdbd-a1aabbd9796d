<style>
    .d-flex {
        display: flex;
    }

    .flex-wrap {
        flex-wrap: wrap;
    }

    .d-none {
        display: none;
    }

    .overlay {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    .align-items-center {
        align-items: center;
    }

    .justify-content {
        justify-content: center;
    }

    @media(min-width: 768px) {
        .btn-submit {
            margin-top: 25px;
        }
    }

    .row.d-flex>[class^="col-"] {
        width: 100%;
        flex-grow: 1;
    }

    @media(min-width: 992px) {
        .row.d-flex>.col-md-6 {
            width: 50%;
        }
    }
</style>

<div class="w-100">
    <form id="chart-time" class="hidden">
        <div class="row">
            <div class="col-12 col-sm-6 col-md-4">
                <div class="form-group">
                    <label>Start Date</label>
                    <input type="text" id="from-date" name="from_date" class="form-control"
                        value="{{ \Carbon\Carbon::today()->subDays(6)->format('d/m/Y') }}" />
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
                <div class="form-group">
                    <label>End Date</label>
                    <input type="text" id="to-date" class="form-control" name="to_date"
                        value="{{ \Carbon\Carbon::today()->format('d/m/Y') }}" />
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4">
                <div class="form-group">
                    <label>Period</label>
                    <select class="form-control select2-application" name="period">
                        @foreach(config('constant.CHART.PERIOD') as $text => $value)
                        <option value="{{ $value }}">{{ ucFirst($text) }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-12">
                <div class="col-12 col-sm-6 col-md-8">
                    <div class="form-group">
                        <label>Status</label>
                        <select class="form-control select2-application" multiple name="status_ids[]">
                            @foreach(Modules\Job\Entities\Candidate::getStatusDescription() as $key => $value)
                            <option value="{{ $key }}">{{ ucFirst($value) }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="col-12 col-sm-6 col-md-8">
                    <div class="form-group">
                        <label>Location</label>
                        <select id="location" class="form-control select2-application" multiple name="location_ids[]">
                            <?php $arrCode = [] ?>
                            @foreach(Modules\VietnamArea\Entities\VietnamArea::whereIn('code',['79','01','48'])->get() as $key => $value)
                                <?php array_push($arrCode, $value->code) ?>
                                <option value="{{ $value->code }}">{{ ucFirst($value->name) }}</option>
                            @endforeach
                            <option value="none">None</option>
                            <option value={{ implode(',', $arrCode) }}>Nơi khác</option>
                        </select>
                    </div>
                    <input type="hidden" id="cache_location" name="cache_location" value=false>
                </div>

                <div class="col-12 col-sm-6 col-md-4">
                    <div class="form-group">
                        <button class="btn btn-primary btn-block btn-submit" type="submit">Submit</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="w-100" style="position: relative">
    <div id="candidate-apply-loading" class="d-none align-items-center justify-content-center overlay"
        style="background-color: #fff;">
        <i class="fa fa-spinner fa-4x fa-spin m-auto" aria-hidden="true"></i>
    </div>

    <canvas id="bar" style="width: 100%;">
    </canvas>

    <div id="candidate-chart-error" class="d-none align-items-center justify-content-center overlay"
        style="background-color: rgba(255, 255, 255, 0.5);">
        <span class="text-danger text-center px-15 py-10"
            style="margin: auto; background-color: rgba(255, 255, 255, 1);">Have errors while loading because
            <strong>Timeout</strong>!.</span>
    </div>
</div>


<script>
    $(function () {
        var candidateApplyChart;

        $(document).ready(function() {
            initChart(false);

            $('#chart-time').on('submit', function(e) {
                e.preventDefault();

                initChart(candidateApplyChart, true);

                $('#cache_location').attr('value',false);
            });

            $('#location').on('change', function() {
                $('#location_flag').attr('value',true);
                let value = $('#location').val();
                if(value != null && value.length > 0 ){
                    if(parseInt(jQuery.inArray("none", value)) > -1){
                        $('#location').val('none');
                    }
                    let lastValue = value.pop();
                    if((lastValue.split(",")).length > 1){
                        $('#location').val(lastValue);
                    }
                }
            });
        });

        $('#from-date').datetimepicker({
            format: 'DD/MM/YYYY',
        });

        $('#to-date').datetimepicker({
            format: 'DD/MM/YYYY',
            useCurrent: false,
        });

        $("#from-date").on("dp.change", function (e) {
            $('#to-date').data("DateTimePicker").minDate(e.date);
        });
        $("#to-date").on("dp.change", function (e) {
            $('#from-date').data("DateTimePicker").maxDate(e.date);
        });

        window.chartColors = {
            red: 'rgb(255, 99, 132)',
            orange: 'rgb(255, 159, 64)',
            yellow: 'rgb(255, 205, 86)',
            green: 'rgb(75, 192, 192)',
            blue: 'rgb(54, 162, 235)',
            purple: 'rgb(153, 102, 255)',
            grey: 'rgb(201, 203, 207)'
        };

        Chart.Legend.prototype.afterFit = function() {
            this.height = this.height + 30;
        };

        function initChart(updateChart = false) {
            $('#candidate-chart-error').removeClass('d-flex').addClass('d-none');
            $('#candidate-apply-loading').removeClass('d-none').addClass('d-flex');

            let data = $('#chart-time').serializeArray();

            let startDate = data[0].value;
            let endDate = data[1].value;
            let period = data[2].value;

            let formatLabel = makeFormatLabel(data[0].value, data[1].value, data[2].value);

            let status_ids = [];
            let location_ids = [];
            let cache_location = false;

            data.filter((value, index) => {
                if(value.name == 'status_ids[]'){
                    status_ids.push(value.value);
                }
                if(value.name == 'location_ids[]'){
                    location_ids.push(value.value);
                }
                if(value.name == 'cache_location'){
                    cache_location = value.value;
                }
            });

            Promise.all([ getChartData(startDate, endDate, period, formatLabel, status_ids, location_ids, cache_location) ])
            .then((dataList) => {
                let labels = [];
                let candidates = [];
                let appliedUsers = [];

                dataList.map((item) => {
                    labels = labels.concat(item.labels);
                    candidates = candidates.concat(item.candidates);
                    appliedUsers = appliedUsers.concat(item.user_apply);
                });

                $('#candidate-apply-loading').removeClass('d-flex').addClass('d-none');

                createChart(labels, candidates, appliedUsers, updateChart);
            })
            .catch((errorList) => {
                $('#candidate-chart-error').removeClass('d-none').addClass('d-flex');
            })

        }

        function getChartData(startDate, endDate, period, formatLabel, status_ids, location_ids, cache_location) {
            return $.ajax({
                url: "/admin/ajax/candidate-apply-data",
                method: 'GET',
                data: {
                    'from_date': startDate,
                    'to_date': endDate,
                    'period': period,
                    'format_label': formatLabel,
                    'status_ids': status_ids,
                    'location_ids': location_ids,
                    'cache_location': cache_location,
                },
            });
        }

        function createChart(labels, candidates, users, updateChart = false) {
            var barChartData = {
                labels: labels,
                datasets: [{
                    label: 'Candidates',
                    backgroundColor: window.chartColors.red,
                    borderWidth: 1,
                    data: candidates
                }, {
                    label: 'Applied Users',
                    backgroundColor: window.chartColors.blue,
                    borderWidth: 1,
                    data: users
                }]
            };

            if(!updateChart) {
                $('#chart-time').removeClass('hidden');
                $('.select2-application').select2({
                    minimumResultsForSearch: -1
                });

                var ctx = document.getElementById('bar').getContext('2d');
                candidateApplyChart = new Chart(ctx, {
                    type: 'bar',
                    data: barChartData,
                    options: {
                        responsive: true,
                        legend: {
                            position: 'top',
                        },
                        animation: {
                            onComplete: function() {
                                var chartInstance = this.chart,
                                ctx = chartInstance.ctx;
                                ctx.font = Chart.helpers.fontString(Chart.defaults.global.defaultFontSize, Chart.defaults.global.defaultFontStyle,
                                Chart.defaults.global.defaultFontFamily);
                                ctx.textAlign = 'center';
                                ctx.textBaseline = 'bottom';

                                this.data.datasets.forEach(function (dataset, i) {
                                    var meta = chartInstance.controller.getDatasetMeta(i);
                                    meta.data.forEach(function (bar, index) {
                                        var data = dataset.data[index];
                                        ctx.fillText(data, bar._model.x, bar._model.y - 5);
                                    });
                                });
                            }
                        }
                    },
                });
            } else {
                candidateApplyChart.data = barChartData;
                candidateApplyChart.update();
            }
        }

        function makeFormatLabel(startDate, endDate, period) {
            let startD = moment(startDate, 'DD/MM/YYYY');
            let endD = moment(endDate, 'DD/MM/YYYY');

            if(period == 1 || period == 2) {
                if(!endD.isSame(startD, 'year') || endD.diff(startD, 'days') > 365) {
                    return 'd/m/Y';
                }
                return 'd/m';
            } else if(period == 3) {
                if(!endD.isSame(startD, 'year') || endD.diff(startD, 'days') > 13) {
                    return 'd/m/Y';
                }
                return 'm/Y';
            } else {
                return 'd/m/Y'
            }
        }
    });
</script>
