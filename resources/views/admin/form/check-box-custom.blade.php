<div
    class=" {{ (isset($dataForm->status) && $dataForm->status == $dataForm::CANDIDATE_UNQUALIFIED || $errors->has($errorKey)) ? '' : 'hidden' }} field_why_not_matching {{$viewClass['form-group']}} {!! !$errors->has($column) ?: 'has-error' !!}">
    <label for="{{$id}}" class="{{$viewClass['label']}} control-label">{{$label}}</label>
    <div class="{{$viewClass['field']}}" id="{{$id}}">
        @include('admin::form.error')
        @foreach($options as $option => $label)
        <div class="items-check">
            <input id="{{$name.'_'.$option}}" type="checkbox" name="{{$name}}[]" value="{{$option}}" class="hidden" {{
                in_array($option, (array) old($column, $value)) || ($value===null && in_array($option, $checked))
                ?'checked':'' }} {!! $attributes !!} />
            <label class="label-check-custom" for="{{$name.'_'.$option}}">
                {{$label}}
            </label>
            @if ($option==='other')
            <input {{ $value!=null && isset($value['other_text_for_not_matching']) ? '' : 'disabled' }} type="text"
                id="other_text_for_not_matching" name="other_text_for_not_matching"
                value="{{ $value!=null && isset($value['other_text_for_not_matching']) ? old('other_text_for_not_matching', $value['other_text_for_not_matching']) :'' }}"
                class="form-control other_text_for_not_matching">
            @endif
        </div>
        @endforeach
        @include('admin::form.help-block')
    </div>
</div>
