<div class="multi-lang-input {{$viewClass['form-group']}} {!! !$errors->has($errorKey) ? '' : 'has-error' !!}">

    <label for="{{$id}}" class="{{$viewClass['label']}} control-label">{{$label}}</label>

    <div class="{{$viewClass['field']}}">

        @include('admin::form.error')

        <div class="input-locale-list">
            @foreach(config('translatable.locales') as $locale)
                <label><input @if($locale == 'vi') checked @endif type="radio" class="{{ $id }}_translate_option" name="{{ $name }}_translate_option" value="{{ $locale }}" /> {{ $locale }}</label>
            @endforeach
        </div>
        <div class="input-group duyduyduy">

            @if ($prepend)
            <span class="input-group-addon">{!! $prepend !!}</span>
            @endif

            @foreach(config('translatable.locales') as $locale)
            @php 
            $translation = Arr::get($translations, $locale, []);
            @endphp
            <input {!! $attributes !!} name="{{ $name . '[' . $locale . ']' }}" value="{{ Arr::get($translation, $lastColumn) }}" class="form-control {{$id}} @if($locale == 'en') hidden @endif" />
            @endforeach

            @if ($append)
                <span class="input-group-addon clearfix">{!! $append !!}</span>
            @endif

        </div>

        @include('admin::form.help-block')

    </div>
</div>