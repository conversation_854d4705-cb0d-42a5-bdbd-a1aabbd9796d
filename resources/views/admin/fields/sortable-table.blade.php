<style>
    td .form-group {
        margin-bottom: 0 !important;
    }
</style>

<div class="row ">
    <div class="{{$viewClass['label']}}">
        <h4 class="pull-right">{!! $label !!}</h4>
        <span class="{!! !$errors->has($errorKey) ? '' : 'has-error' !!}">
            @include('admin::form.error')
        </span>
    </div>
    <div class="{{$viewClass['field']}} relative">
        <div id="has-many-{{$column}}" style="margin-top: 15px;">
            <table class="table table-has-many has-many-{{$column}}">
                <thead>
                <tr>
                    <th></th>
                    @foreach($headers as $header)
                        <th>{{ $header }}</th>
                    @endforeach

                    <th class="hidden"></th>

                    @if($options['allowDelete'])
                        <th></th>
                    @endif
                </tr>
                </thead>
                <tbody class="has-many-{{$column}}-forms sortable-table">
                @foreach($forms as $pk => $form)
                    <tr class="has-many-{{$column}}-form fields-group">
                        <td class="handle">
                            <i class="fa fa-arrows-alt"></i>
                        </td>

                        <?php $hidden = ''; ?>

                        @foreach($form->fields() as $field)

                            @if (is_a($field, \Amscore\Admin\Form\Field\Hidden::class))
                                <?php $hidden .= $field->render(); ?>
                                @continue
                            @endif

                            <td>{!! $field->setLabelClass(['hidden'])->setWidth(12, 0)->render() !!}</td>
                        @endforeach

                        <td class="hidden">{!! $hidden !!}</td>

                        @if($options['allowDelete'])
                            <td class="form-group">
                                <div>
                                    <div class="remove btn btn-warning btn-sm pull-right"><i class="fa fa-trash">&nbsp;</i>{{ trans('admin.remove') }}</div>
                                </div>
                            </td>
                        @endif
                    </tr>
                @endforeach
                </tbody>
            </table>

            <template class="{{$column}}-tpl">
                <tr class="has-many-{{$column}}-form fields-group">
                    <td class="handle">
                        <i class="fa fa-arrows-alt"></i>
                    </td>

                    {!! $template !!}

                    <td class="form-group">
                        <div>
                            <div class="remove btn btn-warning btn-sm pull-right"><i class="fa fa-trash">&nbsp;</i>{{ trans('admin.remove') }}</div>
                        </div>
                    </td>
                </tr>
            </template>

            @if($options['allowCreate'])
                <div class="form-group">
                    <div class="{{$viewClass['field']}}">
                        <div class="add btn btn-success btn-sm"><i class="fa fa-save"></i>&nbsp;{{ trans('admin.new') }}</div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    @includeWhen($showAudit, 'admin::partials.history-control')

</div>

<hr style="margin-top: 0px;">
