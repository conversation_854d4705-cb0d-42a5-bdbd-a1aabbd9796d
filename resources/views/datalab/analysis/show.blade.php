

<div class="box" style="max-width: 990px">
    <div class="chart-container box-body">
        <canvas id="analysis-chart"></canvas>
    </div>
</div>

<div class="box">
    <div class="box-header with-border">
        <h5 class="box-title">Data Table (transpose)</h5>
        <div class="box-tools pull-right">
            <button title="Download CSV" class="btn btn-box-tool export-to-cvs" data-table-id="dataTable01" data-filename="{{$filename}}-transpose.csv"><i class="fa fa-download"></i> CSV</button>
            <button class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
        </div>
    </div>
    <form id="dt1">
    <div class="box-body table-responsive table-datalab" id="analysis-table">loading..</div>
    </form>
</div>

<div class="box collapsed-box">
    <div class="box-header with-border">
        <h5 class="box-title">Data Table</h5>
        <div class="box-tools pull-right">
            <button title="Download CSV" class="btn btn-box-tool export-to-cvs" data-table-id="dataTable02" data-filename="{{$filename}}.csv"><i class="fa fa-download"></i> CSV</button>
            <button class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i></button>
        </div>
    </div>
    <div class="box-body table-responsive table-datalab">
        @include('datalab.analysis.widgets.table', $table)
    </div>
</div>
<div class="box collapsed-box">
    <div class="box-header with-border">
        <h5 class="box-title">Developer</h5>
        <div class="box-tools pull-right">
            <button class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i></button>
        </div>
    </div>
    <div class="box-body">
        <h6>config('datalab.components.[name]')</h6>
        <textarea class="form-control" rows="16">{{ implode("\n", $datalabConfig) }}</textarea>
        {{--
        <h6>For Sheet</h6>
        <p>date, metric_id, $dimension_id, $comparison_id, mode, value, $dimensionCompoundValues</p>
<textarea class="form-control" rows="8">
@foreach($examples as $row)
@foreach($row['values'] as $date => $value)
{{ implode("\t", array_merge(['date' => $date], $row, ['values' => $value])) }}
@endforeach
@endforeach
</textarea>
        --}}
        {{--
        <h6>Data API (Not implementation)</h6>
        <textarea class="form-control" rows="16">POST {{ route('analysis.api.analysis.data.store', ['analysis' => $responseData->data->id]) }}
Content-Type: application/json

@json($examples, JSON_PRETTY_PRINT | JSON_UNESCAPED_CUSTOM)
        </textarea>
        --}}
    </div>
</div>

<style>
    .table-datalab th.date-label {
        text-align: center;
        min-width: 80px
    }
</style>
<script>jQuery(function ($) {
        var config = {
            type: 'line',
            data: @json($data),
            definitions: @json($definitions),
            options: {
                scales: {
                    yAxes: [{
                        ticks: {
                            beginZero: true,
                        }
                    }]
                },
                legend: {
                    display: false
                },
                elements: {
                    line: {
                        tension: 0, // disables bezier curves
                    }
                },
                // showLines: false, // disable for all datasets
                animation: {
                    duration: 0, // general animation time
                },
                hover: {
                    animationDuration: 0, // duration of animations when hovering an item
                },
                responsiveAnimationDuration: 0, // animation duration after a resize
            },
        }

        var xAxis = config.data.labels
        var yAxis = config.data.datasets
        var definitions = config.definitions

        var tableHeader = `<tr>${
            xAxis.reduce((memo, entry) => {
                memo += `<th class="date-label">${entry}</th>`
                return memo
            }, `<th><input class="select-all" type="checkbox" value="all" checked="checked"></th><th>Metric</th><th>Dimension</th><th>Comparison</th>`)
        }</tr>`

        const prev = {
            metric: 0,
            dimension: 0,
            comparison: 0
        }

        const nf = new Intl.NumberFormat()
        const tableBody = yAxis.reduce((memo, entry, index) => {

            // Total or All
            var checked = false
            var new_definition = false
            var headerRow = ''
            for (const [type, value] of Object.entries(prev)) {
                const dfn = definitions.find(el => el.id === parseInt(entry[type])) || {id: null}
                if (value === dfn.id && !new_definition) {
                    headerRow += `<th>&nbsp;</th>`
                } else {
                    headerRow += `<th class="text-nowrap"><button data-compound_id="${entry.compound_id}" data-toggle="tooltip" title="${dfn.description} | ${entry.note || ''}">${dfn.name}</button></th>`
                    new_definition = true
                    prev[type] = dfn.id
                    if (type === 'metric' || (type === "dimension" && entry.dimension === "1") || (type === "comparison" && entry.comparison === "4") ) {
                        checked = true
                    }
                }
            }

            entry.hidden = !checked

            const rowData = entry.data.reduce((memo, entry) => {
                memo += `<td class="text-right">${nf.format(entry)}</td>`
                return memo
            }, '')

            memo += `<tr><th><input class="row-checkbox" type="checkbox" value="${index}" ${checked ? 'checked="checked"' : ''}></th>` + headerRow + rowData + '</tr>'

            return memo
        }, '')


        $('#analysis-table').html(`<table id="dataTable01" class="table table-striped table-bordered table-condensed table-hover">
            <thead>${tableHeader}</thead>
            <tbody>${tableBody}</tbody>
        </table>`)

        var chart = new Chart('analysis-chart', config)
        // https://www.chartjs.org/docs/2.7.3/configuration/legend.html

        const $form = $('#dt1')
        const $selectAll = $form.find('input.select-all[type=checkbox]')
        const $rowCheckbox = $form.find('input.row-checkbox[type=checkbox]')
        checkIndeterminate()

        $selectAll.change((e) => {
            if (e.target.value === "all") {
                e.target.form.reset()
                $rowCheckbox.trigger('change')
                e.target.value = 'reseted'
            } else {
                $rowCheckbox.prop('checked', e.target.checked)
                chart.data.datasets.forEach((item) => {
                    item.hidden = !e.target.checked
                })

            }
            chart.update()
            checkIndeterminate(e.target.checked)
        })

        $rowCheckbox.change((e) => {
            var checked = e.target.checked
            chart.data.datasets[e.target.value].hidden = !checked
            chart.update()
            checkIndeterminate(checked)
        })

        function checkIndeterminate(checked) {
            all = true;

            $rowCheckbox.each(function() {
                return all = (this.checked === checked);
            });

            $selectAll.prop({
                indeterminate: !all,
                checked: checked,
                value: (all && !checked) ? 'all' : ''
            });
        }

        checkIndeterminate(true)


        $(".export-to-cvs").click(function (e) {
            var $el = $(e.target)
            exportTableToCsv($el.data('table-id'), $el.data('filename'))
        })

        function exportTableToCsv(tableId, filename) {
            var BOM = "\uFEFF";
            var table = document.getElementById(tableId);
            var csvString = BOM;
            for (var rowCnt = 0; rowCnt < table.rows.length; rowCnt++) {
                var rowData = table.rows[rowCnt].cells;
                for (var colCnt = 0; colCnt < rowData.length; colCnt++) {
                    var columnData = rowData[colCnt].innerText;
                    if (columnData == null || columnData.length === 0) {
                        columnData = "".replace(/"/g, '""');
                    }
                    else {
                        columnData = columnData.toString().replace(/"/g, '""'); // escape double quotes
                    }
                    csvString = csvString + '"' + columnData + '",';
                }
                csvString = csvString.substring(0, csvString.length - 1);
                csvString = csvString + "\r\n";
            }
            csvString = csvString.substring(0, csvString.length - 1);

            // IE 10, 11, Edge Run
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                window.navigator.msSaveOrOpenBlob(new Blob([decodeURIComponent(csvString)], {
                    type: 'text/csv;charset=utf8'
                }), filename);
            } else {
                var a = document.createElement('a');
                a.setAttribute('style', 'display:none');
                if (window.Blob && window.URL) {
                    // HTML5 Blob
                    a.setAttribute('href', URL.createObjectURL(new Blob([csvString], { type: 'text/csv;charset=utf8' })));
                } else {
                    // Data URI
                    a.setAttribute('target', '_blank');
                    a.setAttribute('href', 'data:application/csv;charset=utf-8,' + encodeURIComponent(csvString));
                }
                a.setAttribute('download', filename);
                document.body.appendChild(a);
                a.click()
                a.remove();
            }

        }


    })
</script>
