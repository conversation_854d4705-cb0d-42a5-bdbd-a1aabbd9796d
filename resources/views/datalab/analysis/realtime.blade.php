<div class="chart-container" style="position: relative; height:40vh; width:80vw">
    <canvas id="statistics-chart"></canvas>
</div>
<div id="statistics-report-table"> ...</div>

Realtime overview

USERS IN LAST 30 MINUTES
Desktop, Mobile, Tablet,

mobile 에 ios/android 앱이 포함됨

Desktop: Include Stream name = TopDev Responsive Web AND Include Device category = desktop or tablet or smart tv
Mobile: Include Stream name = TopDev Responsive Web AND Include Device category = mobile
M.App: Include Stream name = TopDev or TopDev



Users by First user source/medium

Users by Audience
New Users


Views by Page title and screen name


Event count by Event name


Conversions  by Event name

Users  by User property



---

Firebase overview
User activity over time
USERS IN LAST 30 MINUTES






