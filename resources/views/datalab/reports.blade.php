


<div class="chart-container" style="position: relative; height:40vh; width:80vw">
    <canvas id="statistics-chart"></canvas>
</div>
<div id="statistics-report-table"> ...</div>
...
<script>


    jQuery(function ($) {


        var labelDates = ['5/27', '5/28', '5/29', '5/30']
        var chartConfig = {
            type: 'line',
            data: {
                // labels: ['5/28', '5/29', 'Yellow', 'Green', 'Purple', 'Orange'],
                labels: ['5/27', '5/28', '5/29', '5/30', '6/1', '6/2', '6/3'],
                datasets: [
                    {
                        label: '# of Votes',
                        data: [12, 19, 3, 5, 2, 3],
                        borderWidth: 1
                    },
                    {
                        label: '# of Points',
                        data: [7, 11, 5, 8, 3, 7],
                        borderWidth: 1
                    }
                ]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    yAxes: [{
                        ticks: {
                            reverse: false
                        }
                    }]
                },

            }
        }

        new Chart('statistics-chart', chartConfig)

        var xAxis = chartConfig.data.labels
        var yAxis = chartConfig.data.datasets

        var tableHeader = `<tr>${
            xAxis.reduce((memo, entry) => {
                memo += `<th>${entry}</th>`
                return memo
            }, '<th></th>')
        }</tr>`

        var tableBody = yAxis.reduce((memo, entry) => {
            const rows = entry.data.reduce((memo, entry) => {
                memo += `<td>${entry}</td>`
                return memo
            }, '')

            memo += `<tr><td>${entry.label}</td>${rows}</tr>`

            return memo
        }, '')

        $('#statistics-report-table').html(`<table>${tableHeader}${tableBody}</table>`)


    })
</script>
