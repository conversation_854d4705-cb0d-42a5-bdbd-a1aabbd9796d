<?php

namespace Modules\Company\Entities;

use App\Helpers\FeatureFlag;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Amscore\Admin\Traits\AdminBuilder;
use Amscore\Admin\Auth\Database\Administrator;
use Modules\Company\Transformers\AuditTransformer;
use Modules\Meta\Entities\Meta;
use Modules\SearchCandidate\Entities\CompanyCreditLog;
use Modules\SearchCandidate\Entities\CompanySearchPackage;
use Modules\VietnamArea\Traits\Addressable;
use Overtrue\LaravelFollow\Traits\CanBeFollowed;
use Modules\Taxonomy\Traits\Taxoable;
use Modules\User\Entities\User;
use Modules\Job\Entities\Job;
use Laravel\Scout\Searchable;
use Modules\Subscription\Entities\Package;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Modules\Company\Traits\HasProduct;
use Modules\Company\Traits\HasIntroduce;
use Modules\Company\Traits\HasBottomIntroduceGroup;
use Modules\Company\Traits\HasHeadIntroduceGroup;
use Modules\Company\Traits\HasBlog;
use OwenIt\Auditing\Contracts\Auditable;
use Modules\Activity\Traits\InteractsWithViews;
use Modules\Activity\Contracts\ViewableContract;
use App\Jobs\ScriptedDocumentUpdate;
use App\Traits\UseUuid;
use App\Contracts\TopdevModel;
use Modules\Meta\Traits\Metable;
use App\Traits\HasSlug;
use Modules\File\Traits\HasFile;
use Modules\Company\Jobs\HandlingAfterSaved;
use Modules\Subscription\Traits\HasSubscriptions;
use App\Traits\HasCustomRelations;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Modules\Company\Database\Searchable\CompanyImportSource;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\MediaCollections\File;

/**
 * Modules\Company\Entities\Company
 *
 * @property int $id
 * @property string $uuid
 * @property string|null $display_name
 * @property string|null $email
 * @property string|null $phone
 * @property string|null $description
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int|null $parent_id
 * @property string $slug
 * @property int|null $user_id
 * @property int $status
 * @property string|null $tax_number
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\VietnamArea\Entities\Address[] $addresses
 * @property-read int|null $addresses_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\OwenIt\Auditing\Models\Audit[] $audits
 * @property-read int|null $audits_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Company\Entities\Blog[] $blogs
 * @property-read int|null $blogs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Company\Entities\BottomIntroduceGroup[] $bottomIntroducesGroup
 * @property-read int|null $bottom_introduces_group_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Company[] $childrens
 * @property-read int|null $childrens_count
 * @property-read \Illuminate\Database\Eloquent\Collection|User[]|null $employees
 * @property-read int|null $employees_count
 * @property-read User|null $employer
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Subscription\Entities\Feature[] $features
 * @property-read int|null $features_count
 * @property-read \Illuminate\Database\Eloquent\Collection|User[] $followers
 * @property-read int|null $followers_count
 * @property-read array $address_locality_array
 * @property-read array $address_locality
 * @property-read string $address_locality_list
 * @property-read array $address_region_array
 * @property-read array $address_region
 * @property-read array $address_region_ids
 * @property-read string $address_region_list
 * @property-read string $address_short_region_list
 * @property-read mixed $bottom_introduces_group
 * @property-read array $collection_addresses
 * @property-read \Modules\Company\Entities\url $company_url
 * @property-read string $description_str
 * @property-read string $detail_url
 * @property-read array $employ_overview
 * @property-read array $employer_aggregate_rating
 * @property-read \Modules\Company\Entities\url $follow_url
 * @property-read array $full_addresses
 * @property-read string $group_url
 * @property-read mixed $head_introduces_group
 * @property-read bool $is_highlight
 * @property-read bool $is_spotlight
 * @property array $members
 * @property-read Collection $meta_collect
 * @property-read array $nationalities_arr
 * @property-read string $nationalities_str
 * @property-read int $num_employees_id
 * @property-read int $num_followers
 * @property-read int $num_job_openings
 * @property-read int $num_jobs
 * @property-read int $num_viewers
 * @property array $packages
 * @property-read array $schema_local_business
 * @property-read mixed $scoring_by_basic_plus
 * @property-read mixed $scoring_by_distinction
 * @property-read string $search_employerl_url
 * @property array $services
 * @property-read string $short_addresses
 * @property-read string $status_display
 * @property array $terms
 * @property-read \Modules\Company\Entities\url $update_url
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Company\Entities\HeadIntroduceGroup[] $headIntroducesGroup
 * @property-read int|null $head_introduces_group_count
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\Company\Entities\Introduce[] $introduces
 * @property-read int|null $introduces_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Job[] $jobs
 * @property-read int|null $jobs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\File\Entities\Media[] $media
 * @property-read int|null $media_count
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\Meta\Entities\Meta[] $meta
 * @property-read int|null $meta_count
 * @property-read Company|null $parent
 * @property-read \Illuminate\Database\Eloquent\Collection|Administrator[] $personCharge
 * @property-read int|null $person_charge_count
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\Company\Entities\Product[] $products
 * @property-read int|null $products_count
 * @property-write mixed $blog
 * @property mixed $faqs
 * @property-write mixed $force_meta
 * @property-read \Illuminate\Database\Eloquent\Collection|Package[] $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Taxonomy\Entities\Taxonomy[] $taxonomies
 * @property-read int|null $taxonomies_count
 * @property-read \Modules\Company\Entities\CompanyTranslation|null $translation
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Company\Entities\CompanyTranslation[] $translations
 * @property-read int|null $translations_count
 * @property-read \Illuminate\Database\Eloquent\Collection|User[] $viewers
 * @property-read int|null $viewers_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Activity\Entities\Activity[] $views
 * @property-read int|null $views_count
 * @property-read CompanyCreditLog|null $companyCreditLogs
 * @property-read CompanySearchPackage|null $companySearchPackages
 * @method static Builder|Company active()
 * @method static Builder|Company disableCache()
 * @method static Builder|Company inactive()
 * @method static Builder|Company listsTranslations(string $translationField)
 * @method static Builder|Company newModelQuery()
 * @method static Builder|Company newQuery()
 * @method static Builder|Company notTranslatedIn(?string $locale = null)
 * @method static \Illuminate\Database\Query\Builder|Company onlyTrashed()
 * @method static Builder|Company orWhereTranslation(string $translationField, $value, ?string $locale = null)
 * @method static Builder|Company orWhereTranslationLike(string $translationField, $value, ?string $locale = null)
 * @method static Builder|Company orderByMeta(string $key, string $direction = 'asc', $strict = false)
 * @method static Builder|Company orderByMetaNumeric(string $key, string $direction = 'asc', $strict = false)
 * @method static Builder|Company orderByTranslation(string $translationField, string $sortMethod = 'asc')
 * @method static Builder|Company query()
 * @method static Builder|Company translated()
 * @method static Builder|Company translatedIn(?string $locale = null)
 * @method static Builder|Company whereCreatedAt($value)
 * @method static Builder|Company whereDeletedAt($value)
 * @method static Builder|Company whereDescription($value)
 * @method static Builder|Company whereDisplayName($value)
 * @method static Builder|Company whereDoesntHaveMeta($key)
 * @method static Builder|Company whereEmail($value)
 * @method static Builder|Company whereHasMeta($key)
 * @method static Builder|Company whereHasMetaKeys(array $keys)
 * @method static Builder|Company whereId($value)
 * @method static Builder|Company whereMeta(string $key, $operator, $value = null)
 * @method static Builder|Company whereMetaIn(string $key, array $values)
 * @method static Builder|Company whereMetaNumeric(string $key, string $operator, $value)
 * @method static Builder|Company whereMetaRefactor(string $key, $operator, $value = null)
 * @method static Builder|Company whereParentId($value)
 * @method static Builder|Company wherePhone($value)
 * @method static Builder|Company whereSlug($value)
 * @method static Builder|Company whereStatus($value)
 * @method static Builder|Company whereTaxNumber($value)
 * @method static Builder|Company whereTranslation(string $translationField, $value, ?string $locale = null, string $method = 'whereHas', string $operator = '=')
 * @method static Builder|Company whereTranslationLike(string $translationField, $value, ?string $locale = null)
 * @method static Builder|Company whereUpdatedAt($value)
 * @method static Builder|Company whereUserId($value)
 * @method static Builder|Company whereUuid($value)
 * @method static Builder|Company withAllArea($areas = [], $type = null)
 * @method static Builder|Company withAllTerms($terms, ?string $taxonomy = null)
 * @method static Builder|Company withAnyArea($areas = [], $type = null)
 * @method static Builder|Company withAnyTerms($terms, ?string $taxonomy = null)
 * @method static Builder|Company withCacheCooldownSeconds(?int $seconds = null)
 * @method static Builder|Company withTermsName($keyword)
 * @method static Builder|Company withTranslation()
 * @method static \Illuminate\Database\Query\Builder|Company withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Company withoutTrashed()
 * @mixin \Eloquent
 * @property-read int|null $company_credit_logs_count
 * @property-read int|null $company_search_packages_count
 * @property array $benefits
 */
class Company extends TopdevModel implements HasMedia, ViewableContract, Auditable, TranslatableContract
{
    use UseUuid;
    use HasSlug;
    use Taxoable;
    use Metable;
    use SoftDeletes;
    use Addressable;
    use AdminBuilder;
    use HasFile;
    use CanBeFollowed;
    use Searchable;
    use Cachable;
    use HasProduct;
    use HasIntroduce;
    use HasBlog;
    use InteractsWithViews;
    use \OwenIt\Auditing\Auditable;
    use HasBottomIntroduceGroup;
    use HasHeadIntroduceGroup;
    use Translatable;

    public $translatedAttributes = [
        'meta_title',
        'meta_description',
        'meta_keywords'
    ];

    /**
     * Has Subscriptions
     */
    use HasSubscriptions;

    use HasCustomRelations;

    public const FEATURE_ENABLE = 'on';
    public const FEATURE_DISABLE = 'off';
    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 2;
    public const STATUS_REVIEW = 3;
    public const STATUS_WAITING = 4;

    /**
     * {@inheritdoc}
     */
    protected $table = 'companies';

    /**
     * {@inheritdoc}
     */
    protected $fillable = [
        'id', 'status', 'uuid', 'display_name', 'parent_id', 'email', 'phone', 'image_logo', 'image_cover',
        'image_galleries', 'products', 'description', 'addresses', 'introduces', 'nationalities', 'blogs',
        'tagline', 'categories', 'extra_skills', 'best_skills', 'industries', 'nationalities', 'num_employees',
        'company_size', 'website', 'social_network', 'benefits', 'recruitment_process', 'skills', 'skills_ids',
        'source', 'link_crawl', 'level', 'image_statisties',
        'middle_description_group', 'layout_group', 'statisties', 'title_statisties',
        'headIntroducesGroup', 'bottomIntroducesGroup', 'logo_group', 'cover_group', 'faqs', 'note', 'tax_number'
    ];

    /**
     * {@inheritdoc}
     */
    public $files = [
        'image_galleries', 'image_logo', 'image_cover', 'image_statisties', 'logo_group', 'cover_group', 'erc'
    ];

    /**
     * {@inheritdoc}
     */
    protected $attributes = [
        'slug' => null,
        'status' => self::STATUS_INACTIVE
    ];

    /**
     * Attributes to include in the Audit.
     *
     * @var array
     */
    protected $auditInclude = [
        'benefits',
    ];

    /**
     * The attributes that should be append to native types.
     *
     * @var array
     */
    public $scopeMeta = [
        'tagline' => null,
        'spotlight' => null,
        'category' => null,
        'highlight' => null,
        'company_size' => null,
        'website' => null,
        'source' => null,
        'emails_cc' => [],
        'social_network' => [],
        'num_viewers_fake' => 0,
        'num_followers_fake' => 0,
        'recruitment_process' => [],
        'link_crawl' => null,
        'level' => null,
        'middle_description_group' => null,
        'layout_group' => null,
        'title_statisties' => null,
        'statisties' => [],
        'faqs' => [],
        'note' => [],
        'benefits' => []
    ];

    /**
     * Value meta will return all the values of an array.
     *
     * @var array
     */
    protected $arrayValues = [
        'benefits', 'recruitment_process', 'social_network', 'statisties', 'faqs', 'note'
    ];

    /**
     * The attributes that should be append to native types.
     *
     * @var array
     */
    public $scopeTerm = [
        'categories', 'extra_skills', 'skills', 'industries', 'nationalities', 'num_employees'
    ];

    /**
     * To automatically delete all views of an viewable Eloquent model on delete
     *
     * @return bool
     */
    protected $removeViewsOnDelete = true;

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function (self $company) {
            dispatch(new HandlingAfterSaved($company));
        });
    }

    /**
     * {@inheritdoc}
     */
    public function sluggable(): string
    {
        return $this->display_name ?? '';
    }

    public function generateUniqueSlug()
    {
        return false;
    }

    /**
     * Register media for job
     *
     * @var void
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image_logo')
            ->acceptsFile(function (File $file) {
                return in_array($file->mimeType, config('media-library.allow_image_mimes'));
            })
            ->singleFile();

        $this->addMediaCollection('image_cover')
            ->acceptsFile(function (File $file) {
                return in_array($file->mimeType, config('media-library.allow_image_mimes'));
            })
            ->singleFile();

        $this->addMediaCollection('logo_group')
            ->acceptsFile(function (File $file) {
                return in_array($file->mimeType, config('media-library.allow_image_mimes'));
            })
            ->singleFile();

        $this->addMediaCollection('cover_group')
            ->acceptsFile(function (File $file) {
                return in_array($file->mimeType, config('media-library.allow_image_mimes'));
            })
            ->singleFile();

        $this->addMediaCollection('image_galleries')
            ->acceptsFile(function (File $file) {
                return in_array($file->mimeType, config('media-library.allow_image_mimes'));
            });

        $this->addMediaCollection('image_statisties')
            ->acceptsFile(function (File $file) {
                return in_array($file->mimeType, config('media-library.allow_image_mimes'));
            });

        if (FeatureFlag::isEnabledFreePost()) {
            $this
                ->addMediaCollection('erc')
                ->acceptsMimeTypes(['application/pdf'])
                ->singleFile();
        }
    }

    /**
     * Override media relationship in InteractsWithMedia.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function media(): \Illuminate\Database\Eloquent\Relations\MorphMany
    {
        return $this->morphMany(config('media-library.media_model'), 'model')
            ->disableCache();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\hasOne
     */
    public function employer()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    /**
     * @return HasMany
     */
    public function employees(): HasMany
    {
        return $this->hasMany(User::class, 'company_id')->where('type', 'employer');
    }

    /**
     * @return HasMany
     */
    public function jobs(): HasMany
    {
        return $this->hasMany(Job::class, 'owned_id')
            ->orderBy('id', 'desc');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parent()
    {
        return $this->belongsTo($this, 'parent_id');
    }

    /**
     * @return HasMany
     */
    public function childrens(): HasMany
    {
        return $this->hasMany($this, 'parent_id');
    }

    /**
     * Get all members id.
     *
     * @return array
     */
    public function getMembersAttribute()
    {
        return (array)$this->childrens->pluck('id')->all();
    }

    /**
     * Set members.
     *
     * @param array $values
     */
    public function setMembersAttribute($values)
    {
        $add = array_diff($values, $this->members);
        $remove = array_diff($this->members, $values);

        static::whereIn('id', $add)->update([
            'parent_id' => $this->getKey()
        ]);

        static::whereIn('id', $remove)->update([
            'parent_id' => null
        ]);
    }

    /**
     * @return hasMany
     */
    public function onlyJobOpen()
    {
        return $this->jobs()->onlyOpen();
    }

    /**
     * Get total number the job post.
     *
     * @return integer
     */
    public function getNumJobsAttribute()
    {
        if (array_key_exists('jobs_count', $this->attributes)) {
            return $this->attributes['jobs_count'];
        }

        return $this->jobs->count();
    }

    /**
     * Get total number the job openings.
     *
     * @return integer
     */
    public function getNumJobOpeningsAttribute()
    {
        if (array_key_exists('only_job_open_count', $this->attributes)) {
            return $this->attributes['only_job_open_count'];
        }

        return $this->onlyJobOpen->count();
    }

    /**
     * Get total followers.
     *
     * @return integer
     */
    public function getNumFollowersAttribute()
    {
        if (array_key_exists('followers_count', $this->attributes)) {
            return $this->attributes['followers_count'] + $this->num_followers_fake;
        }

        return $this->followers->count() + $this->num_followers_fake;
    }

    /**
     * Get link detail.
     *
     * @return string
     */
    public function getDetailUrlAttribute()
    {
        return frontend_url('companies/' . $this->slug . '-' . $this->getKey());
    }

    /**
     * Get link detail.
     *
     * @return string
     */
    public function getGroupUrlAttribute()
    {
        return frontend_url('companies/group/' . $this->slug . '-' . $this->getKey());
    }

    /**
     * Get link search employer.
     *
     * @return string
     */
    public function getSearchEmployerlUrlAttribute()
    {
        return url('/admin/employers?company_id=' . $this->getKey());
    }

    /**
     * Lấy link update thông tin công ty.
     *
     * @return url
     */
    public function getUpdateUrlAttribute()
    {
        return url('companies/edit/' . $this->id);
    }

    /**
     * Lấy link follow cty.
     *
     * @return url
     */
    public function getFollowUrlAttribute()
    {
        return api_url('users/me/followed-companies/' . $this->uuid);
    }

    /**
     * Lấy link xem thông tin công ty.
     *
     * @return url
     */
    public function getCompanyUrlAttribute()
    {
        return url('company/' . $this->alias . '-' . $this->id);
    }

    /**
     * Get description.
     *
     * @return string
     */
    public function getDescriptionStrAttribute()
    {
        return preg_replace("/[\n\r]/", '', html_entity_decode(strip_tags($this->description)));
    }

    /**
     * Check highlight.
     *
     * @return bool
     */
    public function getIsHighlightAttribute()
    {
        return $this->hasFeature('highlight');
    }

    /**
     * Kiểm tra phải cty spotlight.
     *
     * @return bool
     */
    public function getIsSpotlightAttribute()
    {
        return $this->hasFeature('spotlight');
    }

    /**
     * Kiểm tra phải cty có mua gói free.
     *
     * @return bool
     */
    public function isFree()
    {
        return $this->jobs->filter(function ($job) {
            return $job->subscribedTo('free');
        })
            ->isNotEmpty();
    }

    /**
     * Kiểm tra phải cty có mua gói basic.
     *
     * @return bool
     */
    public function isBasic()
    {
        return $this->jobs->filter(function ($job) {
            return $job->subscribedTo('basic');
        })
            ->isNotEmpty();
    }

    /**
     * Kiểm tra phải cty có mua gói basic plus.
     *
     * @return bool
     */
    public function isBasicPlus()
    {
        return $this->jobs->filter(function ($job) {
            return $job->subscribedTo('basic-plus');
        })
            ->isNotEmpty();
    }

    /**
     * Kiểm tra phải cty có mua gói distinction.
     *
     * @return bool
     */
    public function isDistinction()
    {
        return $this->jobs->filter(function ($job) {
            return $job->subscribedTo('distinction');
        })
            ->isNotEmpty();
    }

    public function getScoringByBasicPlusAttribute()
    {
        return $this->jobs->reduce(function ($total, $job) {
            foreach ($job->packages as $key => $package) {
                if (Package::PACKAGE_BASIC_PLUS == $key) {
                    return $total + Package::getScoreByPackage($key);
                }
            }

            return $total;
        }, 0);
    }

    public function getScoringByDistinctionAttribute()
    {
        return $this->jobs->reduce(function ($total, $job) {
            foreach ($job->packages as $key => $package) {
                if (Package::PACKAGE_DISTINCTION == $key) {
                    return $total + Package::getScoreByPackage($key);
                }
            }

            return $total;
        }, 0);
    }

    /**
     * Determine if the model should be searchable.
     *
     * @return bool
     */
    public function shouldBeSearchable()
    {
        if ($this->isInactive() || $this->trashed()) {
            return false;
        }

        return true;
    }

    /**
     * Get the name of the "parent id" column.
     *
     * @return string
     */
    public function getParentColumn()
    {
        return defined('static::PARENT_COLUMN') ? static::PARENT_COLUMN : 'parent_id';
    }

    /**
     * Mark as pending.
     *
     * @return mixed
     */
    public function markAsPending($parent = null)
    {
        return $this->fill([
            $this->getParentColumn() => is_null($parent) ? $this->getKey() : $parent
        ])->save();
    }

    /**
     * Count job by subscription
     *
     * @return integer
     */
    public function countJobBy($subscription, $status = 1)
    {
        return $this->jobs->filter(function ($job) use ($subscription, $status) {
            return ($job->subscribedTo($subscription) && $job->status == $status);
        })->count();
    }

    /**
     * Return viewers.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function viewers()
    {
        $table = config('eloquent-viewable.models.view.table_name');
        $class = \get_class($this);
        $userTable = 'users';
        $foreignKey = 'user_id';
        $tablePrefixedForeignKey = app('db.connection')->getQueryGrammar()->wrap(\sprintf('pivot_viewable.%s', $foreignKey));
        $eachOtherKey = app('db.connection')->getQueryGrammar()->wrap('pivot_each_other');

        return $this->morphToMany(User::class, 'viewable', $table)
            ->wherePivot('collection', '=', 'view')
            ->withPivot('viewable_type', 'collection')
            ->addSelect("{$userTable}.*", \DB::raw("(CASE WHEN {$tablePrefixedForeignKey} IS NOT NULL THEN 1 ELSE 0 END) as {$eachOtherKey}"))
            ->leftJoin("{$table} as pivot_viewable", function ($join) use ($table, $class, $foreignKey) {
                $join->on('pivot_viewable.viewable_type', '=', \DB::raw(\addcslashes("'{$class}'", '\\')))
                    ->on('pivot_viewable.viewable_id', '=', "{$table}.{$foreignKey}")
                    ->on("pivot_viewable.{$foreignKey}", '=', "{$table}.viewable_id")
                    ->where('pivot_viewable.collection', '=', 'view');
            });
    }

    /**
     * Update only specific field value in elasticsearch
     */
    public function scriptUpdate($params = [])
    {
        dispatch_sync(new ScriptedDocumentUpdate($this, $params));
    }

    /**
     * Handle when someone viewed the company
     */
    public function recentlyViewedBy($visitor)
    {
        $this->scriptUpdate([
            'num_viewers' => $this->num_viewers
        ]);
    }

    /**
     * Get nationalities.
     *
     * @return array
     */
    public function getNationalitiesArrAttribute()
    {
        if (isset($this->terms['nationalities'])) {
            return $this->terms['nationalities']->map(function ($item) {
                return [
                    'national' => $item->name,
                    'flag' => $item->image
                ];
            })->toArray();
        }

        return [];
    }

    /**
     * Get num_employees.
     *
     * @return integer
     */
    public function getNumEmployeesIdAttribute()
    {
        return $this->num_employees->pluck('id')->first();
    }

    /**
     * Get nationalities.
     *
     * @return string
     */
    public function getNationalitiesStrAttribute()
    {
        return collect($this->nationalities_arr)->pluck('national')->implode(', ');
    }

    /**
     * Get status for the model.
     *
     * @return string
     */
    public function getStatusDisplayAttribute()
    {
        $list = static::getStatusDescription();
        return $list[$this->status] ?? 'None';
    }

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'companies_ams_v3';
    }

    //Refactor import es
    public function importSearchableAs()
    {
        return 'companies_ams_v3';
    }

    public function importSearchableUsing()
    {
        return new CompanyImportSource();
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        \Log::info('Build elasticsearch company: ' . $this->getKey());

        $this->loadMissing('meta', 'media', 'products.media', 'introduces.media', 'taxonomies.term', 'jobs.subscriptions.term', 'views', 'addresses', 'blogs.meta');
        $latest_job = $this->onlyJobOpen()->orderByDesc('refreshed_at')->first();

        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'email' => $this->email,
            'phone' => $this->phone,
            'website' => $this->website,
            'display_name' => $this->display_name,
            'tagline' => $this->tagline,
            'description' => $this->description,
            'description_str' => $this->description_str,
            'slug' => $this->slug,
            'recruitment_process' => $this->recruitment_process,
            'status' => $this->status,
            'status_display' => $this->status_display,
            'products' => $this->getProducts(),
            'introduces' => $this->getIntroduces(),
            'news' => $this->getBlogs(),
            'benefits' => $this->benefits,
            'faqs' => $this->faqs,
            'social_network' => $this->social_network,
            'company_size' => $this->getTaxonomies('num_employees')->map->term->map->name->first(),
            'employees' => $this->employees->pluck('id')->all(),
            'members' => $this->members,

            /*
            |--------------------------------------------------------------------------
            | Media
            |--------------------------------------------------------------------------
            |
            |  Nếu không có ảnh cover thì sẽ lấy ảnh đầu tiên của galleries làm cover.
            |
            */
            'logo' => $this->image_logo->isNotEmpty() ? $this->image_logo->first()->toArray() : null,
            'cover' => $this->image_cover->isNotEmpty() ? $this->image_cover->first()->toArray()
                : ($this->image_galleries->isNotEmpty() ? $this->image_galleries->first()->toArray() : null),

            'image_logo' => !current($this->image_logo_url) ? null : current($this->image_logo_url),
            'image_cover' => !current($this->image_cover_url) ? (!current($this->image_galleries_url) ? null : current($this->image_galleries_url)) : current($this->image_cover_url),
            'logo_group' => !current($this->logo_group_url) ? null : current($this->logo_group_url),
            'cover_group' => !current($this->cover_group_url) ? null : current($this->cover_group_url),
            'image_galleries' => $this->image_galleries->toArray(),

            /*
            |--------------------------------------------------------------------------
            | Addresses
            |--------------------------------------------------------------------------
            */
            'addresses' => [
                'address_region_ids' => $this->address_region_ids,
                'address_region_list' => $this->address_region_list,
                'address_region_array' => $this->address_region_array,
                'full_addresses' => $this->full_addresses,
                'sort_addresses' => $this->short_addresses,
                'collection_addresses' => $this->collection_addresses,
                'address_short_region_list' => $this->address_short_region_list,
            ],

            /*
            |--------------------------------------------------------------------------
            | Urls
            |--------------------------------------------------------------------------
            */
            'follow_url' => $this->follow_url,
            'detail_url' => $this->detail_url, //add new

            /*
            |--------------------------------------------------------------------------
            | Aggregations
            |--------------------------------------------------------------------------
            */
            'num_viewers' => $this->num_viewers,
            'num_jobs' => $this->num_jobs,
            'num_job_openings' => $this->num_job_openings,
            'num_followers' => $this->num_followers,
            'num_job_free' => $this->countJobBy('free', Job::STATUS_OPEN),
            'num_job_basic' => $this->countJobBy('basic', Job::STATUS_OPEN),
            'num_job_basic_plus' => $this->countJobBy('basic-plus', Job::STATUS_OPEN),
            'num_job_distinction' => $this->countJobBy('distinction', Job::STATUS_OPEN),
            'num_employees' => $this->getTaxonomies('num_employees')->pluck('id')->first(), //add new

            /*
            |--------------------------------------------------------------------------
            | Skills
            |--------------------------------------------------------------------------
            */
            'extra_skills' => $this->extra_skills_name->all(),

            'skills_ids' => $this->skills_id->all(),
            'skills_arr' => $this->skills_name->all(),
            'skills_str' => $this->skills_name->implode(', '),

            'categories_ids' => $this->categories_id->all(),
            'categories_arr' => $this->categories_name->all(),
            'categories_str' => $this->categories_name->implode(', '),

            'industries_ids' => $this->industries_id->all(),
            'industries_arr' => $this->industries_name->all(),
            'industries_str' => $this->industries_name->implode(', '),

            'nationalities_arr' => $this->nationalities_arr,
            'nationalities_str' => $this->nationalities_str,
            'nationalities' => $this->nationalities->values()->toArray(),

            /*
            |--------------------------------------------------------------------------
            | Services
            |--------------------------------------------------------------------------
            */

            'scoring_by_basicplus' => $this->scoring_by_basicplus,
            'scoring_by_distinction' => $this->scoring_by_distinction,
            'is_group' => !empty($this->members),
            'features' => $this->features()->active()->get()->pluck('feature')->values()->all(),
            'packages' => $this->packages()->get()->pluck('slug')->all(),

            /*
            |--------------------------------------------------------------------------
            | Timestamps
            |--------------------------------------------------------------------------
            */
            'created_at' => empty($this->created_at) ? null : $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => empty($this->updated_at) ? null : $this->updated_at->format('Y-m-d H:i:s'),
            'latest_posted_at' => empty($latest_job) ? null : ($latest_job->refreshed_at->format('Y-m-d H:i:s') ?? null),

            /*
            |--------------------------------------------------------------------------
            | Schema
            |--------------------------------------------------------------------------
            */
            'schema_local_business' => $this->schema_local_business,
            'schema_job_posting' => $this->getEmployOverviewAttribute(),
            // 'schema_employer_aggregate_rating' => $this->getEmployerAggregateRatingAttribute(),

            //Sap bo roi
            'spotlight' => $this->spotlight,
            'is_spotlight' => $this->is_spotlight,
            'highlight' => $this->highlight,
            'is_highlight' => $this->is_highlight,
            'is_free' => $this->isFree(),
            'is_basic' => $this->isBasic(),
            'is_basic_plus' => $this->isBasicPlus(),
            'is_distinction' => $this->isDistinction(),

            //add new attribute members
            // 'statisties' => [
            //     'title' => $this->title_statisties,
            //     'data' => $this->statisties
            // ],
            // 'image_statisties' => $this->image_statisties->toArray(),
            // 'middle_description_group' => !empty($this->middle_description_group) ? $this->middle_description_group : null,
            // 'layout_group' => !empty($this->layout_group) ? $this->layout_group : null,
            // 'bottom_introduces_group' => $this->getBottomIntroducesGroup(),
            // 'head_introduces_group' => $this->getHeadIntroducesGroup(),

            /*
            |--------------------------------------------------------------------------
            | Meta for SEO
            |--------------------------------------------------------------------------
            */
            'meta_title_vi' => $this->translate('vi')->meta_title ?? '',
            'meta_keywords_vi' => $this->translate('vi')->meta_keywords ?? '',
            'meta_description_vi' => $this->translate('vi')->meta_description ?? '',

            'meta_title_en' => $this->translate('en')->meta_title ?? '',
            'meta_keywords_en' => $this->translate('en')->meta_keywords ?? '',
            'meta_description_en' => $this->translate('en')->meta_description ?? '',
        ];
    }

    /**
     * Get schema local business for the company.
     *
     * @return array
     */
    public function getSchemaLocalBusinessAttribute()
    {
        return json_encode([
            '@context' => 'http://schema.org',
            '@type' => 'LocalBusiness',
            'name' => $this->display_name,
            'image' => (!empty($this->image_galleries->toArray())) ? ($this->image_galleries->map(function ($image) {
                return $image->getFullUrl();
            })) : (!empty($this->getFirstMediaUrl('image_logo')) ? $this->getFirstMediaUrl('image_logo') : null),
            'description' => $this->description_str,
            'url' => $this->detail_url,
            'telephone' => $this->phone,
            'location' => [
                '@type' => 'Place',
                'address' => $this->addresses->map(function ($address) {
                    return [
                        '@type' => 'PostalAddress',
                        'addressRegion' => $address->province_name,
                        'postalCode' => $address->postal_code,
                        'addressCountry' => 'VN', // <-- problem
                        'addressLocality' => $address->district_name,
                        'streetAddress' => $address->full_address
                    ];
                })->toArray(),
            ],
        ]);
    }

    /**
     * https://schema.org/employerOverview
     *
     * @return array
     */
    public function getEmployOverviewAttribute()
    {
        return json_encode([
            '@context' => 'http://schema.org',
            '@type' => 'JobPosting',
            'title' => $this->display_name,
            //???
            'datePosted' => empty($this->created_at) ? null : $this->created_at->format('Y-m-d'),
            'description' => $this->description_str,
            'hiringOrganization' => [
                '@type' => 'Organization',
                'name' => $this->display_name,
                'sameAs' => $this->detail_url,
                'logo' => $this->getFirstMediaUrl('image_logo'),
            ],
            'jobLocation' => [
                '@type' => 'Place',
                'address' => $this->addresses->map(function ($address) {
                    return [
                        '@type' => 'PostalAddress',
                        'addressRegion' => $address->province_name,
                        'postalCode' => $address->postal_code,
                        'addressCountry' => 'VN', // <-- problem
                        'addressLocality' => $address->district_name,
                        'streetAddress' => $address->full_address
                    ];
                })->toArray(),
            ],
            //???
            'employerOverview' => implode(
                '. ',
                array_filter(
                    collect($this->benefits)->map(function ($e) {
                        return $e['value'] ?? null;
                    })->toArray()
                )
            ),
        ]);
    }

    /**
     * https://schema.org/EmployerAggregateRating
     *
     * @return array
     */
    public function getEmployerAggregateRatingAttribute()
    {
        return json_encode([
            '@context' => 'http://schema.org',
            '@type' => 'EmployerAggregateRating',
            'description' => 'ACME Corp được 4/5 sao với tư cách là nhà tuyển dụng dựa trên 42 xếp hạng.',
            'itemReviewed' => [
                '@type' => 'Tổ chức',
                'name' => 'ACME Corp',
            ],
            'ratingCount' => 42,
            'ratingValue' => 4,
            'worstRating' => 1,
            'bestRating' => 5,
        ]);
    }

    /**
     * Get the value used to index the model.
     *
     * @return mixed
     */
    public function getScoutKey()
    {
        return $this->id;
    }

    /**
     * Get total view for the company.
     *
     * @return integer
     */
    public function getNumViewersAttribute()
    {
        if (array_key_exists('views_count', $this->attributes)) {
            return $this->attributes['views_count'] + $this->num_viewers_fake;
        }

        return $this->views->count() + $this->num_viewers_fake;
    }

    /**
     * @return boolean
     */
    public function isActive()
    {
        return ($this->status == self::STATUS_ACTIVE);
    }

    /**
     * @return Builder
     */
    public function scopeActive(Builder $builder)
    {
        return $builder->where('status', self::STATUS_ACTIVE);
    }

    /**
     * @return boolean
     */
    public function isInactive()
    {
        return ($this->status == self::STATUS_INACTIVE);
    }

    /**
     * @return Builder
     */
    public function scopeInactive(Builder $builder)
    {
        return $builder->where('status', self::STATUS_INACTIVE);
    }

    /**
     * Get status for the model.
     *
     * @return array
     */
    public static function getStatusDescription()
    {
        $status = [
            self::STATUS_ACTIVE => 'Active',
            self::STATUS_INACTIVE => 'Inactive',
            self::STATUS_REVIEW => 'Review',
        ];

        if (FeatureFlag::isEnabledFreePost()) {
            $status[self::STATUS_WAITING] = 'Waiting';
        }

        return $status;
    }

    /**
     * @inheritdoc
     */
    public function traceActivityBy()
    {
        return $this->display_name;
    }

    public function packages()
    {
        // Note: load meta before access addresses.
        return $this->customRelation(
            \Modules\Subscription\Entities\Package::class,
            // add constraints
            function ($relation) {
                $relation
                    ->getQuery()
                    ->join('posts', function ($join) {
                        if ($this->getKey()) {
                            $join->where('posts.owned_id', $this->getKey());
                        }
                    })
                    ->join('term_relationships', function ($join) {
                        $join
                            ->on('term_taxonomy.id', '=', 'term_relationships.taxonomy_id')
                            ->on('term_relationships.gables_id', '=', 'posts.id')
                            ->where('term_relationships.gables_type', app(Job::class)->getMorphClass());
                    })
                    ->groupBy('term_taxonomy.id');
            },

            // add eager constraints
            function ($relation, $companies) {
                $relation
                    ->getQuery()
                    ->whereIn('posts.owned_id', collect($companies)->pluck('id')->all())
                    ->with([
                        'jobs' => function ($query) use ($companies) {
                            $query->whereIn('owned_id', collect($companies)->pluck('id')->all());
                        }
                    ]);
            },
            function (array $companies, Collection $results, $relation, $customRelation) {
                if ($results->isEmpty()) {
                    return $companies;
                }

                foreach ($companies as $company) {
                    $company->setRelation(
                        $relation,
                        $results->filter(function (Package $package) use ($company) {
                            return in_array($company->getKey(), $package->jobs->pluck('owned_id')->unique()->all());
                        })->unique('id')->values()
                    );
                }

                return $companies;
            }
        );
    }

    /**
     * Set frequently asked questions meta attributes
     *
     * @param mixed $value
     * @return void
     */
    public function setFaqsAttribute($value)
    {
        $questions = collect($value)->map(function($question) {
            if (empty($question['answer']) || !isset($question['active'])) {
                $question['active'] = 0;
            }

            return $question;
        });

        $this->addMetaCollection($questions->toArray(), 'faqs');
    }

    /*
    |--------------------------------------------------------------------------
    | Custom audits
    |--------------------------------------------------------------------------
    */

    protected $oldValuesAudit = [];

    public function setOldValuesAudit(array $values)
    {
        $this->oldValuesAudit = $values;
        return $this;
    }

    public function getOldValuesAudit()
    {
        return $this->oldValuesAudit;
    }

    public function transformAudit(array $values): array
    {
        return array_merge($values, array_filter([
            'old_values' => $this->oldValuesAudit,
            'new_values' => AuditTransformer::make($this)
        ]));
    }

    public function personCharge(): BelongsToMany
    {
        return $this->belongsToMany(Administrator::class, 'company_admin_user', 'company_id', 'user_id');
    }

    public function companyCreditLogs(): HasMany
    {
        return $this->hasMany(CompanyCreditLog::class, 'company_id');
    }

    public function companySearchPackages(): HasMany
    {
        return $this->hasMany(CompanySearchPackage::class, 'company_id');
    }

    /**
     * @param string $emailsCcJob
     * @return array
     */
    public function getEmailsCcCompany(string $emailsCcJob): array
    {
        $emailsCcCompany = $this->meta()->where('key','emails_cc')->first()->value ?? "";
        return array_filter(array_merge(explode(',', $emailsCcCompany), explode(',', $emailsCcJob)));
    }

    public function getBenefitAttribute()
    {
        return collect($this->benefits)->pluck('value')->implode('<br>');
    }

    public function setBenefitAttribute($value)
    {
        $this->benefits = [[
            'icon' => null,
            'value' => $value
        ]];
    }

    public function getErcUrlAttribute()
    {
        $erc = $this->erc->first();

        if (is_null($erc)) {
            return null;
        }

        return $erc->getFullUrl();
    }
}
