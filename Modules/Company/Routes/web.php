<?php

use Illuminate\Routing\Router;
use Modules\Company\Http\Controllers\InvoiceController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('admin')->group(function (Router $router) {
    $router->resource('/companies', CompanyController::class);
    Route::post('/companies/{id}/approval', 'CompanyController@approval');
});
Route::prefix('/admin/ajax/companies')->group(function () {
    Route::get('/', 'CompanyController@companies');

    Route::get('/addresses', 'CompanyController@addresses');

    Route::get('/person-charge', 'CompanyController@getPersonInCharge');
});
