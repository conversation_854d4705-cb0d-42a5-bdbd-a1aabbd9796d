<?php

namespace Modules\Company\Http\Controllers;

use App\Helpers\CrmApi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Event;
use Modules\Company\Transformers\AuditTransformer;
use Modules\Company\Transformers\AuditTransformer as CompanyAuditTransformer;
use Modules\Taxonomy\Repositories\Contracts\TaxonomyRepositoryInterface;
use Amscore\Admin\Controllers\AdminController;
use App\Contracts\Form;
use Amscore\Admin\Grid;
use Amscore\Admin\Show;
use Amscore\Admin\Widgets\Table;
use Modules\Taxonomy\Entities\Terms;
use Modules\Company\Entities\Company;
use Modules\VietnamArea\Entities\VietnamArea;
use App\Actions\Restore;
use Modules\Company\Actions\SearchEmployer;
use Modules\Company\Actions\ShowDetailFrontend;
use Modules\Company\Actions\ShowGroupFrontend;
use Modules\Company\Actions\PersonInChargeCompany;
use Amscore\Admin\Form\NestedForm;
use Amscore\Admin\Auth\Database\Administrator;
use Illuminate\Support\Facades\DB;
use Amscore\Admin\Facades\Admin;
use App\Helpers\FeatureFlag;
use Modules\Job\Entities\Candidate;
use Modules\Job\Entities\Job;
use OwenIt\Auditing\Models\Audit;

class CompanyController extends AdminController
{
    /**
     * @var TaxonomyRepositoryInterface
     */
    protected $taxonomyRepository;

    protected $list;

    public function __construct(TaxonomyRepositoryInterface $taxonomy)
    {
        $this->taxonomyRepository = $taxonomy;
    }

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Companies';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Company());

        $grid->disableExport();
        $grid->setResource(url('/admin/companies'));

        $grid->quickSearch(function ($model, $query) {
            $model->where(function ($que) use ($query) {
                $que->orwhere('companies.id', $query)
                    ->orWhere('companies.display_name', 'LIKE', "%{$query}%");
            });
        });

        $grid->filter(function ($filter) {
            // Range filter, call the model's `onlyTrashed` method to query the soft deleted data.
            $filter->scope('pending', 'Pending')->onlyVersion();
            $filter->scope('trashed', 'Recycle Bin')->onlyTrashed();
        });

        $grid->filter(function ($filter) {
            $filter->where(function ($query) {
                $query->whereHas('personCharge', function ($query) {
                    $query->where('user_id', $this->input);
                });
            }, 'Person In Charge')->select(Administrator::get()->pluck('username', 'id')->all());
        });

        $grid->filter(function ($filter) {
            $filter->expand();
            $filter->column(1 / 2, function ($filter) {
            });
            $filter->column(1 / 2, function ($filter) {
                $filter->where(function ($query) {
                    $query->WithAnyTerms($this->input, 'thanh-pho');
                }, 'Nationalities ')->multipleSelect(
                    app(Terms::class)->selectTerm('nationalities')->pluck('name', 'id')
                );
            });
        });

        $grid->column('id', __('Id'));

        $grid->column('image_logo_url', 'Logo')->image(null, 70);

        $grid->column('Company')->display(function () {
            $info = '<b>Company Name: </b><a href="' . $this->detail_url . '" target="_blank" >' . e($this->display_name) . '</a><br>' .
                ($this->email ? '<b>Email:</b> ' . $this->email . '<br>' : '') .
                ($this->phone ? '<b>Phone:</b> ' . $this->phone . '<br>' : '') .
                ($this->full_addresses ? '<b>Địa chỉ:</b> ' . join('<br>', $this->full_addresses) . '<br>' : '') .
                ($this->audits->first() ? '<b>Created by:</b> ' . ($this->audits->first()->user->email ?? 'Administrator') . '<br>' : '') .
                ($this->audits->last() ? '<b>Last modified by:</b> ' . ($this->audits->last()->user->email ?? 'Administrator') . ' ' .  $this->audits->last()->created_at . '<br>' : '') .
                (($this->personCharge->isNotEmpty()) ? '<b>Person In Charge:</b> ' . $this->personCharge->pluck('name')->implode(', ') . '<br>' : '' ).
                ($this->status_display == 'Inactive' ? '<span class="label label-default"> ' . $this->status_display . '</span>' : '<span class="label label-success"> ' . $this->status_display . '</span>');

            return $info;
        })->expand(function ($model) {
            $jobs = $model->jobs->map(function ($job) {
                return $job->only(['id', 'title', 'status_display', 'expires_at' ,'created_at']);
            });
            return new Table(['ID', 'Job', 'Status', 'Expires at', 'Create at'], $jobs->toArray());
        });

        $grid->column('Employer')->display(function () {
            $list = '';
            $numItems = count($this->employees);
            $i = 0;
            foreach ($this->employees as $key => $value) {
                $list .= '<b>Employer Name: </b> <a href="/admin/employers/'.$value->getKey().'/edit"> ' . e($value->display_name) . '</a><br>' .
                ($value->email ? '<b>Email:</b> ' . $value->email . '<br>' : '') .
                ($value->phone ? '<b>Phone:</b> ' . $value->phone . '<br>' : '') .
                ($value->firstname ? '<b>First Name:</b> ' . $value->firstname . '<br>' : '') .
                ($value->lastname ? '<b>Last Name:</b> ' . $value->lastname . '<br>' : '') .
                ($value->position ? '<b>Position:</b> ' . $value->position . '<br>' : '') .
                ($value->approved_at ? '<b>Approve:</b> ' . $value->approved_at . '<br>' : '') .
                (++$i !== $numItems ? '<hr>' : '');
            }
            return $list;
        });

        $grid->column('status', __('Active'))->switch([
            'on' => ['value' => 1],
            'off' => ['value' => 2],
        ]);

        $grid->column('Features')->display(function () {
            $labels = $this->is_spotlight ? '<span class="label label-success">Spotlight</span></br>' : '<span class="label label-default">Spotlight</span></br>';
            $labels = $this->is_highlight ? $labels . '<span class="label label-success">Highlight</span>' : $labels . '<span class="label label-default">Highlight</span>';

            return $labels;
        });

        $grid->actions(function (Grid\Displayers\Actions $actions) {
            if (\request('_scope_') == 'trashed') {
                $actions->add(new Restore());
                $actions->disableDelete();
            }

            $actions->add(new SearchEmployer());
            $actions->add(new ShowDetailFrontend());
            $actions->add(new PersonInChargeCompany());

            if ($actions->row->childrens_count > 0) {
                $actions->add(new ShowGroupFrontend());
            }
        });

        $grid->model()->collection(function ($collection) {
            return $collection->map->append([
                'image_logo_url', 'highlight', 'spotlight'
            ]);
        });

        $grid
            ->model()
            ->with(['taxonomies.term.translations', 'meta', 'media', 'addresses', 'features.term', 'employer', 'jobs', 'employees', 'audits', 'personCharge'])
            ->withCount('childrens')
            ->orderBy('id', 'desc');

        // No need to load translations at this time
        // Because no display on the grid
        $grid->model()->getOriginalModel()->disableAutoloadTranslations();

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Company::withoutGlobalScopes()->findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('uuid', __('Uuid'));
        $show->field('display_name', __('Display name'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));
        $show->field('deleted_at', __('Deleted at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form($id = null)
    {
        if (!empty($id)) {
            $form = new Form(Company::withoutGlobalScopes());
        } else {
            $form = new Form(new Company());
        }

        $taxonomies = $this->taxonomyRepository->getModel()
                ->with('term.translations')
                ->whereIn('taxonomy', ['categories', 'skills', 'industries', 'nationalities', 'num_employees', 'services'])->get()
                ->groupBy(function ($item, $key) {
                    return $item->taxonomy;
                });

        $form->tab('Basic info', function ($form) {
            $form->text('display_name', __('Display name'))->rules('required');
            $form->select('status', __('Status'))->options(
                Company::getStatusDescription()
            )->rules('required')->default(2);

            $form->email('email', __('Email'))->icon('fa-email');
            $form->text('phone', __('Phone'))->icon('fa-phone');

            $form->text('tagline', __('tagline'));
            $form->text('website', __('Website'))->icon('fa-globe');
            $form->text('tax_number', __('Tax'))->icon('fa-usd');

            $form->tags('emails_cc', 'Send New Candidate Emails To');

            $form->table('social_network', __('Social Network'), function ($table) {
                $table->select('social')->options(config('constant.SNS'));
                $table->text('url', __('Url'));
            });

            $provinces = VietnamArea::province()->pluck('name', 'code as id');

            $form
                ->hasMany('addresses', function (NestedForm $form) use ($provinces) {
                    $form->hidden('display_name', 'Display name');
                    $form->text('street', 'Street');

                    $form->select('province_id', 'Province')->options($provinces)
                            ->load('district_id', '/admin/vietnam/api/district');

                    $form->select('district_id', 'District')->options(function ($id) {
                        return VietnamArea::options($id);
                    })->load('ward_id', '/admin/vietnam/api/ward');

                    $form->select('ward_id', 'Ward')->options(function ($id) {
                        return VietnamArea::options($id);
                    });
                })
                ->showOriginal()
                ->showPrevious()
                ->auditShowModal();

            $form
                ->tinymce('description', __('Description'))
                ->showOriginal()
                ->showPrevious()
                ->auditShowModal();

            if (FeatureFlag::isEnabledFreePost()) {
                $form->divider();

                $form->file('erc_url')->removable()->rules('mimes:pdf');
            }
        })
        /**
         * Tab Recruitment Process
         *
         */
        ->tab('Recruitment Process', function ($form) {
            $form->sortableTable('recruitment_process', __('Recruitment process <br>(max 8 steps)'), function ($table) {
                $table
                    ->text('name', __('name'))
                    ->attribute(['maxlength' => 100])
                    ->help('Tối đa 100 ký tự, KHÔNG nhập IN HOA toàn bộ')
                    ->rules('min:3|max:100');
            })->setMaxLimit(8);
        })
        /**
         * Tab products
         *
         */
        ->tab('Products', function ($form) {
            $form->hasMany('products', '', function (NestedForm $form) {
                $form->text('name', __('Name'))->rules('required');
                $form->image('image');
                $form->textarea('description', __('Description'));
                $form->url('link', __('Link'))->attribute(['data-links' => 'company_product_link']);
            });
        })
        /**
         * Tab introduces
         *
         */
        ->tab('Introduces', function ($form) {
            $form->hasMany('introduces', '', function (NestedForm $form) {
                $form->text('name', __('Name'))->rules('required');
                $form->image('image');
                $form->text('description', __('Description'));
            });
        })
        /**
         * Tab quan ly noi dung.
         *
         */
        ->tab('Content', function ($form) {
            $form->tinymce('benefit', 'Job Benefit');
            $form->table('faqs', 'Fequently ask question', function ($table) {
                $table->text('question', 'Question')->required();
                $table->text('answer', 'Answer');
                $table
                    ->switch('active')
                    ->default(1)
                    ->states([
                        'on'  => ['value' => 1, 'text' => 'Enable', 'color' => 'success'],
                        'off' => ['value' => 0, 'text' => 'Disable', 'color' => 'danger'],
                    ]);
            });
        })
        /**
         * Tab quan ly taxonomy.
         *
         */
        ->tab('Taxonomy', function ($form) use ($taxonomies) {
            $form->multipleSelect('categories_id', 'Categories')->options(
                empty($taxonomies->get('categories')) ? [] : $taxonomies->get('categories')->pluck('term.name', 'id')
            );

            $form->select('num_employees_id', __('Employees'))->options(
                empty($taxonomies->get('num_employees')) ? [] : $taxonomies->get('num_employees')->pluck('term.name', 'id')
            );

            if (Admin::user()->isAdministrator()) {
                $form->tags('skills_name', 'Best skills')->options(
                    empty($taxonomies->get('skills')) ? [] : $taxonomies->get('skills')->pluck('term.name', 'id')
                );
            } else {
                $form->multipleSelect('skills_name', 'Best skills')->options(
                    empty($taxonomies->get('skills')) ? [] : $taxonomies->get('skills')->pluck('term.name', 'term.name')
                );
            }

            $form->tags('extra_skills_name', 'Extra skills')->options(
                empty($taxonomies->get('skills')) ? [] : $taxonomies->get('skills')->pluck('term.name', 'id')
            );

            if (Admin::user()->isAdministrator()) {
                $form->tags('industries_name', 'Industries')->options(
                    empty($taxonomies->get('industries')) ? [] : $taxonomies->get('industries')->pluck('term.name', 'id')
                );
            } else {
                $form->multipleSelect('industries_name', 'Industries')->options(
                    empty($taxonomies->get('industries')) ? [] : $taxonomies->get('industries')->pluck('term.name', 'term.name')
                );
            }


            $form->tags('nationalities_name', 'Nationalities')->options(
                empty($taxonomies->get('nationalities')) ? [] : $taxonomies->get('nationalities')->pluck('term.name', 'id')
            );
        })
        /**
         * Tab quan ly file.
         *
         */
        ->tab('Media', function ($form) {
            $form->multipleImage('image_logo_url', 'Image logo')->removable();
            $form->multipleImage('image_cover_url', 'Image cover')->removable();
            $form->multipleImage('logo_group_url', 'Logo Group')->removable();
            $form->multipleImage('cover_group_url', 'Cover Group')->removable();
            $form->multipleImage('image_galleries_url', 'Image galleries')->removable();
        })
        /**
         * Tab quan ly cac tinh nang.
         *
         */
        ->tab('Feature', function ($form) use ($taxonomies) {
            $form->table('services', 'Services', function ($table) use ($taxonomies) {
                $table->select('name', 'Service')
                    ->options(
                        empty($taxonomies->get('services')) ? [] : $taxonomies->get('services')->pluck('term.name', 'id')
                    )->rules('required');
                $table->text('expires_in', 'Expires in (days)')->help('0 là hết hạn, Null là vô  cực :))');
            });
        })
        /**
         * Tab Nhóm Cty
         *
         */
        ->tab('Members', function ($form) {
            $form->multipleSelect('members')->options(function ($values) {
                if (empty($values)) {
                    return [];
                }

                return Company::select('id', DB::raw('CONCAT( display_name, " (#", id, ")" ) as text'))
                        ->find($values)->pluck('text', 'id');
            })->ajax('/admin/ajax/companies');

            $form->select('layout_group', 'Layout')->options(['layout_default' => 'layout_default', 'layout_bank' => 'layout_bank']);
            $form->text('middle_description_group', ('Middle description'));

            $form->multipleImage('image_statisties_url', 'Image statisties')->removable();

            $form->divider();

            $form->text('title_statisties', ('Title Statisties'));
            $form->table('statisties', __('Statisties'), function ($table) {
                $table->text('name', __('name'));
                $table->text('description', __('description'));
            });

            $form->divider();

            $form->hasMany('headIntroducesGroup', 'Head Introduces', function (NestedForm $form) {
                $form->text('name_head', __('Title'))->rules('required');
                $form->editor('head_introduces_group_content', __('Description'));
            });

            $form->hasMany('bottomIntroducesGroup', 'Bottom Introduces', function (NestedForm $form) {
                $form->text('name_bottom', __('Title'))->rules('required');
                $form->image('image');
                $form->editor('bottom_introduces_group_content', __('Description'));
            });
        })
        /**
         * Tab Blog
         *
         */
        ->tab('Blog', function ($form) {
            $form->hasMany('blogs', '', function (NestedForm $form) {
                $form->text('link_blog', __('Link Blog'))->rules('required');
            });
        })
        /**
        * SEO tab
        */
       /*->tab('SEO', function ($form) {
           $form
                ->text('meta_title', __('Meta title'))
                ->attribute(['maxlength' => 250])
                ->rules('max:250');

           $form
                ->text('meta_keywords', __('Meta keywords'))
                ->attribute(['maxlength' => 250])
                ->rules('max:250');

           $form
                ->textarea('meta_description', __('Meta description'))
                ->attribute(['maxlength' => 250])
                ->rules('max:250');
       })*/
        /**
         * Note tab
         */
        ->tab('Note', function ($form) {
            $form->table('note', __('Note'), function ($table) {
                $table->text('name');
                $table->textarea('value');
            });
        });

        $form->model()->disableCache()->with('media', 'meta', 'taxonomies.term.translations', 'addresses', 'products.media', 'introduces.media', 'blogs.meta');

        $form->saving(function(Form $form) {
            $form->model()->disableAuditing();
        });

        // Update lastest audit new values
        $form->saved(function(Form $form) {
            $company = $form->model();
            $company->refresh();

            $audit = new Audit();
            $audit->user_type = get_class(auth()->user());
            $audit->user_id = auth()->id();

            if (request()->isMethod('POST')) {
                $audit->event = 'created';
                $audit->old_values = [];
            } else {
                $audit->event = 'updated';
                $audit->old_values = $company->getOldValuesAudit();
            }
            $audit->new_values = CompanyAuditTransformer::make($company)->toArray(request());
            $audit->auditable_type = Company::class;
            $audit->auditable_id = $company->id;
            $audit->url = request()->url();
            $audit->ip_address = request()->ip();
            $audit->user_agent = request()->userAgent();
            $audit->save();
        });

        $form->append([
            'members',
            'image_logo_url',
            'image_cover_url',
            'image_galleries_url',
            'categories_id',
            'extra_skills_name',
            'skills_name',
            'industries_name',
            'nationalities_name',
            'num_employees_id',
            'services',
            'image_statisties_url',
            'middle_description_group',
            'layout_group',
            'bottomIntroducesGroup',
            'headIntroducesGroup',
            'logo_group_url',
            'cover_group_url',
            'benefit',
            'erc_url',
        ]);

        $form->beforeModified(function(Form $form) {
            $form->model()->setOldValuesAudit(
                CompanyAuditTransformer::make($form->model())->toArray(request())
            );
        });

        return $form;
    }

    public function companies(Request $request)
    {
        $q = $request->get('q');

        $query = Company::query()
            ->select('id', DB::raw('CONCAT( display_name, " (#", id, ")" ) AS text'))
            ->where('display_name', 'like', "%$q%");

        if ($status = $request->get('status')) {
            $query->where('status', $status);
        }

        return $query->paginate();
    }

    public function addresses(Request $request)
    {
        $companyId = $request->get('q');

        return Company::query()
            ->findOrFail($companyId)
            ->addresses
            ->map(function ($value, $item) {
                return [
                    'id' => $value['id'],
                    'text' => $value['street']
                ];
            });
    }

    public function getPersonInCharge(Request $request)
    {
        $keyword = $request->get('q');
        return Administrator::query()
            ->select('id',
                DB::raw('CONCAT(IFNULL(username,""), IFNULL(CONCAT(" | ", email),""), IFNULL(CONCAT(" | ", name),"")) as text'))
            ->where('name', 'like', "%$keyword%")
            ->paginate(null, ['id', 'text']);
    }

    public function availableInvoices(Request $request, $jobId = null)
    {
        $companyId = $request->get('q');
        $selectedInvoiceId = null;
        if ($companyId && $jobId) {
            $job = Job::whereId($jobId)->select('owned_id', 'crm_invoice_id')->first();
            if ($job->owned_id == $companyId) {
                $selectedInvoiceId = $job->crm_invoice_id;
            }
        }

        return $companyId ? CrmApi::getAvailableInvoices($companyId, $selectedInvoiceId) : [];
    }
}
