<?php

namespace Modules\Job\Services;

use App\Jobs\EmployerDash\AMSProcessJobForEmployerDash;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Stringable;
use Modules\Job\Http\Resources\JobTemplateResource;
use Modules\Job\Entities\Job;

class JobService
{
    /**
     * @param  Job  $job
     *
     * @return Job
     * @throws \Throwable
     */
    public function generateJobDescriptionHTML(Job $job): Job
    {
        $jobTemplate = $job->job_template->pluck('slug')->first();

        Log::info('JobService generateJobDescriptionHTML', [
            'jobTemplate' => $jobTemplate,
        ]);

        $htmlDesktop = $htmlMobile = $this->renderTemplate($job);

        Log::info('JobService generateJobDescriptionHTML render', [
            'job' => $job->id,
        ]);

        if ($job->level === Job::JOB_LEVEL_PAID) {
            foreach ([$htmlDesktop, $htmlMobile] as $html) {
                if (empty($html)) {
                    throw new \Exception('Failed to generate job description HTML');
                }
            }
        }

        $job->addmediaFromString($htmlDesktop)
            ->usingFileName($job->slug.'-'.$job->id.'_desktop.html')
            ->toMediaCollection('job_posting_html_desktop');
        $job->addmediaFromString($htmlMobile)
            ->usingFileName($job->slug.'-'.$job->id.'_mobile.html')
            ->toMediaCollection('job_posting_html_mobile');

        // Update job content HTML
        $this->updateJobContentHtml([
            'html_desktop' => $htmlDesktop,
            'html_mobile' => $htmlMobile,
        ], $job);

        return $job;
    }

    public function renderTemplate(Job $job): string
    {
        $jobResource = JobTemplateResource::make($job)->toArray();

        // $html = view("job::templates.job_posting.$jobTemplate", $jobResource)->render();
        $htmlContent = html_entity_decode($job->job_blade_html);
        $html = Blade::render($htmlContent, $jobResource);

        $html = $this->replaceColor($job, $html, $jobResource['color_name']);
        // $html = $this->replaceColor($html, '#0A4423');

        return $html;
    }

    public function handleAfterGenerateJobDescriptionHTML(Job $job, string $action): Job
    {
        $job = $this->updateJobStatus($job, $action);

        if ($action === AMSProcessJobForEmployerDash::ACTION_FORCE_PUBLISH && $job->status !== Job::STATUS_OPEN) {
            $this->publishJob($job);
            Log::info('Job published successfully with force publish', ['jobId' => $job->id, 'jobTitle' => $job->title]);
        }

        return $job;
    }

    public function publishJob(Job $job): Job
    {
        $job->update([
            'status' => Job::STATUS_OPEN,
            'published_at' => now(),
            'original_published_at' => now(),
            'expires_at' => now()->addDays($job->current_level_duration_in_days)->endOfDay(),
        ]);

        return $job;
    }

    public function updateJobStatus(Job $job, string $action): Job
    {
        if ($job->level === Job::JOB_LEVEL_FREE && $job->status === Job::STATUS_DRAFT) {
            $this->publishJob($job);

            Log::info('Free job level to skip generate job description HTML');
            Log::info('Set free job status to open', ['jobId' => $job->id, 'jobTitle' => $job->title]);
        } elseif ($job->status === Job::STATUS_OPEN && $action === AMSProcessJobForEmployerDash::ACTION_FORCE_PUBLISH) {
            Log::info('Paid job level is already open', ['jobId' => $job->id, 'jobTitle' => $job->title]);
        } elseif ($job->level === Job::JOB_LEVEL_PAID) {
            $job->update([
                'status' => Job::STATUS_REVIEW,
            ]);

            Log::info('Set paid job status to review', ['jobId' => $job->id, 'jobTitle' => $job->title]);
        } else {
            Log::error('Job status is draft', ['jobId' => $job->id, 'jobTitle' => $job->title]);
        }

        return $job;
    }

    public function updateJobContentHtml(array $data, Job $job): Job
    {
        $job->update([
            'content_html_desktop' => preg_replace('/.*<body[^>]*>|<\/body>.*/si', '', $data['html_desktop']),
            'content_html_mobile' => preg_replace('/.*<body[^>]*>|<\/body>.*/si', '', $data['html_mobile']),
        ]);

        return $job;
    }

    /**
     * Hardcoded color replacement for job description HTML
     * No hope ...
     *
     * @param  Job  $job
     * @param  string  $html
     * @param  string  $colorName
     *
     * @return Stringable
     */
    public function replaceColor(Job $job, string $html, string $colorName): Stringable
    {
        $html = Str::of($html);

        $jobColorProperties = json_decode($job->color_properties, true);
        $colors = $jobColorProperties['color_properties'][$colorName] ?? [];

        foreach ($colors as $key => $color) {
            $html = $html->replace(strtoupper($key), strtoupper($color));
        }

        return $html;
    }

    /**
     * Hardcoded color replacement for job description HTML
     * php artisan tinker \Modules\Job\Services\JobService::updateTemplateColorFirst(9275)
     *
     * @param  int  $taxonomyTemplateId
     *
     * @return int
     */
    public static function updateTemplateColorFirst(int $taxonomyTemplateId): int
    {
        if (! $taxonomyTemplateId) {
            return 0;
        }

        /** @var \Modules\Taxonomy\Entities\Taxonomy $taxonomy */
        $taxonomy = \Modules\Taxonomy\Entities\Taxonomy::find($taxonomyTemplateId);

        $colors = [
            '#1D2754',
            '#223F99',
            '#183D67',
            '#08549E',
            '#B0DAF5',
            '#29357D',
            '#0071B8',
            '#2A296C',
            '#95C8EC',
            '#D6EBF9',
            '#18489D',
            '#2C65B0',
            '#54A6D6',
            '#21409A',
            '#0C97BB',
            '#025098',
            '#00348F',
            '#23356D',
            '#22285F',
            '#3771B8',
            '#22428D',
            '#23428d',
            '#2196F3',
            '#1875D2',
            '#0D47A0',
            '#2293EF',
            '#1577BD',
            '#E0F3FD',
            '#CBE7F6',
            '#E1F1F9',
            '#2B4A9F',
            '#D2EBFA',
            '#559FD7',
            '#3080C3',
            '#3E80C2',
            '#2F80C3',
            '#24336C',
            '#3393D1',
            '#1171B9',
            '#005D93',
            '#3666B0',
            '#E9F6FD',
        ];

        $colorTemplate = [];
        foreach ($colors as $color) {
            $colorTemplate[] = [
                'color' => $color,
                'description' => '',
            ];
        }

        $taxonomy->jobTemplate()->updateOrCreate(
            ['taxonomy_id' => $taxonomy->id],
            [
                'blade_html' => $taxonomy->job_template_blade_html,
                'colors' => $colorTemplate,
            ]
        );

        $colorSchemes['red'] = [
            "#1D2754" => "#640813",
            "#223F99" => "#640813",
            "#183D67" => "#971B1E",
            "#08549E" => "#C22026",
            "#B0DAF5" => "#FAC9D1",
            "#29357D" => "#640813",
            "#0071B8" => "#C52127",
            "#2A296C" => "#640813",
            "#95C8EC" => "#D0292D",
            "#D6EBF9" => "#FAC9D1",
            "#18489D" => "#9C1C21",
            "#2C65B0" => "#D0292D",
            "#54A6D6" => "#CE2B2C",
            "#21409A" => "#6A1010",
            "#0C97BB" => "#B92025",
            "#025098" => "#B92025",
            "#00348F" => "#640813",
            "#23356D" => "#640813",
            "#22285F" => "#870F10",
            "#3771B8" => "#A7282A",
            "#22428D" => "#F59698",
            "#23428d" => "#F59698",
            "#2196F3" => "#E95354",
            "#1875D2" => "#B41C1B",
            "#0D47A0" => "#6A1010",
            "#2293EF" => "#CE2B2C",
            "#1577BD" => "#B32025",
            "#E0F3FD" => "#FFE6EA",
            "#CBE7F6" => "#FAC9D1",
            "#E1F1F9" => "#FFE6EA",
            "#2B4A9F" => "#870F10",
            "#D2EBFA" => "#FFD7DE",
            "#559FD7" => "#971B1E",
            "#3080C3" => "#870F10",
            "#3E80C2" => "#B92025",
            "#2F80C3" => "#971B1E",
            "#24336C" => "#640813",
            "#3393D1" => "#870F10",
            "#1171B9" => "#CE2B2C",
            "#005D93" => "#B92025"
        ];
        $colorSchemes["blue"] = array_combine(
            array_keys($colorSchemes["red"]),
            array_keys($colorSchemes["red"])
        );
        $colorSchemes["green"] = [
            "#1D2754" => "#063420",
            "#223F99" => "#0A4423",
            "#183D67" => "#0A4423",
            "#08549E" => "#38813D",
            "#B0DAF5" => "#C8F1BC",
            "#0071B8" => "#459243",
            "#2C65B0" => "#186634",
            "#18489D" => "#14542E",
            "#D6EBF9" => "#CDE7CB",
            "#95C8EC" => "#1cb05b",
            "#2A296C" => "#0A4423",
            "#29357D" => "#0A4423",
            "#3666B0" => "#34a700",
            "#3771B8" => "#459243",
            "#54A6D6" => "#459243",
            "#21409A" => "#0A4423",
            "#0C97BB" => "#38813D",
            "#025098" => "#0A4423",
            "#00348F" => "#063420",
            "#23356D" => "#063420",
            "#22285F" => "#0A4423",
            "#22428D" => "#0A4423",
            "#23428d" => "#0A4423",
            "#2196F3" => "#89d287",
            "#1875D2" => "#279d57",
            "#0D47A0" => "#063420",
            "#2293EF" => "#459243",
            "#1577BD" => "#38813D",
            "#E0F3FD" => "#C8F1BC",
            "#CBE7F6" => "#ABE6A9",
            "#E1F1F9" => "#C8F1BC",
            "#2B4A9F" => "#0A4423",
            "#D2EBFA" => "#C8F1BC",
            "#559FD7" => "#14542E",
            "#3080C3" => "#0A4423",
            "#3E80C2" => "#38813D",
            "#2F80C3" => "#14542E",
            "#24336C" => "#063420",
            "#3393D1" => "#0A4423",
            "#1171B9" => "#459243",
            "#005D93" => "#38813D"
        ];
        $colorSchemes["yellow"] = [
            "#1D2754" => "#F9A61A",
            "#223F99" => "#F9A61A",
            "#08549E" => "#F9A61A",
            "#B0DAF5" => "#FDE4BA",
            "#183D67" => "#F9A61A",
            "#29357D" => "#63420A",
            "#0071B8" => "#F9A61A",
            "#2A296C" => "#F9A61A",
            "#95C8EC" => "#e3d780",
            "#D6EBF9" => "#FFF3A2",
            "#18489D" => "#EFCD13",
            "#2C65B0" => "#D5B729",
            "#54A6D6" => "#FFE56C",
            "#21409A" => "#F9A61A",
            "#00348F" => "#8A7200",
            "#025098" => "#FAC812",
            "#23356D" => "#D19400",
            "#0C97BB" => "#FDCF2E",
            "#3771B8" => "#ffcd78",
            "#3771B7" => "#ffcd78",
            "#22428D" => "#F9A61A",
            "#22285F" => "#815102",
            "#2196F3" => "#f2e789",
            "#24336C" => "#EFCD13",
            "#E9F6FD" => "#fff4ba",
            "#3393D1" => "#b09506",
            "#1171B9" => "#e3c000",
            "#3E80C2" => "#EFCD13",
            "#005D93" => "#D5B729",
            "#1577BD" => "#EFCD13",
            "#CBE7F6" => "#fff6a7",
            "#E0F3FD" => "#fff9c0",
            "#2F80C3" => "#f4b90b",
            "#E1F1F9" => "#fffdef",
            "#2B4A9F" => "#b2a42d",
            "#D2EBFA" => "#fffbd8",
            "#3080C3" => "#bdae2d"
        ];
        $colorSchemes['black'] = [
            "#1D2754" => "#3B3C3B",
            "#223F99" => "#292929",
            "#183D67" => "#3B3C3B",
            "#08549E" => "#5D5D5D",
            "#B0DAF5" => "#E6E6E6",
            "#29357D" => "#3B3C3B",
            "#0071B8" => "#3B3C3B",
            "#2A296C" => "#3B3C3B",
            "#95C8EC" => "#8d8d8d",
            "#D6EBF9" => "#E1E3E3",
            "#18489D" => "#3B3C3B",
            "#2C65B0" => "#5D5D5D",
            "#54A6D6" => "#3B3C3B",
            "#21409A" => "#3B3C3B",
            "#0C97BB" => "#3B3C3B",
            "#025098" => "#3B3C3B",
            "#00348F" => "#3B3C3B",
            "#23356D" => "#3B3C3B",
            "#22285F" => "#3B3C3B",
            "#3771B8" => "#3B3C3B",
            "#22428D" => "#3B3C3B",
            "#23428d" => "#3B3C3B",
            "#2196F3" => "#d4d4d4",
            "#1875D2" => "#959595",
            "#0D47A0" => "#3B3C3B",
            "#2293EF" => "#3B3C3B",
            "#1577BD" => "#5D5D5D",
            "#E0F3FD" => "#F4F4F4",
            "#CBE7F6" => "#C8C7C6",
            "#E1F1F9" => "#F4F4F4",
            "#2B4A9F" => "#292929",
            "#D2EBFA" => "#E4E4E4",
            "#559FD7" => "#3B3C3B",
            "#3080C3" => "#292929",
            "#3E80C2" => "#3F3F3E",
            "#2F80C3" => "#292929",
            "#24336C" => "#3B3C3B",
            "#3393D1" => "#3B3C3B",
            "#1171B9" => "#5D5D5D",
            "#005D93" => "#3B3C3B",
        ];

        $taxonomy->setJobTemplateColorPropertiesAttribute([
            'id' => $taxonomy->id,
            'description' => 'Color properties',
            'sort_order' => 0,
            'color_properties' => $colorSchemes,
        ]);

        return $taxonomyTemplateId;
    }
}
