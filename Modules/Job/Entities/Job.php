<?php

namespace Modules\Job\Entities;

use App\Helpers\CustomRelation;
use App\Helpers\FeatureFlag;
use Carbon\Carbon;
use App\Traits\HasSlug;
use App\Traits\UseUuid;
use App\Traits\HasStatus;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;
use Laravel\Scout\Searchable;
use App\Contracts\TopdevModel;
use App\Traits\GoogleIndexable;
use Modules\File\Entities\Media;
use Modules\Job\Enums\JobCategoryType;
use Modules\Job\Notifications\NotificationActivateNewPosting;
use Modules\Job\Traits\HasSimilarJob;
use Modules\Taxonomy\Entities\Taxonomy;
use Modules\User\Entities\User;
use Modules\File\Traits\HasFile;
use App\Traits\HasCustomRelations;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Jobs\ScriptedDocumentUpdate;
use Modules\Job\Traits\HasCandidate;
use Modules\Company\Entities\Company;
use Modules\Taxonomy\Traits\Taxoable;
use Notification;
use OwenIt\Auditing\Contracts\Auditable;
use Illuminate\Database\Eloquent\Builder;
use Modules\VietnamArea\Entities\Address;
use Spatie\MediaLibrary\MediaCollections\File;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\VietnamArea\Traits\AddressHelper;
use Amscore\Admin\Auth\Database\Administrator;
use Amscore\Admin\Form\Field\HasMany;
use App\Facades\Unleash;
use App\Helpers\CrmApi;
use Modules\Job\Traits\HackerRankGoWithTopdev;
use Modules\Job\Traits\SocialPostContentTrait;
use Modules\Job\Transformers\AuditTransformer;
use Arcanedev\LaravelNotes\Traits\HasManyNotes;
use Modules\Activity\Traits\InteractsWithViews;
use Modules\Announcement\Entities\Announcement;
use Modules\Activity\Contracts\ViewableContract;
use Overtrue\LaravelFollow\Traits\CanBeFollowed;
use Modules\Subscription\Traits\HasSubscriptions;
use Modules\Job\Traits\Contracts\GoWithHackerRank;
use Modules\Job\Database\Searchable\JobImportSource;
use Chelout\RelationshipEvents\Concerns\HasMorphManyEvents;
use Illuminate\Support\Arr;
use Modules\Company\Entities\CompanyJobPostingLog;
use Modules\Job\Entities\PersonChargeJob;
use Modules\Subscription\Entities\Package;
use Modules\VietnamArea\Entities\VietnamArea;

/**
 * Modules\Job\Entities\Job
 *
 * @method static Job find(int $job_id)
 * @property int $id
 * @property \Illuminate\Support\Collection $skills_name See Taxoable::termAttribute()
 * @property \Illuminate\Support\Collection $extra_skills_name
 * @property \Illuminate\Support\Collection $required_skills_name
 * @property \Illuminate\Support\Collection $optional_skills_name
 * @property \Illuminate\Support\Collection $optional_skills
 * @property \Illuminate\Support\Collection $required_skills
 * @property \Illuminate\Support\Collection $job_types_name
 * @property \Illuminate\Support\Collection $job_levels_name
 * @property \Illuminate\Support\Collection $experiences_name
 * @property Company $company
 * @property Carbon $published_at
 * @property Carbon|null $original_published_at
 * @property string $title
 * @property JobInformation $job_information
 * @property string $detail_url See Job::getDetailUrlAttribute()
 * @property string $preview_url See Job::getPreviewUrlAttribute()
 * @property Collection<Address> $addresses
 * @property string $uuid
 * @property string $slug
 * @property int $status
 * @property string|null $content
 * @property string|null $content_html_desktop
 * @property string|null $content_html_mobile
 * @property string|null $requirements
 * @property string|null $responsibilities
 * @property int|null $owned_id
 * @property int|null $parent_id
 * @property int|null $creator_id
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property string|null $emails_cc
 * @property array|null $hackerrank
 * @property string|null $lever_id
 * @property string|null $hot
 * @property string|null $blog_tags
 * @property array|null $blog_posts See Job::getBlogPostsAttribute()
 * @property string|null $sidebar_image_link
 * @property \Illuminate\Support\Carbon|null $refreshed_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $crm_invoice_id
 * @property int|null $crm_request_design_id
 * @property string|null $level
 * @property-read int|null $addresses_count
 * @property-read Collection|Announcement[] $announcement
 * @property-read int|null $announcement_count
 * @property-read Collection|\OwenIt\Auditing\Models\Audit[] $audits
 * @property-read int|null $audits_count
 * @property-read Administrator|null $author
 * @property-read Collection|Candidate[] $candidates
 * @property-read int $candidates_count
 * @property-read Collection|\CyrildeWit\EloquentViewable\View[] $clickApply
 * @property-read int $click_apply_count
 * @property-read Collection|\Modules\Subscription\Entities\Feature[] $features
 * @property-read int|null $features_count
 * @property-read Collection|User[] $followers
 * @property-read int|null $followers_count
 * @property-read array $address_locality_array
 * @property-read array $address_locality
 * @property-read string $address_locality_list
 * @property-read array $address_region_array
 * @property-read array $address_region
 * @property-read array $address_region_ids
 * @property-read string $address_region_list
 * @property-read string $address_short_region_list
 * @property-read mixed $addresses_id
 * @property array $array_salary
 * @property-read mixed $benefits_html
 * @property-read int $candidate_ready_count
 * @property-read array $collection_addresses
 * @property-read mixed $description_str
 * @property-read string $excerpt
 * @property-read mixed $experience_from_id getExperienceFromIdAttribute
 * @property-read mixed $experience_to_id getExperienceToIdAttribute
 * @property-read mixed $experiences_range_name
 * @property-read mixed $experiences_range_slug
 * @property-read mixed $feed_catalog_facebook
 * @property-read mixed $feed_facebook_job
 * @property-read mixed $feed_google_datafeed
 * @property-read \Modules\Job\Entities\url $follow_url
 * @property-read array $full_addresses
 * @property-read bool $is_hot
 * @property-read mixed $job_information_audits
 * @property-read array $job_information_benefits
 * @property-read array $job_information_languages
 * @property-read array $job_information_other_supports
 * @property-read array $job_information_salary
 * @property-read mixed $job_note_audits
 * @property-read mixed $job_note_employer_notes
 * @property-read mixed $job_note_notes_label
 * @property-read array $job_note_recruiment_process
 * @property-read array $job_translation_working_hours
 * @property-read mixed $meta_description
 * @property-read mixed $meta_keywords
 * @property-read mixed $meta_title
 * @property-read int $num_candidates
 * @property-read int $num_delivered_candidates
 * @property-read int $num_followers
 * @property-read int $num_ready_candidates
 * @property-read int $num_unqualified_candidates
 * @property-read int $num_viewers
 * @property array $packages
 * @property-read int $refreshed_count
 * @property-read string $requirements_str
 * @property-read string $responsibilities_str
 * @property-read array $schema_job_posting
 * @property-read float $scores
 * @property-read int $scoring_by_packages
 * @property array $services
 * @property-read string $short_addresses
 * @property-read mixed $social_column
 * @property-read array $social_post_content
 * @property-read string $status_display
 * @property-read string $summary_viewers
 * @property-read string $template_color_desc
 * @property-read string $notes_label
 * @property array $terms
 * @property string|null $education_certificate
 * @property string|null $company_tagline
 * @property Media|null $company_logo
 * @property-read array $recruitment_processes
 * @property-read array $benefits
 * @property-read string $template_slug
 * @property-read string $color_properties
 * @property-read string $job_blade_html
 * @property-read Collection $taxonomy_requirements
 * @property-read Collection $taxonomy_responsibilities
 * @property-read Collection $taxonomy_recruitment_processes
 * @property-read Collection $education_degree
 * @property-read Collection $taxonomy_benefits
 * @property-read Collection $job_banner
 * @property-read Collection $job_template
 * @property-read Collection $job_template_color
 * @property-read Collection $education_major
 * @property-read Collection|JobInvitationEmail[] $jobInvitationEmails
 * @property-read int|null $job_invitation_emails_count
 * @property-read JobMeta $job_meta
 * @property-read JobNote $job_note
 * @property-read Collection|JobRefresh[] $job_refreshes
 * @property-read int|null $job_refreshes_count
 * @property-read JobTranslation $job_translation
 * @property-read Candidate|null $latestCandidate
 * @property-read Collection|Media[] $media
 * @property-read int|null $media_count
 * @property-read Collection|\App\Entities\Note[] $notes
 * @property-read int|null $notes_count
 * @property-read Company|null $owner
 * @property-read Collection|Administrator[] $personChargeJob
 * @property-read int|null $person_charge_job_count
 * @property-read Collection|User[] $resumes
 * @property-read int|null $resumes_count
 * @property-read Collection|Job[] $similar_jobs
 * @property-read int|null $similar_jobs_count
 * @property-read Collection|\Modules\Subscription\Entities\Package[] $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read Collection|Taxonomy[] $taxonomies
 * @property-read int|null $taxonomies_count
 * @property-read Collection|User[] $viewers
 * @property-read int|null $viewers_count
 * @property-read Collection|\Modules\Activity\Entities\Activity[] $views
 * @property-read int|null $views_count
 * @property-read array $province_codes getProvinceCodesAttribute
 * @property-read array|null $months_of_experience getMonthsOfExperienceAttribute
 * @property-read array|null $min_months_of_experience getMinMonthsOfExperienceAttribute
 * @property-read array $skills_ids getSkillsIdsAttribute
 * @property-read Collection|JobCategory[] $job_categories
 * @property-read JobCategory $job_category
 * @property-read int $job_category_id
 * @property-read array $job_category_role_ids
 *
 * @method static Builder|Job doesntHaveSimilarJobs()
 * @method static Builder|Job hackerrank()
 * @method static Builder|Job hasSimilarJobs()
 * @method static Builder|Job needFollow()
 * @method static Builder|Job newModelQuery()
 * @method static Builder|Job newQuery()
 * @method static Builder|Job ofCompany(?\Modules\Company\Entities\Company $company = null)
 * @method static Builder|Job onGracePeriod()
 * @method static Builder|Job onlyClose()
 * @method static Builder|Job onlyCrawl()
 * @method static Builder|Job onlyDraft()
 * @method static Builder|Job onlyFree()
 * @method static Builder|Job onlyOpen()
 * @method static Builder|Job onlyPaid()
 * @method static Builder|Job onlyReview()
 * @method static \Illuminate\Database\Query\Builder|Job onlyTrashed()
 * @method static Builder|Job published()
 * @method static Builder|Job query()
 * @method static Builder|Job similarJobIn($jobIds)
 * @method static Builder|Job status($status)
 * @method static Builder|Job unpublished()
 * @method static Builder|Job whereBlogPosts($value)
 * @method static Builder|Job whereBlogTags($value)
 * @method static Builder|Job whereContent($value)
 * @method static Builder|Job whereCreatedAt($value)
 * @method static Builder|Job whereCreatorId($value)
 * @method static Builder|Job whereCrmInvoiceId($value)
 * @method static Builder|Job whereDeletedAt($value)
 * @method static Builder|Job whereEmailsCc($value)
 * @method static Builder|Job whereExpiresAt($value)
 * @method static Builder|Job whereHackerrank($value)
 * @method static Builder|Job whereHot($value)
 * @method static Builder|Job whereId($value)
 * @method static Builder|Job whereLevel($value)
 * @method static Builder|Job whereLeverId($value)
 * @method static Builder|Job whereOwnedId($value)
 * @method static Builder|Job whereParentId($value)
 * @method static Builder|Job wherePublishedAt($value)
 * @method static Builder|Job whereRefreshedAt($value)
 * @method static Builder|Job whereRequirements($value)
 * @method static Builder|Job whereResponsibilities($value)
 * @method static Builder|Job whereSidebarImageLink($value)
 * @method static Builder|Job whereSlug($value)
 * @method static Builder|Job whereStatus($value)
 * @method static Builder|Job whereTitle($value)
 * @method static Builder|Job whereUpdatedAt($value)
 * @method static Builder|Job whereUuid($value)
 * @method static Builder|Job withAllTerms($terms, ?string $taxonomy = null)
 * @method static Builder|Job withAnyTerms($terms, ?string $taxonomy = null)
 * @method static Builder|Job withTermsName($keyword)
 * @method static \Illuminate\Database\Query\Builder|Job withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Job withoutTrashed()
 * @mixin \Eloquent
 * @property-read mixed $experiences_id
 * @property-read mixed $skills_id
 * @property-read mixed $job_levels_id
 * @property-read mixed $job_types_id
 * @property-read mixed $contract_types_id
 * @method static Builder|Job whereOriginalPublishedAt($value)
 * @property-read mixed $jobBestMatch
 * @property-read string $job_information_benefit
 * @property-read array $location_best_match
 * @property-read \Modules\Job\Entities\number $location_type
 * @property-read Collection|JobLocationBestMatch[] $jobLocationBestMatch
 * @property-read int|null $job_location_best_match_count
 * @property-read JobCount $job_count
 * @property \Illuminate\Support\Carbon|null $opened_at
 * @property \Illuminate\Support\Carbon|null $closed_at
 */
class Job extends TopdevModel implements HasMedia, ViewableContract, Auditable, GoWithHackerRank
{
    use UseUuid;
    use HasStatus;
    use Searchable;
    use HasCandidate;
    use HasMorphManyEvents;
    use HackerRankGoWithTopdev;
    use AddressHelper;
    use GoogleIndexable;
    use SocialPostContentTrait;
    use Taxoable; // Term Relationship for Job
    use HasSlug;
    use HasFile;
    use SoftDeletes;
    use HasManyNotes;
    use CanBeFollowed;
    use InteractsWithViews;
    use \OwenIt\Auditing\Auditable;
    use HasCustomRelations;
    use HasSubscriptions;
    use HasSimilarJob;
    use \Illuminate\Database\Eloquent\Factories\HasFactory;

    public const FEATURE_ENABLE = 'on';
    public const FEATURE_DISABLE = 'off';

    public const STATUS_DRAFT = 0;
    public const STATUS_CLOSED = 1;
    public const STATUS_CLOSED_PLUS = 11;
    public const STATUS_REVIEW = 2;
    public const STATUS_OPEN = 3;
    public const STATUS_OPEN_PLUS = 31;

    // Job's pricing type
    public const PRICING_TYPE_FREE = 1;
    public const PRICING_TYPE_PAID = 2;

    // Job's level
    public const JOB_LEVEL_PAID = 'paid';
    public const JOB_LEVEL_FREE = 'free';

    // Job duration
    public const JOB_FREE_DURATION_IN_DAYS = 14;
    public const JOB_PAID_DURATION_IN_DAYS = 30;
    public const JOB_PAID_DURATION_15_DAYS_IN_DAYS = 15;

    public $editor = null;

    protected $table = 'jobs';

    protected $primaryKey = 'id';

    protected $fillable = [
        'uuid',
        'slug',
        'status',
        'title',
        'content',
        'content_html_desktop',
        'content_html_mobile',
        'requirements',
        'responsibilities',
        'owned_id',
        'parent_id',
        'creator_id',
        'expires_at',
        'emails_cc',
        'hackerrank',
        'lever_id',
        'hot',
        'refreshed_at',
        'published_at',
        'skills_ids',
        'best_skills',
        'extra_skills',
        'required_skills',
        'optional_skills',
        'job_types',
        'job_levels',
        'categories',
        'experiences',
        'blog_posts',
        'sidebar_image_link',
        'level',
        'original_published_at',
        'crm_invoice_id',
        'crm_request_design_id',
        'packages',
        'company_tagline',
        'role',
        'edu_certificate',
        'responsibilities_original',
        'requirements_original',
        'is_content_image',
    ];

    protected $casts = [
        'expires_at' => 'datetime:Y-m-d H:i:s',
        'published_at' => 'datetime:Y-m-d H:i:s',
        'original_published_at' => 'datetime:Y-m-d H:i:s',
        'refreshed_at' => 'datetime:Y-m-d H:i:s',
        'blog_posts' => 'array',
        'premium_upgraded_at' => 'datetime:Y-m-d H:i:s',
        'premium_enabled_at' => 'datetime:Y-m-d H:i:s',
    ];

    public $timestamps = true;

    protected $removeViewsOnDelete = true;

    public $addressesId = [];

    /**
     * Attributes to include in the Audit.
     *
     * @var array
     */
    protected $auditInclude = [
        'title',
        'content',
        'content_html_desktop',
        'content_html_mobile',
        'slug',
        'status',
        'owned_id',
        'parent_id',
        'creator_id',
        'expires_at',
        'deleted_at',
        'published_at',
        'responsibilities',
        'requirements',
        'job_information_benefits',
        'job_note_recruiment_process',
        'job_information_salary',
        'addresses',
        'crm_request_design_id',
        'packages',
        'education_certificate',
        'company_tagline',
        'role',
    ];

    /**
     * {@inheritdoc}
     */
    protected $cachePrefix = 'job-prefix';

    /**
     * {@inheritdoc}
     */
    protected $appends = ['status_display'];

    /**
     * The attributes that should be append to native types.
     * Ex: categories_id, categories_name
     *
     * @var array
     */
    public $scopeTerm = [
        'skills',
        'best_skills',
        'extra_skills',
        'required_skills',
        'optional_skills',
        'job_types',
        'job_levels',
        'categories',
        'experiences',
        'other_supports',
        'contract_types',

        'role',
        'responsibilities',
        'benefits',
        'education',
        'requirements',
        'recruitment_processes',
        'education_major',
        'job_banner',
        'job_template',
        'job_template_color',
    ];

    /**
     * The attributes that should be append to native types.
     *
     * @var array
     */
    public $files = [
        'image_galleries',
        'image_thumbnail',
        'image_banner',
        'image_content',
        'sidebar_image_banner',
        'job_posting_html_desktop',
        'job_posting_html_mobile',
    ];

    public const MONTHS_OF_EXPERIENCE = [
        1640 => "0",
        8667 => "3",
        1641 => "6",
        1642 => "12",
        1643 => "24",
        1644 => "36",
        1645 => "48",
        1646 => "60",
        1647 => "72",
        1648 => "84",
        1649 => "96",
        1650 => "108",
        1651 => "132",
        4851 => null,
        8288 => "120"
    ];

    /**
     * Boot model job
     */
    protected static function boot()
    {
        // Set these two event for set address before auditing
        static::saved(function (self $job) {
            if ($job->addressesId) {
                $job->addresses()->sync($job->addressesId);
            }

            if ($job->company->shouldBeSearchable()) {
                $job->company->searchable();
            }

            $job->syncJobToCrm();
        });

        // Then boot everything else
        parent::boot();

        static::creating(function (Job $job) {
            $job->initial();
        });

        static::saving(function (Job $job) {
            $job->addressesId = $job->attributes['addresses_id'] ?? [];

            $job->offsetUnset('addresses_id');
            $job->offsetUnset('location_type');
            $job->offsetUnset('location_best_match');

            // Nếu post không được publish
            if (!$job->isPublished()) {
                $job->ensureBeforeUnpublish();
            }

            // Nếu post hết hạn
            if (!$job->onGracePeriod() && !$job->isEternal()) {
                $job->ensureBeforeExpires();
            }

            // Save original published_at
            if ($job->isDirty('status') && $job->status == Job::STATUS_OPEN) {
                $job->original_published_at = now();
            }

            //Handle Notification Active New Job (Review -> Open)
            $statusOld = $job->original['status'] ?? 0;
            if ((int)$statusOld === self::STATUS_REVIEW && (int)$job->status === self::STATUS_OPEN) {
                Notification::send($job->company, new NotificationActivateNewPosting($job));
            }
        });
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('sidebar_image_banner')
            ->acceptsFile(function (File $file) {
                return in_array($file->mimeType, config('media-library.allow_image_mimes'));
            })
            ->singleFile();
    }

    /**
     * Construct model job
     *
     */
    public function __construct($attributes = [])
    {
        $this->fillable = array_merge($this->fillable, [
            'opened_at',
            'closed_at',
        ]);

        $this->casts = array_merge($this->casts, [

            'closed_at' => 'datetime:Y-m-d H:i:s',
        ]);

        parent::__construct($attributes);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): \Illuminate\Database\Eloquent\Factories\Factory
    {
        return \Modules\Job\Database\Factories\JobFactory::new();
    }

    /**
     * {@inheritdoc}
     */
    public function sluggable(): string
    {
        if (empty($this->company)) {
            return $this->title;
        }

        return $this->title . ' ' . $this->company->display_name;
    }

    public function generateUniqueSlug(): bool
    {
        return false;
    }

    public function addresses(): BelongsToMany
    {
        return $this->belongsToMany(Address::class, 'address_job', 'job_id', 'address_id')
            ->using(JobAddress::class);
    }


    /**
     * Người đang update job.
     *
     * @return $this
     */
    public function setEditor($editor)
    {
        $this->editor = $editor;
        return $this;
    }

    /**
     * @return CustomRelation
     */
    public function employer()
    {
        return $this->customRelation(
            User::class,
            // add constraints
            function ($relation) {
                $relation
                    ->getQuery()
                    ->join('companies', function ($join) {
                            $join->on('companies.user_id', '=', 'users.id');

                            if ($this->getKey()) {
                                $join->where('companies.id', $this->owned_id);
                            }
                        });
            },

            // add eager constraints
            function ($relation, $jobs) {
                $relation
                    ->getQuery()
                    ->whereIn('companies.id', collect($jobs)->pluck('owned_id')->all())
                    ->with('company');
            },
            function (array $jobs, Collection $results, $relation, $customRelation) {
                if ($results->isEmpty()) {
                    return $jobs;
                }

                foreach ($jobs as $job) {
                    $job->setRelation(
                        $relation,
                        $results->filter(function (User $user) use ($job) {
                                return ($user->company->id == $job->owned_id);
                            })->first()
                    );
                }

                return $jobs;
            }
        );
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'owned_id')
            ->disableCache()
            ->withoutGlobalScopes()
            ->withDefault();
    }

    /**
     * Set company
     *
     * @return void
     */
    public function companyOwnedBy($company = null)
    {
        if (!empty($company)) {
            $this->company()->associate($company);
        }
    }

    /**
     * Set init field default job
     *
     * @return void
     */
    public function initial()
    {
        $this->authoredBy(request()->user());
        $this->setPublishedAtAttribute(now());
        $this->setRefreshedAtAttribute(now());
    }

    /**
     * {@inheritdoc}
     */
    public function republish()
    {
        $this->setRefreshedAtAttribute(now())->save();

        $this->job_refreshes()->create([]);

        $this->job_count()->updateOrCreate(
            ['job_id' => $this->id],
            ['refresh_count' => $this->job_refreshes()->count()]
        );
    }

    /**
     * Check has republish today
     *
     * @return bool
     */
    public function shouldBeRepublish()
    {
        if (empty($this->refreshed_at)) {
            return false;
        }

        if ($this->subscribedTo('distinction')) {
            return $this->refreshed_at->addDays(5)->startOfDay()->isPast();
        }

        if ($this->subscribedTo('basic-plus')) {
            return $this->refreshed_at->addDays(10)->startOfDay()->isPast();
        }

        if ($this->subscribedTo('basic')) {
            return $this->refreshed_at->addDays(15)->startOfDay()->isPast();
        }

        if ($this->subscribedTo('top-job-1') ||
            $this->subscribedTo('top-job-web-1') ||
            $this->subscribedTo('distinction-web-1') ||
            $this->subscribedTo('basic-plus-web-1')) {
            if ($this->original_published_at && $this->original_published_at->diffInDays(now()) <= 7) {
                return $this->refreshed_at->addDay()->startOfDay()->isPast();
            }

            return $this->refreshed_at->addDays(5)->startOfDay()->isPast();
        }

        if ($this->subscribedTo('basic-15-days')) {
            return false;
        }

        if ($this->subscribedTo('basic-plus-15-days')) {
            return $this->original_published_at 
                && $this->original_published_at->diffInDays(now()) == 7
                && $this->job_refreshes()->count() == 0;
        }

        return false;
    }

    /**
     * Get emails cc follow job.
     *
     * @return array
     */
    public function getEmailsCc()
    {
        return array_values(
            array_filter(array_map('trim', array_unique(
                array_merge(
                    is_string($this->emails_cc) ? explode(',', $this->emails_cc) : ($this->emails_cc ?? []),
                    is_string($this->company->emails_cc) ? explode(',', $this->company->emails_cc) : $this->company->emails_cc
                )
            )
            ), function ($email) {
                return (filter_var($email, FILTER_VALIDATE_EMAIL))
                    ? true
                    : false;
            })
        );
    }

    /**
     * Get total followers.
     *
     * @return integer
     */
    public function getNumFollowersAttribute()
    {
        if (array_key_exists('followers_count', $this->attributes)) {
            return $this->attributes['followers_count'] + $this->num_followers_fake;
        }

        return $this->followers->count() + $this->num_followers_fake;
    }

    /**
     * Get link detail.
     * Using for detail_url attribute.
     *
     * @return string
     */
    public function getDetailUrlAttribute(): string
    {
        if ($this->isOpen() || $this->isClose() || $this->isReview()) {
            return frontend_url('detail-jobs/' . $this->slug . '-' . $this->getKey());
        }

        return frontend_url('/');
    }

    public function getPreviewUrlAttribute(): string
    {
        return frontend_url('preview-jobs/' . $this->slug . '-' . $this->getKey());
    }

    /**
     * Get location type.
     *
     * @return number
     */

    public function getLocationTypeAttribute()
    {
        /** @var \Illuminate\Support\Collection $collectionBestMatch */
        $collectionBestMatch = $this->jobLocationBestMatch->pluck('province_id');
        return $collectionBestMatch->count() ? 1 : 0;
    }

    /**
     * Get Province Best Match.
     *
     * @return array
     */
    public function getLocationBestMatchAttribute()
    {
        return $this->jobLocationBestMatch->pluck('province_id')->toArray();
    }


    /**
     * Check is hot.
     *
     * @return bool
     */
    public function getIsHotAttribute(): bool
    {
        return $this->hasFeature('super-hot-job');
    }

    /**
     * @return bool
     */
    public function shouldBeGoogleIndexing(): bool
    {
        return (!$this->trashed() && $this->isOpen());
    }

    /**
     * Determine if the model should be searchable.
     *
     * @return bool
     */
    public function shouldBeSearchable()
    {
        $company = $this->company;

        if (empty($company->getKey()) || $company->isInactive() || $company->trashed()) {
            return false;
        }

        return true;
    }

    /**
     * Get status for the model.
     *
     * @return array
     */
    public static function getStatusDescriptionOriginal(): array
    {
        return [
            static::STATUS_DRAFT => 'Draft',
            static::STATUS_CLOSED => 'Closed',
            static::STATUS_REVIEW => 'Review',
            static::STATUS_OPEN => 'Open',
        ];
    }

    /**
     * Get status for the model.
     *
     * @return array
     */
    public static function getJobPricingType(): array
    {
        return [
            static::PRICING_TYPE_FREE => 'Free',
            static::PRICING_TYPE_PAID => 'Paid',
        ];
    }

    /**
     * Get status for the model.
     *
     * @return array
     */
    public static function getStatusDescriptionDev2062FreePost(): array
    {
        return [
            static::STATUS_DRAFT => 'Draft',
            static::STATUS_CLOSED => 'Closed',
            static::STATUS_CLOSED_PLUS => 'Closed*',
            static::STATUS_REVIEW => 'Review',
            static::STATUS_OPEN => 'Open',
            static::STATUS_OPEN_PLUS => 'Open*',
        ];
    }

    /**
     * Get status for the model.
     *
     * @return array
     */
    public static function getStatusDescription(): array
    {
        return static::getStatusDescriptionDev2062FreePost();
    }

    protected function getStatusDisplayOriginal()
    {
        $list = static::getStatusDescription();
        return $list[$this->status];
    }

    protected function getStatusExtendMapping()
    {
        return [
            static::STATUS_CLOSED => static::STATUS_CLOSED_PLUS,
            static::STATUS_OPEN => static::STATUS_OPEN_PLUS,
        ];
    }

    protected function getStatusExtendDateMapping()
    {
        return [
            static::STATUS_CLOSED_PLUS => 'opened_at',
            static::STATUS_OPEN_PLUS => 'closed_at',
        ];
    }

    protected function getStatusDisplayDev2062FreePost()
    {
        $list = static::getStatusDescription();
        $status = $this->getNewStatus();

        return $list[$status];
    }

    /**
     * Get status for the model.
     *
     * @return string
     */
    public function getStatusDisplayAttribute()
    {
        return $this->getStatusDisplayDev2062FreePost();
    }

    /**
     * Get job string requirements.
     *
     * Accessor for requirements_str attribute. (->requirements_str)
     */
    public function getRequirementsStrAttribute()
    {
        if (!is_null($this->requirements) && is_string($this->requirements)) {
            return preg_replace("/[\n\r]/", '', html_entity_decode(strip_tags($this->requirements))) ?? null;
        }

        return collect($this->requirements)->pluck('name')->implode(', ');
    }

    /**
     * Get job string responsibilities.
     *
     * @return string
     */
    public function getResponsibilitiesStrAttribute()
    {
        return collect($this->responsibilities)->pluck('name')->implode(', ');
        // return !is_null($this->responsibilities) ? preg_replace("/[\n\r]/", '', html_entity_decode(strip_tags($this->responsibilities))) : null;
    }

    /**
     * Điểm của job.
     *
     * @return float
     */
    public function getScoresAttribute(): float
    {
        return (float) ($this->scoring_by_packages);
    }

    /**
     * Tính điểm theo gói.
     * Just demo.
     *
     * @return integer
     */
    public function getScoringByPackagesAttribute()
    {
        if ($this->subscribedTo('distinction') ||
            $this->subscribedTo('top-job-1') ||
            $this->subscribedTo('top-job-web-1') ||
            $this->subscribedTo('distinction-web-1')) {
            return 4;
        }

        if ($this->subscribedTo('basic-plus') ||
            $this->subscribedTo('basic-plus-web-1')) {
            return 3;
        }

        if ($this->subscribedTo('basic')) {
            return 2;
        }

        return 1;
    }

    public function clickApply()
    {
        return $this->morphToMany(\CyrildeWit\EloquentViewable\View::class, 'viewable')
            ->whereIn('collection', ['apply-now', 'apply-without-cv']);
    }

    /**
     * Get the name of the "parent id" column.
     *
     * @return string
     */
    public function getParentColumn(): string
    {
        return 'parent_id';
    }

    /**
     * Get the name of the "status" column.
     *
     * @return string
     */
    public function getStatusColumn(): string
    {
        return 'status';
    }

    /**
     * Get the name of the "opened_at" column.
     *
     * @return string
     */
    public function getOpenedAtColumn(): string
    {
        return 'opened_at';
    }

    /**
     * Get the name of the "closed_at" column.
     *
     * @return string
     */
    public function getClosedAtColumn(): string
    {
        return 'closed_at';
    }

    /**
     * Get the value of the "status open".
     *
     * @return mixed
     */
    public function getStatusOpen()
    {
        return defined('static::STATUS_OPEN') ? static::STATUS_OPEN : 3;
    }

    /**
     * Get the value of the "status close".
     *
     * @return mixed
     */
    public function getStatusClose()
    {
        return defined('static::STATUS_CLOSED') ? static::STATUS_CLOSED : 1;
    }

    /**
     * Get the value of the "status review".
     *
     * @return mixed
     */
    public function getStatusReview()
    {
        return defined('static::STATUS_REVIEW') ? static::STATUS_REVIEW : 1;
    }

    /**
     * Get the value of the "status draft".
     *
     * @return mixed
     */
    public function getStatusDraft()
    {
        return defined('static::STATUS_DRAFT') ? static::STATUS_DRAFT : 0;
    }

    /**
     * Get the follow url for the job post.
     *
     * @return \Illuminate\Contracts\Routing\UrlGenerator|string
     */
    public function getFollowUrlAttribute()
    {
        return api_url('users/me/followed-jobs/' . $this->uuid);
    }

    /**
     * Get total view for the job post.
     *
     * @return integer
     */
    public function getNumViewersAttribute()
    {
        return $this->job_count->num_viewers ?? 0;
    }

    /**
     * Get total candidate_ready_count for the job post.
     *
     * @return integer
     */
    public function getCandidateReadyCountAttribute()
    {
        return $this->job_count->candidate_ready_count ?? 0;
    }

    /**
     * Get total candidates_count for the job post.
     *
     * @return integer
     */
    public function getCandidatesCountAttribute()
    {
        return $this->job_count->candidates_count ?? 0;
    }

    /**
     * Get total click_apply_count for the job post.
     *
     * @return integer
     */
    public function getClickApplyCountAttribute()
    {
        return $this->job_count->click_apply_count ?? 0;
    }

    /**
     * Get summary view for the job post.
     *
     * @return string
     */
    public function getSummaryViewersAttribute()
    {
        if (empty($this->published_at)) {
            return;
        }

        $numViewers = $this->num_viewers;

        if (empty($this->expires_at) || (!empty($this->expires_at) && $this->expires_at->isFuture())) {
            $diffInDays = now()->diffInDays($this->published_at);
        } else {
            $diffInDays = $this->expires_at->diffInDays($this->published_at);
        }

        $diffInDays = !$diffInDays ? 1 : $diffInDays;
        return $numViewers . '/' . $diffInDays . '/' . intval($numViewers / $diffInDays);
    }

    /**
     * {@inheritdoc}
     */
    public function ensureBeforeExpires(): self
    {
        $this->fill([
            $this->getStatusColumn() => $this->status ?? $this->getStatusClose()
        ]);

        return $this;
    }

    /**
     * {@inheritdoc}
     */
    public function ensureBeforeUnpublish(): self
    {
        $this->fill([
            $this->getStatusColumn() => $this->status ?? $this->getStatusReview()
        ]);

        return $this;
    }

    /**
     * Update only specific field value in elasticsearch
     */
    public function scriptUpdate($params = [])
    {
        dispatch_sync(new ScriptedDocumentUpdate($this, $params));
    }

    /**
     * Scope query jobs of company.
     *
     * @return Builder
     */
    public function scopeOfCompany(Builder $query, Company $company = null)
    {
        return $company ? $query->where('owned_id', $company->getKey()) : $query;
    }

    /**
     * Scope query only job crawl.
     */
    public function scopeOnlyCrawl(Builder $builder)
    {
        // return $builder->whereMeta('level', $this->crawlLevel())
        //     ->whereHasMeta('link_crawl');
    }

    /**
     * Scope query only job paid.
     */
    public function scopeOnlyPaid(Builder $builder): Builder
    {
        return $builder->where('level', $this->paidLevel());
    }

    /**
     * Scope query only job paid.
     */
    public function scopeOnlyFree(Builder $builder): Builder
    {
        return $builder->where('level', $this->freeLevel());
    }

    /**
     * Scope query only job paid.
     */
    public function scopeNeedFollow(Builder $builder): Builder
    {
        return $builder->whereIn('level', [$this->freeLevel(), $this->paidLevel()]);
    }

    /**
     * Scope query only job paid.
     */
    public function paidLevel(): string
    {
        return 'paid';
    }

    /**
     * Scope query only job paid.
     */
    public function freeLevel(): string
    {
        return 'free';
    }

    /**
     * Scope query only job paid.
     */
    public function crawlLevel(): string
    {
        return 'crawl';
    }

    /**
     * Handle when someone viewed the job
     */
    public function recentlyViewedBy($visitor)
    {
        $this->scriptUpdate([
            'num_viewers' => $this->num_viewers
        ]);
    }

    /**
     * Return viewers.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function viewers()
    {
        $table = config('eloquent-viewable.models.view.table_name');
        $class = \get_class($this);
        $userTable = 'users';
        $foreignKey = 'user_id';
        $tablePrefixedForeignKey = app('db.connection')->getQueryGrammar()->wrap(\sprintf('pivot_viewable.%s', $foreignKey));
        $eachOtherKey = app('db.connection')->getQueryGrammar()->wrap('pivot_each_other');

        return $this->morphToMany(User::class, 'viewable', $table)
            ->wherePivot('collection', '=', 'view')
            ->withPivot('viewable_type', 'collection')
            ->addSelect("{$userTable}.*", DB::raw("(CASE WHEN {$tablePrefixedForeignKey} IS NOT NULL THEN 1 ELSE 0 END) as {$eachOtherKey}"))
            ->leftJoin("{$table} as pivot_viewable", function ($join) use ($table, $class, $foreignKey) {
                $join->on('pivot_viewable.viewable_type', '=', DB::raw(\addcslashes("'{$class}'", '\\')))
                    ->on('pivot_viewable.viewable_id', '=', "{$table}.{$foreignKey}")
                    ->on("pivot_viewable.{$foreignKey}", '=', "{$table}.viewable_id")
                    ->where('pivot_viewable.collection', '=', 'view');
            });
    }

    /**
     * @inheritdoc
     */
    public function traceActivityBy()
    {
        return $this->title;
    }

    /**
     * Get the exportable data array for the model.
     *
     * @return array
     */
    public function toGooglesheetArray()
    {
        return array_values([
            'id' => $this->getKey(),
            'title' => $this->title,
            'company_name' => $this->company->display_name,
            'addresses' => $this->address_region_list,
            'num_viewers' => $this->num_viewers,
            'num_candidates' => $this->num_candidates,
            'num_delivered_candidates' => $this->num_delivered_candidates,
            'status_display' => $this->status_display,
            'published_at' => empty($this->published_at) ? null : $this->published_at->format('Y-m-d'),
            'latest_delivered_at' => empty($this->latestCandidate) ? null : $this->latestCandidate->delivered_at->format('Y-m-d'),
            'expires_at' => empty($this->expires_at) ? null : $this->expires_at->format('Y-m-d'),
            'detail_url' => $this->detail_url
        ]);
    }

    /**
     * Get the value used to index the model.
     *
     * @return mixed
     */
    public function getScoutKey()
    {
        return $this->id;
    }

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'jobs_ams_v3';
    }

    //Refactor import es
    public function importSearchableAs()
    {
        return 'jobs_ams_v3';
    }

    public function importSearchableUsing()
    {
        return new JobImportSource();
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        Log::info('Build elasticsearch job: ' . $this->getKey());

        $this->loadMissing([]);
        $features = $this->features()->active()->get();

        return [
            /*
            |--------------------------------------------------------------------------
            | Basic
            |--------------------------------------------------------------------------
            */
            'id' => $this->id,
            'uuid' => $this->uuid,
            'level' => $this->level,
            'title' => $this->title,
            'content' => $this->content,
            'content_html_desktop' => $this->content_html_desktop,
            'content_html_mobile' => $this->content_html_mobile,
            'recruiment_process' => $this->recruitment_processes,
            'slug' => $this->slug,
            'salary' => $this->job_information->salary->toArray(),
            'requirements' => $this->requirements,
            'requirements_str' => $this->requirements_str,
            'responsibilities' => $this->responsibilities,
            'responsibilities_str' => $this->responsibilities_str,
            'status' => (string) $this->status,
            'status_display' => $this->status_display,
            'benefits' => $this->benefits ?? [],
            'scores' => $this->scores,
            'working_hours' => $this->job_translation->working_hours,
            'owned_id' => $this->owned_id,

            /*
            |--------------------------------------------------------------------------
            | Company
            |--------------------------------------------------------------------------
            */
            'company' => [
                'id' => $this->company->id,
                'slug' => $this->company->slug,
                'image_logo' => !current($this->company->image_logo_url) ? null : current($this->company->image_logo_url),
                'display_name' => $this->company->display_name,
                'faqs' => $this->company->faqs,
                'num_employees_id' => $this->company->getTaxonomies('num_employees')->pluck('id')->first(), //add new
                'industries_ids' => $this->company->industries_id->all(),
            ],

            /*
            |--------------------------------------------------------------------------
            | Media
            |--------------------------------------------------------------------------
            */
            'image_banner' => $this->image_banner->toArray(),
            'image_thumbnail' => !current($this->image_thumbnail_url) ? null : current($this->image_thumbnail_url),
            'image_galleries' => $this->image_galleries->toArray(),
            'image_content' => !current($this->image_content) ? null : current($this->image_content),

            /*
            |--------------------------------------------------------------------------
            | Urls
            |--------------------------------------------------------------------------
            */
            'hot' => $this->hot,
            'is_hot' => $this->is_hot,

            /*
            |--------------------------------------------------------------------------
            | Taxonomies
            |--------------------------------------------------------------------------
            */
            'extra_skills' => $this->extra_skills_name->all(),
            'skills_arr' => $this->skills_name->all(),
            'skills_str' => $this->skills_name->implode(', '),
            'skills_ids' => $this->skills_id->all(),
            'skills' => $this->skills->map(function ($skill) {
                return [
                    'id' => $skill->id,
                    'name_vi' => $skill->name_vi,
                    'name_en' => $skill->name_en,
                ];
            })->toArray(),

            'job_levels_arr' => $this->job_levels_name->all(),
            'job_levels_str' => $this->job_levels_name->implode(', '),
            'job_levels_ids' => $this->job_levels_id->all(),
            'experiences_arr' => $this->experiences_name->all(),
            'experiences_str' => $this->experiences_name->implode(', '),
            'experiences_ids' => $this->experiences_id->all(),
            'job_types_arr' => $this->job_types_name->all(),
            'job_types_str' => $this->job_types_name->implode(', '),
            'job_types_ids' => $this->job_types_id->all(),
            'categories_str' => $this->categories_name->implode(', '),
            'categories_arr' => $this->categories_name->all(),
            'categories_ids' => $this->categories_id->all(),
            'contract_types_ids' => $this->contract_types_id->all(),
            'contract_types_arr' => $this->contract_types_name->all(),
            'contract_types_str' => $this->contract_types_name->implode(', '),
            'features' => $features->pluck('feature')->values()->all(),
            'packages' => $this->subscriptions->pluck('slug')->all(),
            'other_supports' => $this->job_information->other_supports,
            'required_skills_arr' => $this->required_skills_name->all(),
            'optional_skills_arr' => $this->optional_skills_name->all(),

            'education_arr' => $this->education_name->all(),
            'education_str' => $this->education_name->implode(', '),
            'education_ids' => $this->education_id->all(),

            'education_major_arr' => $this->education_major_name->all(),
            'education_major_str' => $this->education_major_name->implode(', '),
            'education_major_ids' => $this->education_major_id->all(),

            /*
            |--------------------------------------------------------------------------
            | Social Post Content to Facebook, Google
            |--------------------------------------------------------------------------
            */
            //'social_post_content' => $this->social_post_content,

            /*
            |--------------------------------------------------------------------------
            | Aggregations
            |--------------------------------------------------------------------------
            */
            'num_candidates' => (int) $this->num_ready_candidates + (int) $this->num_unqualified_candidates,
            'num_ready_candidates' => (int) $this->num_ready_candidates,
            'num_unqualified_candidates' => (int) $this->num_unqualified_candidates,

            'num_viewers' => $this->num_viewers,
            'num_followers' => $this->num_followers,

            /*
            |--------------------------------------------------------------------------
            | Addresses
            |--------------------------------------------------------------------------
            */
            'addresses' => [
                'address_region_ids' => $this->address_region_ids,
                'address_region_list' => $this->address_region_list,
                'address_region_array' => $this->address_region_array,
                'full_addresses' => $this->full_addresses,
                'sort_addresses' => $this->short_addresses,
                'collection_addresses' => $this->collection_addresses
            ],

            /*
            |--------------------------------------------------------------------------
            | Packages
            |--------------------------------------------------------------------------
            */
            'is_free' => $this->subscribedTo('free'),
            'is_basic' => $this->subscribedTo('basic') || $this->subscribedTo('basic-15-days'),
            'is_basic_plus' => $this->subscribedTo('basic-plus') || $this->subscribedTo('basic-plus-web-1') || $this->subscribedTo('basic-plus-15-days'),
            'is_distinction' => $this->subscribedTo('distinction') ||
                $this->subscribedTo('top-job-1') ||
                $this->subscribedTo('top-job-web-1') ||
                $this->subscribedTo('distinction-web-1'),
            'is_top_job_web' => $this->subscribedTo('top-job-web-1'),
            'is_distinction_web' => $this->subscribedTo('distinction-web-1'),
            'is_basic_plus_web' => $this->subscribedTo('basic-plus-web-1'),

            /*
            |--------------------------------------------------------------------------
            | URLs
            |--------------------------------------------------------------------------
            */
            'detail_url' => $this->detail_url,

            /*
            |--------------------------------------------------------------------------
            | Meta for SEO
            |--------------------------------------------------------------------------
            */
            'meta_title' => $this->job_meta->meta_title,
            'meta_keywords' => $this->job_meta->meta_keywords,
            'meta_description' => $this->job_meta->meta_description,

            /*
            |--------------------------------------------------------------------------
            | Blog Post
            |--------------------------------------------------------------------------
            */
            'blog_tags' => $this->blog_tags,
            'blog_posts' => $this->blog_posts,
            'sidebar_image_banner_url' => $this->sidebar_image_banner_url,
            'sidebar_image_link' => $this->sidebar_image_link,

            /*
            |--------------------------------------------------------------------------
            | Timestamps
            |--------------------------------------------------------------------------
            */
            'expires_at' => empty($this->expires_at) ? null : $this->expires_at->format('Y-m-d H:i:s'),
            'published_at' => empty($this->published_at) ? null : $this->published_at->format('Y-m-d H:i:s'),
            'refreshed_at' => empty($this->refreshed_at) ? null : $this->refreshed_at->format('Y-m-d H:i:s'),
            'created_at' => empty($this->created_at) ? null : $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => empty($this->updated_at) ? null : $this->updated_at->format('Y-m-d H:i:s'),
            'schema_job_posting' => $this->schema_job_posting,

            /**
             * CRM
             */
            'crm_invoice_id' => $this->crm_invoice_id,
            'crm_request_design_id' => $this->crm_request_design_id,
            'candidates_count' => $this->candidates_count,
            'service_ids' => $features->pluck('id')->values()->all(),
            'service_list' => $features->count() ? $features->map(fn($service) => [
                'id' => $service->id,
                'name' => $service->term->name,
                'feature' => $service->term->feature,
                'expires_at' => $service->pivot->expires_at ? $service->pivot->expires_at->format('Y-m-d H:i:s') : null,
                'created_at' => $service->pivot->created_at ? $service->pivot->created_at->format('Y-m-d H:i:s') : null,
            ]) : [],
            'package_ids' => $this->subscriptions->pluck('id')->values()->all(),
            'package_list' => $this->subscriptions->count() ? $this->subscriptions->map(fn($package) => [
                'id' => $package->id,
                'name' => $package->term->name,
                'feature' => $package->term->feature,
                'expires_at' => $package->pivot->expires_at ? $package->pivot->expires_at->format('Y-m-d H:i:s') : null,
                'created_at' => $package->pivot->created_at ? $package->pivot->created_at->format('Y-m-d H:i:s') : null,
            ]) : [],
            'created_by' => $this->audits->first()->user->email ?? 'Administrator',
            'summary_viewers' => $this->summary_viewers,
            'notes_label' => $this->job_note->notes_label,

            /**
             * Images post
             */
            'is_content_image' => $this->is_content_image,
            'is_content_image_enabled' => ! is_null($this->premium_enabled_at),

            'responsibilities_original' => $this->responsibilities_original,
            'requirements_original' => $this->requirements_original,
            'benefits_original' => $this->job_information_benefits ?? [],
            'recruitment_process_original' => $this->job_note->recruiment_process,
            'education_certificate' => $this->education_certificate,

            /**
             * Multiple job category id
             */
            'job_categories_ids' => $this->job_categories->pluck('id'),
            'benefit_ids' => $this->taxonomy_benefits->pluck('id')
        ];
    }

    protected function getDatePosted()
    {
        if ($this->refreshed_at) {
            return $this->refreshed_at->format('Y-m-d');
        }

        if ($this->published_at) {
            return $this->published_at->format('Y-m-d');
        }

        return null;
    }

    /**
     * Get schema job posting for the job.
     *
     * @return false|string
     */
    public function getSchemaJobPostingAttribute()
    {
        $salary = $this->job_information->salary->toArray();
        $isNegotiable = $this->job_information->salary->isNegotiable();

        return json_encode(array_filter([
            '@context' => 'http://schema.org',
            '@type' => 'JobPosting',
            'industry' => 'Information Technology',
            'title' => $this->title,
            'datePosted' => $this->getDatePosted(),
            'validThrough' => empty($this->expires_at) ? null : $this->expires_at->format('Y-m-d'),
            'skills' => $this->skills_name->implode(', '),
            'baseSalary' => array_filter([
                '@type' => 'MonetaryAmount',
                // 'directApply' => $isNegotiable ? true : null,
                'currency' => $salary['currency'] ?? 'VND',
                'value' => array_filter([
                    '@type' => 'QuantitativeValue',
                    'unitText' => $salary['unit'],
                    'minValue' => $isNegotiable ? null : $salary['min'],
                    'maxValue' => $isNegotiable ? null : $salary['max'],
                    'value' => $salary['value']
                ])
            ]),
            'description' => (
                $this->content
                . '<br>Your role & responsibilities<br>'
                . $this->responsibilities_str
                . '<br>Your skills & qualifications<br>'
                . $this->requirements_str
                . '<br>Benefits for you<br>'
                . $this->benefits_html
            ),
            "identifier" => [
                "@type" => "PropertyValue",
                "name" => $this->company->display_name,
                "value" => $this->company->getKey(),
            ],
            'potentialAction' => [
                '@type' => 'ApplyAction',
                'target' => $this->detail_url
            ],
            'hiringOrganization' => [
                '@type' => 'Organization',
                'name' => $this->company->display_name,
                'sameAs' => $this->company->detail_url,
                'logo' => $this->company->getFirstMediaUrl('image_logo'),
                'description' => $this->company->tagline,
                //'value' => $this->company->getKey(),
            ],
            'directApply' => 'TRUE',
            'employmentType' => $this->getEmploymentType(),
            'jobBenefits' => $this->benefits_html,
            'jobLocationType' => $this->addresses->isEmpty() ? 'TELECOMMUTE' : null,
            'jobLocation' => $this->addresses->isNotEmpty() ? [
                '@type' => 'Place',
                'address' => $this->addresses->map(function ($address) {
                    $provinceName = str_replace('Thành phố ', '', $address->province_name);
                    return [
                        '@type' => 'PostalAddress',
                        'addressRegion' => $provinceName,
                        'postalCode' => $address->postal_code,
                        'addressCountry' => 'VN',
                        // <-- problem
                        'addressLocality' => $address->district_name ?? $address->province_name,
                        'streetAddress' => $address->full_address,
                    ];
                })->first(),
            ] : null,
            'applicantLocationRequirements' => [
                '@type' => 'Country',
                'name' => 'Vietnam'
            ],
            'experienceRequirements' => $this->months_of_experience ? [
                '@type' => 'OccupationalExperienceRequirements',
                'monthsOfExperience' => $this->months_of_experience
            ] : null
        ]));
    }

    /**
     * province_codes attribute
     *
     * @return array
     */
    public function getProvinceCodesAttribute(): array
    {
        return $this->addresses->pluck('province_code')->toArray();
    }

    public function getExperiences() {
        return $this->experiences;
    }

    /**
     * for the months_of_experience attribute
     *
     * @return mixed|null
     */
    public function getMonthsOfExperienceAttribute()
    {
        $max = self::MONTHS_OF_EXPERIENCE[$this->getExperiences()->max('id')] ?? null;
        $min = self::MONTHS_OF_EXPERIENCE[$this->getExperiences()->min('id')] ?? null;

        if (!$max) {
            return null;
        } elseif (!$min) {
            return $max;
        }

        $max = (float) str_replace(',', '.', $max);
        $min = (float) str_replace(',', '.', $min);

        if ($max < $min) {
            return $min;
        }
        return $max;
    }

    /**
     * for the min_months_of_experience attribute
     *
     * @return mixed|null
     */
    public function getMinMonthsOfExperienceAttribute()
    {
        $max = self::MONTHS_OF_EXPERIENCE[$this->getExperiences()->max('id')] ?? null;
        $min = self::MONTHS_OF_EXPERIENCE[$this->getExperiences()->min('id')] ?? null;

        if (!$min) {
            return null;
        } elseif (!$max) {
            return $min;
        }

        $max = (float) str_replace(',', '.', $max);
        $min = (float) str_replace(',', '.', $min);

        if ($max < $min) {
            return $max;
        }
        return $min;
    }

    /**
     * Get employment type from job types id array
     *
     * @return Collection
     */
    public function getEmploymentType()
    {
        return $this
            ->job_types_id
            ->map(function ($item) {
                switch ($item) {
                    case config('job.type.FULL_TIME'):
                        return 'FULL_TIME';
                    case config('job.type.PART_TIME'):
                        return 'PART_TIME';
                    case config('job.type.CONTRACTOR'):
                    case config('job.type.FREELANCE'):
                        return 'CONTRACTOR';
                    default:
                        return 'OTHER';
                }
            })
            ->unique();
    }

    public function getBenefitsHtmlAttribute()
    {
        $html = collect($this->benefits)
            ->merge($this->company->benefits)->pluck('value')->map(function ($benefit) {
                return '<li>' . $benefit . '</li>';
            })->implode('');

        if (empty($html)) {
            return null;
        }

        return '<ul>' . $html . '</ul>';
    }

    public function announcement()
    {
        return $this->morphMany(Announcement::class, 'model');
    }

    public function getAddressesIdAttribute($value)
    {
        return $this->attributes['addresses_id'] = $this->addresses->pluck('id')->toArray();
    }

    public function jobInvitationEmails()
    {
        return $this->belongsToMany(JobInvitationEmail::class, JobInvitationUser::class, 'job_id', 'job_invitation_id');
    }

    /*
    |--------------------------------------------------------------------------
    | Custom audits
    |--------------------------------------------------------------------------
    */

    protected $oldValuesAudit = [];

    public function setOldValuesAudit(array $values)
    {
        $this->oldValuesAudit = $values;
        return $this;
    }

    public function getOldValuesAudit()
    {
        return $this->oldValuesAudit;
    }

    public function transformAudit(array $values): array
    {
        return array_merge($values, array_filter([
            'old_values' => $this->oldValuesAudit,
            'new_values' => AuditTransformer::make($this)
        ]));
    }

    // Move method from Post

    public function media(): \Illuminate\Database\Eloquent\Relations\MorphMany
    {
        return $this->morphMany(config('media-library.media_model'), 'model')->disableCache();
    }

    public function owner()
    {
        return $this->belongsTo(Company::class, 'owned_id')->withDefault([
            'id' => null,
            'profile' => null,
            'display_name' => 'N/A',
        ]);
    }

    public function author()
    {
        return $this->belongsTo(Administrator::class, 'creator_id')->withDefault([
            'id' => '1',
            'name' => 'Administrator',
            'profile' => url('/admin/auth/users/1'),
        ]);
    }

    public function authoredBy($author = null)
    {
        if (!empty($author)) {
            $this->author()->associate($author);
        }
    }

    public function getExcerptAttribute(): string
    {
        return Str::limit(strip_tags($this->content), 100);
    }

    public function getDescriptionStrAttribute()
    {
        return preg_replace("/[\n\r]/", "", html_entity_decode(strip_tags($this->content)));
    }

    public function setPublishedAtAttribute($value)
    {
        $this->attributes['published_at'] = $value;
        return $this;
    }

    public function setExpiresAtAttribute($value)
    {
        $this->attributes['expires_at'] = $value;
        return $this;
    }

    public function setRefreshedAtAttribute($value)
    {
        $this->attributes['refreshed_at'] = $value;
        return $this;
    }

    public function isRelease()
    {
        return ($this->isPublished() && ($this->onGracePeriod() || $this->isEternal()));
    }

    public function isPublished()
    {
        if (is_null($this->published_at)) {
            return false;
        }
        return $this->published_at->lte(Carbon::now());
    }

    public function expiresAtLteNumberOfDays(int $numberDays = 3)
    {
        return $this->expires_at != null && $this->expires_at->lte(now()) && $this->expires_at->diffInDays(now()) <= $numberDays;
    }

    public function scopePublished(Builder $query)
    {
        return $query->where('published_at', '<=', Carbon::now())->whereNotNull('published_at');
    }

    public function scopeUnpublished(Builder $query)
    {
        return $query->where('published_at', '>', Carbon::now())->orWhereNull('published_at');
    }

    public function onGracePeriod()
    {
        return $this->expires_at && $this->expires_at->isFuture();
    }

    public function scopeOnGracePeriod($query)
    {
        $query->whereNotNull('expires_at')->where('expires_at', '>', Carbon::now());
    }

    public function isEternal(): bool
    {
        return is_null($this->expires_at);
    }

    public function lifetime($period)
    {
        $period = is_string($period) ? Carbon::now()->add($period) : null;
        $this->setExpiresAtAttribute($period);
        return $this;
    }

    // New relationship model job
    public function job_translation(): HasOne
    {
        return $this->hasOne(JobTranslation::class, 'job_id')->withDefault();
    }

    public function job_information(): HasOne
    {
        return $this->hasOne(JobInformation::class, 'job_id')->withDefault();
    }

    public function job_meta(): HasOne
    {
        return $this->hasOne(JobMeta::class, 'job_id')->withDefault();
    }

    public function job_note(): HasOne
    {
        return $this->hasOne(JobNote::class, 'job_id')->withDefault();
    }

    public function job_refreshes()
    {
        return $this->hasMany(JobRefresh::class, 'job_id');
    }

    public function jobLocationBestMatch()
    {
        return $this->hasMany(JobLocationBestMatch::class, 'job_id');
    }

    /**
     * Get salary attribute from job information
     *
     * @return array
     */
    public function getArraySalaryAttribute()
    {
        return $this->job_information->salary->toArray();
    }

    /**
     * Get salary attribute from job information
     *
     * @return array
     */
    public function setArraySalaryAttribute($value)
    {
        if (!$this->job_information) {
            return;
        }
        $this->job_information->salary = $value;
    }

    /**
     * Get social post content from job information
     * Always return array
     *
     * @return array
     */
    public function getSocialPostContentAttribute()
    {
        return $this->job_information->social_post_content ?? [];
    }

    public function getMetaTitleAttribute()
    {
        return $this->job_meta->meta_title ?? '';
    }

    public function getMetaKeywordsAttribute()
    {
        return $this->job_meta->meta_keywords ?? '';
    }

    public function getMetaDescriptionAttribute()
    {
        return $this->job_meta->meta_description ?? '';
    }

    /**
     * Job count relationship
     *
     * @return HasOne
     */
    public function job_count(): HasOne
    {
        return $this->hasOne(JobCount::class)->withDefault([
            'num_viewers' => 0,
            'candidate_ready_count' => 0,
            'candidates_count' => 0,
            'click_apply_count' => 0,
            'refresh_count' => 0,
            'candidates_not_matching_count' => 0
        ]);
    }

    /**
     * Refresh job_count values
     */
    public function refreshCount()
    {
        $jobCount = $this->job_count;
        if (!$jobCount->exists()) {
            $jobCount = new JobCount();
            $jobCount->job_id = $this->id;
        }

        $jobCount->fill([
            'num_viewers' => $this->views()->count(),
            'click_apply_count' => $this->clickApply()->count(),
            'candidate_ready_count' => $this->candidateReady()->count(),
            'candidates_count' => $this->candidates()->count(),
            'refresh_count' => $this->job_refreshes()->count(),
            'candidates_not_matching_count' => $this->candidateUnqualified()->count()
        ]);

        $jobCount->save();

        return $this;
    }

    /**
     * Get benefits attribute from job_information
     *
     * @return array
     */
    public function getJobInformationBenefitsAttribute()
    {
        return $this->job_information->benefits;
    }

    /**
     * Get benefit attribute from job_information
     * Using by $job_information_benefit
     *
     * @return string
     */
    public function getJobInformationBenefitAttribute()
    {
        return $this->job_information->benefit;
    }

    /**
     * Get languages attribute from job_information
     *
     *
     * @return array
     */
    public function getJobInformationLanguagesAttribute()
    {
        return $this->job_information->languages;
    }

    /**
     * Get recruiment_process attribute from job_note
     *
     * @return array
     */
    public function getJobNoteRecruimentProcessAttribute()
    {
        return $this->job_note->recruiment_process;
    }

    public function getJobNoteEmployerNotesAttribute()
    {
        return $this->job_note->employer_notes;
    }

    /**
     * Get working_hours attribute from job_translation
     *
     * @return array
     */
    public function getJobTranslationWorkingHoursAttribute()
    {
        return $this->job_translation->working_hours;
    }

    /**
     * Get salary attribute from job_information
     *
     * @return array
     */
    public function getJobInformationSalaryAttribute()
    {
        return $this->job_information->salary->toArray() ?? [];
    }

    /**
     * Get refresh count attributes from job_count
     *
     * @return integer
     */
    public function getRefreshedCountAttribute()
    {
        return $this->job_count->refresh_count;
    }

    /**
     * Get job information other support
     *
     * @return array
     */
    public function getJobInformationOtherSupportsAttribute()
    {
        return $this->job_information->other_supports;
    }

    public function personChargeJob(): BelongsToMany
    {
        return $this->belongsToMany(Administrator::class, PersonChargeJob::class, 'job_id', 'user_id');
    }

    public function getJobInformationAuditsAttribute()
    {
        return $this->job_information->audits;
    }

    public function getJobNoteAuditsAttribute()
    {
        /** @phpstan-ignore-next-line */
        return $this->job_note->audits;
    }

    public function getJobNoteNotesLabelAttribute()
    {
        return $this->job_note->notes_label;
    }

    public function getBlogPostsAttribute($value): array
    {
        if (is_array($value)) {
            return $value;
        }

        if (is_null($value)) {
            return [];
        }

        return array_values(json_decode($value, true) ?: []);
    }

    public function setBlogPostsAttribute($value)
    {
        $this->attributes['blog_posts'] = json_encode(array_values($value ?? []));
    }

    public function getExperiencesRangeNameAttribute()
    {
        return $this->getJobExperiencesRange()->pluck('term.name');
    }

    public function getExperiencesRangeSlugAttribute()
    {
        return $this->getJobExperiencesRange()->pluck('term.slug');
    }

    /**
     * @return Builder[]|Collection|\Illuminate\Support\Collection|Taxonomy[]
     */
    private function getJobExperiencesRange(): Collection|\Illuminate\Support\Collection|array
    {
        $experiences = $this->experiences->sortBy('sort_order')->values(); // Somehow some item not index correctly

        $expFrom = $experiences[0] ?? null;
        $expTo = $experiences[1] ?? null;

        if (!$expTo) {
            if (!$expFrom) {
                Log::warning("[Job::getJobExperiencesRange] Job {$this->id} has no experiences.");
                return collect();
            }

            return Taxonomy::taxonomy('experiences')
                ->where('id', $expFrom->id)
                ->with('term')
                ->get();
        }

        $from = with($expFrom, static fn($value) => is_null($value) ? 0 : $value->sort_order);
        $to = with($expTo, static fn($value) => is_null($value) ? 999 : $value->sort_order);

        return Taxonomy::taxonomy('experiences')
            ->whereBetween('sort_order', [$from, $to])
            ->orderBy('sort_order')
            ->with('term')
            ->get();
    }

    public function getExperienceFromIdAttribute()
    {
        return $this->experiences_id[0] ?? null;
    }

    public function getExperienceToIdAttribute()
    {
        return $this->experiences_id[1] ?? null;
    }

    public function getSkillsIdsAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'skills')->pluck('id');
    }

    public function isExperienceLessThan3Year(): bool
    {
        return ($this->experience_from_id < Taxonomy::EXPERIENCE_3_YEARS)
            || ($this->experience_from_id == Taxonomy::EXPERIENCE_3_MONTH);
    }

    public function isExperienceAllLevels(): bool
    {
        return $this->experience_from_id == Taxonomy::EXPERIENCE_ALL_LEVELS;
    }

    public function isDoesntHaveNote(): bool
    {
        return $this->notes->count() == 0;
    }

    public function getIsFreeAttribute()
    {
        return is_null($this->crm_invoice_id);
    }

    protected function getNewStatus()
    {
        $status = $this->status;
        switch ($status) {
            case static::STATUS_OPEN:
                $status = $this->opened_at ? static::STATUS_OPEN_PLUS : $status;
                break;
            case static::STATUS_CLOSED:
                $status = $this->closed_at && is_null($this->expires_at) ? static::STATUS_CLOSED_PLUS : $status;
                break;
            default:
                break;
        }
        return $status;
    }

    public function getNewStatusAttribute()
    {
        return $this->getNewStatus();
    }

    public function setNewStatusAttribute($status)
    {
        $this->attributes['status'] = $status;
        return $this;
    }

    /**
     * Handle logic to sync job to CRM system when it's saved
     */
    private function syncJobToCrm()
    {
        $justOpened = false;
        $statusOld = (int) ($this->original['status'] ?? 0);
        $status = (int) $this->status;
        // If changing job from Review to Open OR
        // Just create and open it need sync to CRM
        if (
            (self::STATUS_REVIEW === $statusOld && self::STATUS_OPEN === $status) ||
            ($this->wasRecentlyCreated && self::STATUS_OPEN === $status)
        ) {
            $justOpened = true;
        }

        // If changing ams invoice id or changing status from Reviewd -> Open, need sync to CRM
        if (($justOpened || $this->isDirty('crm_invoice_id')) && $this->owned_id) {
            /**
             * @var \Modules\Subscription\Entities\Package|null $lastPackage
             */
            $lastPackage = $this->subscriptions->last() ?? null;
            $updateResult = CrmApi::syncJobToCrm(
                $this->id,
                $this->owned_id,
                $this->crm_invoice_id,
                ($lastPackage ? $lastPackage->getKey() : null),
                self::STATUS_OPEN === $status,
                ($lastPackage ? ($lastPackage->pivot->is_free_package !== null ? $lastPackage->pivot->is_free_package : null) : null)
            );

            if (isset($updateResult['success']) && isset($updateResult['data']['is_free_package']) && $lastPackage && $lastPackage->pivot->is_free_package !== null) {
                $lastPackage->pivot->is_free_package = $updateResult['data']['is_free_package'];
                $lastPackage->pivot->save();
            }

            // Log to company job posting when changing job status from Review => Open or update invoice
            if ($this->crm_invoice_id && self::STATUS_OPEN === $status && $lastPackage) {
                CompanyJobPostingLog::updateOrCreate(
                    [
                        'company_id' => $this->owned_id,
                        'job_id' => $this->id,
                    ],
                    [
                        'job_title' => $this->title,
                        'package_name' => $lastPackage->term->name ?? null
                    ]
                );
            }
        }
    }

    public function scopeOnlyLowCv(Builder $query)
    {
        $query->where(function (Builder $query) {
            return $query
                ->where(fn (Builder $query) => $query->publishedAtLessThan7DaysAndHas5Cv())
                ->orWhere(fn (Builder $query) => $query->hasLessThan10CvAndHasNoCvIn7Day());
        });
    }

    public function scopePublishedAtLessThan7DaysAndHas5Cv(Builder $query)
    {
        // Published less than 7 days
        $query = $query
            ->where(
                'published_at',
                '>=',
                now()->subDays(7)
            );

        // Has less than or equal 5 cv
        $query = $query
            ->whereHas(
                'job_count',
                fn (Builder $query) => $query->where('candidates_count', '<=', 5)
            );

        return $query;
    }

    public function scopeHasLessThan10CvAndHasNoCvIn7Day(Builder $query)
    {
        // Has less than or equal 10 cv
        $query = $query->whereHas(
            'job_count',
            fn (Builder $query) => $query->where('candidates_count', '<=', 10)
        );

        // Has no cv in 7 days
        $query = $query->whereDoesntHave(
            'candidates',
            fn (Builder $query) => $query->where('created_at', '>=', now()->subDays(7))
        );

        return $query;
    }

    public function isUserMatchExperience(User $user): bool
    {
        $jobExperience = $this->experiences->first();

        if (!$jobExperience) {
            return false;
        }

        $experienceMapping = [
            Taxonomy::EXPERIENCE_ALL_LEVELS => 0,
            Taxonomy::EXPERIENCE_3_MONTH => 0.25,
            Taxonomy::EXPERIENCE_6_MONTH => 0.5,
            Taxonomy::EXPERIENCE_1_YEAR => 1,
            Taxonomy::EXPERIENCE_2_YEARS => 2,
            Taxonomy::EXPERIENCE_3_YEARS => 3,
            Taxonomy::EXPERIENCE_4_YEARS => 4,
            Taxonomy::EXPERIENCE_5_YEARS => 5,
            Taxonomy::EXPERIENCE_6_YEARS => 6,
            Taxonomy::EXPERIENCE_7_YEARS => 7,
            Taxonomy::EXPERIENCE_8_YEARS => 8,
            Taxonomy::EXPERIENCE_9_YEARS => 9,
            Taxonomy::EXPERIENCE_10_YEARS => 10,
            Taxonomy::EXPERIENCE_OVER_10_YEARS => 10,
        ];

        if ($jobExperience->id == Taxonomy::EXPERIENCE_ALL_LEVELS || $jobExperience->id == Taxonomy::EXPERIENCE_NOT_REQUIRED) {
            return true;
        }

        $userExperience = $user->years_of_exp;

        if (!$userExperience) {
            return false;
        }

        return $userExperience >= ($experienceMapping[$jobExperience->id] ?? 0);
    }

    public function isUserMatchSkills(User $user, $isAllMatch = false): bool
    {
        $userSkills = $user->skills_id;
        $jobSkills = $this->skills_id;

        $countIntersect = $userSkills->intersect($jobSkills)->count();

        if ($isAllMatch) {
            return $countIntersect >= count($jobSkills);
        }

        return $countIntersect > 0;
    }

    public function getYearOfExperienceNumberAttribute()
    {
        $jobExperience = $this->experiences->first();

        if (!$jobExperience) {
            return 0;
        }

        $experienceMapping = [
            Taxonomy::EXPERIENCE_ALL_LEVELS => 0,
            Taxonomy::EXPERIENCE_3_MONTH => 0.25,
            Taxonomy::EXPERIENCE_6_MONTH => 0.5,
            Taxonomy::EXPERIENCE_1_YEAR => 1,
            Taxonomy::EXPERIENCE_2_YEARS => 2,
            Taxonomy::EXPERIENCE_3_YEARS => 3,
            Taxonomy::EXPERIENCE_4_YEARS => 4,
            Taxonomy::EXPERIENCE_5_YEARS => 5,
            Taxonomy::EXPERIENCE_6_YEARS => 6,
            Taxonomy::EXPERIENCE_7_YEARS => 7,
            Taxonomy::EXPERIENCE_8_YEARS => 8,
            Taxonomy::EXPERIENCE_9_YEARS => 9,
            Taxonomy::EXPERIENCE_10_YEARS => 10,
            Taxonomy::EXPERIENCE_OVER_10_YEARS => 10,
        ];

        if ($jobExperience->id == Taxonomy::EXPERIENCE_ALL_LEVELS) {
            return 0;
        }

        return $experienceMapping[$jobExperience->id] ?? 0;
    }

    /**
     * Accessor for responsibilities attribute. (->responsibilities)
     */
    public function getResponsibilitiesAttribute()
    {
        return $this
            ->taxonomies
            ->where('taxonomy', 'responsibilities')
            ->map(function ($taxonomy) {
                return [
                    'id' => $taxonomy->id,
                    'name' => $taxonomy->term->name,
                    'description' => Arr::get($taxonomy->pivot->custom_properties ?? [], 'description'),
                    'sort_order' => Arr::get($taxonomy->pivot->custom_properties ?? [], 'sort_order'),
                    'icon' => Arr::first($taxonomy->thumbnail_url),
                ];
            })
            ->sortBy('sort_order')
            ->values()
            ->toArray();
    }

    /**
     * Accessor for requirements attribute. (->requirements)
     */
    public function getRequirementsAttribute()
    {
        return $this
            ->taxonomies
            ->where('taxonomy', 'requirements')
            ->map(function ($taxonomy) {
                return [
                    'id' => $taxonomy->id,
                    'name' => $taxonomy->term->name,
                    'description' => Arr::get($taxonomy->pivot->custom_properties ?? [], 'description'),
                    'sort_order' => Arr::get($taxonomy->pivot->custom_properties ?? [], 'sort_order'),
                    'icon' => Arr::first($taxonomy->thumbnail_url),
                ];
            })
            ->sortBy('sort_order')
            ->values()
            ->toArray();
    }

    /**
     * Accessor for recruitment_processes attribute. (->recruitment_processes)
     */
    public function getRecruitmentProcessesAttribute()
    {
        return $this
            ->taxonomies
            ->where('taxonomy', 'recruitment_processes')
            ->map(function ($taxonomy) {
                return [
                    'id' => $taxonomy->id,
                    'name' => $taxonomy->term->name,
                    'description' => Arr::get($taxonomy->pivot->custom_properties ?? [], 'description'),
                    'sort_order' => Arr::get($taxonomy->pivot->custom_properties ?? [], 'sort_order'),
                    'icon' => Arr::first($taxonomy->thumbnail_url),
                ];
            })
            ->sortBy('sort_order')
            ->values()
            ->toArray();
    }

    /**
     * Accessor for benefits attribute. (->benefits)
     */
    public function getBenefitsAttribute()
    {
        return $this
            ->taxonomies
            ->where('taxonomy', 'benefits')
            ->map(function ($taxonomy) {
                return [
                    'id' => $taxonomy->id,
                    'name' => $taxonomy->term->name,
                    'description' => Arr::get($taxonomy->pivot->custom_properties ?? [], 'description'),
                    'sort_order' => Arr::get($taxonomy->pivot->custom_properties ?? [], 'sort_order'),
                    'icon' => Arr::first($taxonomy->thumbnail_url),
                ];
            })
            ->sortBy('sort_order')
            ->values()
            ->toArray();
    }

    /**
     * Accessor for role_id attribute. (->role_id)
     */
    public function getRoleIdAttribute()
    {
        return $this->role->first()?->id;
    }

    /**
     * Accessor for role attribute. (->role)
     */
    public function getRoleAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'role');
    }

    /**
     * Accessor for education_major attribute. (->education_major)
     */
    public function getEducationMajorAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'education_major');
    }

    /**
     * Accessor for requirements attribute. (->taxonomy_requirements)
     */
    public function getTaxonomyRequirementsAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'requirements');
    }

    /**
     * Accessor for taxonomy_responsibilities attribute. (->taxonomy_responsibilities)
     */
    public function getTaxonomyResponsibilitiesAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'responsibilities');
    }

    /**
     * Accessor for taxonomy_recruitment_processes attribute. (->taxonomy_recruitment_processes)
     */
    public function getTaxonomyRecruitmentProcessesAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'recruitment_processes');
    }

    /**
     * Accessor for education_degree attribute from education taxonomy. (->education_degree)
     */
    public function getEducationDegreeAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'education');
    }

    /**
     * Accessor for taxonomy_benefits attribute. (->taxonomy_benefits)
     */
    public function getTaxonomyBenefitsAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'benefits');
    }

    /**
     * Accessor for top_banner attribute. (->job_banner)
     */
    public function getJobBannerAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_banner');
    }

    /**
     * Accessor for job_template attribute. (->job_template)
     */
    public function getJobTemplateAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_template');
    }

    /**
     * Accessor for job_template_color attribute. (->job_template_color)
     */
    public function getJobTemplateColorAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_template_color');
    }

    /**
     * Accessor for company_logo attribute. (->company_logo)
     */
    public function getCompanyLogoAttribute()
    {
        return $this->getMedia('company_logo')->first();
    }

    /**
     * Set new accessor for education_certificate attribute from Job. (->edu_certificate)
     *
     * @param mixed $value
     */
    public function setEduCertificateAttribute($value)
    {
        $this->attributes['education_certificate'] = $value;
    }

    public function getTemplateIdAttribute()
    {
        return $this->job_template_id[0] ?? null;
    }

    public function getTemplateColorIdAttribute()
    {
        return $this->job_template_color_id[0] ?? null;
    }

    public function getBannerIdAttribute()
    {
        return $this->job_banner_id[0] ?? null;
    }

    /**
     * Accessor for template_slug attribute. (->template_slug)
     */
    public function getTemplateSlugAttribute()
    {
        return $this->job_template->pluck('slug')->first();
//        return 'template-5';
    }

    /**
     * Accessor for template_color_desc attribute. (->template_color_desc)
     */
    public function getTemplateColorDescAttribute()
    {
        return $this->job_template_color->pluck('description')->first();
    }

    public function getResponsibilitiesOriginalAttribute()
    {
        return $this->attributes['responsibilities'];
    }

    public function setResponsibilitiesOriginalAttribute($value)
    {
        $this->attributes['responsibilities'] = $value;
    }

    /**
     * Accessor for requirements_original attribute. (->requirements_original)
     */
    public function getRequirementsOriginalAttribute()
    {
        return $this->attributes['requirements'];
    }

    public function setRequirementsOriginalAttribute($value)
    {
        $this->attributes['requirements'] = $value;
    }

    /**
     * Accessor for is_content_image attribute. (->is_content_image)
     */
    public function getIsContentImageAttribute()
    {
        return ! is_null($this->premium_upgraded_at);
    }

    public function setIsContentImageAttribute($value)
    {
        if ($this->is_content_image == $value) {
            return;
        }

        $this->premium_upgraded_at = $value ? now() : null;
        $this->premium_enabled_at = $value ? now() : null;
    }

    /**
     * Accessor for color_properties attribute. (->color_properties)
     */
    public function getColorPropertiesAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_template')->first()?->job_template_color_properties ?? '[]';
    }

    /**
     * Accessor for job_blade_html attribute. (->job_blade_html)
     */
    public function getJobBladeHtmlAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_template')->first()?->job_template_blade_html ?? '';
    }

    public function job_categories(): BelongsToMany
    {
        return $this->belongsToMany(JobCategory::class, 'job_category_job');
    }

    /**
     * Accessor for job_category_id attribute. (->job_category_id)
     */
    public function getJobCategoryIdAttribute()
    {
        return $this->job_category?->id;
    }

    /**
     * Accessor for job_category_role_ids attribute. (->job_category_role_ids)
     */
    public function getJobCategoryRoleIdsAttribute()
    {
        return $this->job_categories->where('type', JobCategoryType::ROLE)->pluck('id');
    }

    /**
     * Accessor for job_category attribute. (->job_category)
     */
    public function getJobCategoryAttribute()
    {
        return $this->job_categories
            ->where('type', JobCategoryType::CATEGORY)
            ->first();
    }

    public function setJobCategoryIdAttribute($value)
    {
        if ($this->id < 1 || !$value || $value < 1) {
            return;
        }

        $roleIds = $this->job_categories->where('type', JobCategoryType::ROLE)->pluck('id')->toArray();

        $this->job_categories()->sync(array_merge([$value], $roleIds));
    }

    public function setJobCategoryRoleIdsAttribute($value)
    {
        if ($this->id < 1 || !is_array($value) || empty($value)) {
            return;
        }

        $categoryId = $this->job_category?->id;
        $syncIds = $value;

        if ($categoryId) {
            $syncIds = array_merge([$categoryId], $syncIds);
        }

        $this->job_categories()->sync($syncIds);
    }

    public function getCurrentLevelDurationInDaysAttribute()
    {
        if ($this->level === Job::JOB_LEVEL_FREE) {
            return Job::JOB_FREE_DURATION_IN_DAYS;
        }

        if ($this->subscribedTo('basic-15-days') || $this->subscribedTo('basic-plus-15-days')) {
            return Job::JOB_PAID_DURATION_15_DAYS_IN_DAYS;
        }

        return Job::JOB_PAID_DURATION_IN_DAYS;
    }
}

