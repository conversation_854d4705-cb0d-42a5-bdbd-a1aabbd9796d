<?php

namespace Modules\Job\Http\Controllers;

use Amscore\Admin\Form\EmbeddedForm;
use App\Helpers\CrmApi;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Amscore\Admin\Controllers\AdminController;
use Amscore\Admin\Form\Tools;
use Amscore\Admin\Grid;
use Amscore\Admin\Show;
use Amscore\Admin\Grid\Displayers\Label;
use Amscore\Admin\Layout\Content;
use Amscore\Admin\Widgets\Table;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Notification;
use Modules\Job\Actions\BatchCloseJob;
use Modules\Job\Actions\DuplicateJob;
use Modules\Job\Entities\Job;
use Modules\Job\Entities\JobCategory;
use Modules\Job\Entities\JobCount;
use Modules\Job\Rules\FreeJobStatusReviewRule;
use Modules\Job\Services\CandidateService;
use Modules\Job\Notifications\NotifyActivateWarranty;
use Modules\Job\Services\JobService;
use Modules\Job\Transformers\AuditTransformer as JobAuditTransformer;
use Modules\Taxonomy\Entities\Terms;
use App\Actions\AddNote;
use Modules\Company\Entities\Company;
use Modules\Taxonomy\Repositories\Contracts\TaxonomyRepositoryInterface;
use Modules\Job\Actions\CrossApply;
use App\Actions\MarkAsOpen;
use App\Actions\MarkAsClose;
use App\Actions\Republish;
use App\Actions\Restore;
use App\Tools\NoteTool;
use App\Channels\HackerRankApi;
use App\Contracts\Form;
use Amscore\Admin\Form\NestedForm;
use App\Tools\FacebookFeedTool;
use App\Tools\GoogleAdsFeedTool;
use Illuminate\Support\Facades\Cache;
use Modules\Publisher\Actions\RemoveFacebookCatalog;
use Modules\Publisher\Actions\RemoveGoogleDatafeed;
use Modules\VietnamArea\Entities\VietnamArea;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Modules\Job\Actions\GoogleAdsAction;
use Modules\Job\Actions\FacebookAdsAction;
use Modules\Job\Actions\RemoveGoogleAds;
use Modules\Job\Actions\RemoveFacebookAds;
use Modules\Job\Actions\PersonInCharge;
use Amscore\Admin\Auth\Database\Administrator;
use Amscore\Admin\Facades\Admin;
use App\Facades\Unleash;
use App\Helpers\FeatureFlag;
use Illuminate\Support\Facades\Log;
use OwenIt\Auditing\Models\Audit;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;
use Modules\Job\Entities\JobLocationBestMatch;

class JobController extends AdminController
{
    /**
    * Title for current resource.
    *
    * @var string
    */
    protected $title = 'Job Dashboard';

    /**
    * @var TaxonomyRepositoryInterface
    */
    protected TaxonomyRepositoryInterface $taxonomyRepository;

    /**
    * Create instance JobController
    */
    public function __construct(TaxonomyRepositoryInterface $taxonomy)
    {
        $this->taxonomyRepository = $taxonomy;
    }

    /**
    * List jobs by company.
    *
    * @param Content $content
    * @param Company $company
    *
    * @return Content
    */
    public function jobsOfCompany(Content $content, Company $company)
    {
        return $content
        ->title($this->title())
        ->description($this->description['index'] ?? trans('admin.list'))
        ->body($this->grid($company));
    }

    /**
    * Make a grid builder.
    *
    * @return Grid
    */
    protected function grid($filter = null)
    {
        $grid = new Grid((new Job()));

        $grid->model();
        $grid->setResource(url('/admin/jobs'));

        if ($filter instanceof Company) {
            $grid->model()->ofCompany($filter);
        }

        $grid->filter(function ($filter) {
            /** @var Grid\Filter $filter */
            $filter->scope('paid', 'Job paid')->onlyPaid();
            $filter->scope('free', 'Job free')->onlyFree();
            $filter->scope('crawl', 'Job crawl')->onlyCrawl();
            $filter->scope('trashed', 'Recycle Bin')->onlyTrashed();
            $filter->scope('hasSimilarJobs', 'Has Similar Jobs')->hasSimilarJobs();
            $filter->scope('doesntHaveSimilarJobs', 'Does Not Have Similar')->doesntHaveSimilarJobs();
        });

        $grid->quickSearch(function ($model, $keyword) {
            // $model->where(function ($query) use ($keyword) {
            //     $query->orwhere('jobs.id', $keyword)
            //     ->orWhere('jobs.title', 'LIKE', "%{$keyword}%")
            //     ->orWhereHas('company', function ($query) use ($keyword) {
            //         return $query->where('companies.display_name', 'LIKE', "%{$keyword}%");
            //     });
            // });

            $baseQuery = new BoolQuery();
            $baseQuery->add(new MultiMatchQuery(
                ['title', 'company.display_name', '_id'],
                $keyword,
                ['operator' => 'and']
            ));

            $jobs = Job::search('*', function ($client, $body) use ($baseQuery) {
                $body->addQuery($baseQuery);
                $body->setSize(10000);
                $body->setFrom(0);
                return $client->search([
                    'index' => app(Job::class)->searchableAs(),
                    'body' => $body->toArray()
                ])->asArray();
            })->get();

            $model->whereIn('id', $jobs->pluck('id'));
        })->placeholder('Search ID, title, employer ...');

        $grid->filter(function ($filter) {
            $filter->expand();
            $filter->disableIdFilter();

            $filter->column(1 / 2, function ($filter) {
                $filter->where(function ($query) {
                    $query->where('status', '=', $this->input);
                }, 'Status', 'filter_status_id')->select(Job::getStatusDescription());

                $filter->where(function ($query) {
                    $query->withAnyTerms($this->input);
                }, 'Taxonomy', 'filter_skill_ids')->multipleSelect(
                    // app(Terms::class)->selectTerm(array_keys(Terms::TAXONOMY_KEY))->pluck('name', 'id')
                    '/admin/ajax/terms?taxonomy=' . implode(',', array_keys(Terms::TAXONOMY_KEY))
                );

                $filter->where(function ($query) {
                    $query->whereIn('owned_id', $this->input);
                }, 'Company', 'filter_owned_id')->multipleSelect()->ajax('/admin/ajax/companies');

                $filter->where(function ($query) {
                    $query->when(
                        $this->input,
                        fn($query, $value) =>
                            $query->when(
                                $value == Job::PRICING_TYPE_PAID,
                                fn($subQuery) => $subQuery->whereNotNull('crm_invoice_id'),
                                fn($subQuery) => $subQuery->whereNull('crm_invoice_id'),
                            )
                    );
                }, 'Job Type')->select(Job::getJobPricingType());
            });

            $filter->column(1 / 2, function ($filter) {
                /** @var Grid\Filter $filter */
                $filter->where(function ($query) {
                    /** @var Builder $query */
                    /** @var $this Grid\Filter\Where */
                    $query->scopes(['similarJobIn' => $this->input]);
                }, 'Similar', 'similar_job_id');

                $filter->where(function ($query) {
                    $query->whereHas('job_meta', function ($query) {
                        $query
                            ->where('meta_title', 'like', "%{$this->input}%")
                            ->where('meta_keywords', 'like', "%{$this->input}%")
                            ->where('meta_description', 'like', "%{$this->input}%");
                    });
                }, 'Meta', 'filter_meta');

                $filter->where(function ($query) {
                    $query->whereHas('company', function ($addressQuery) {
                        $addressQuery->withAnyArea($this->input, VietnamArea::VIETNAM_AREA_PROVINCE);
                    });
                }, 'Location', 'filter_location_ids')->multipleSelect(
                    VietnamArea::province()->get()->keyBy('code')->map(function ($item) {
                        return $item['name'];
                    })
                );

                $filter->between('updated_at', 'Updated at')->datetime();
            });
        });
        $grid->header(function ($query) {
            return view('job::job-missing-invoice-warning');
        });

        $grid->column('id', __('Id'))->view('job::job-notes');

        $grid->column('image_thumbnail_url', 'Thumbnail')->image(null, 70);

        $grid->column('Info')->display(function ($value, $column) use ($grid) {
            /** @var Job $this */
            $info = '<b>Job Name: </b><a href="' . $this->detail_url . '" target="_blank" >' . $this->title . '</a>';

            // Check miss best match skills
            if ($this->required_skills_name->isEmpty() && $this->optional_skills_name->isEmpty()) {
                $info .= '<span title="Missing best match skills" class="warning_required_skills"><i class="fa fa-exclamation-circle" aria-hidden="true"></i></span>';
            }

            $info .= '<br>';

            if ($this->address_short_region_list) {
                $info .= '<b>Location: </b> ' . $this->address_short_region_list . '<br>';
            }
            if ($this->subscriptions->map->term->map->name->implode(', ')) {
                $info .= '<b>Packages: </b> ' . $this->subscriptions->map->term->map->name->implode(', ') . '<br>';
            }
            $info .= '<b>Fresh: </b> ' . $this->refreshed_count . '<br>';
            $info .= '<b>Company: </b><a href="' . url('/admin/companies/' . $this->company->getKey() . '/jobs') . '" target="_blank" >' . $this->company->display_name . '</a><br>';
            if ($this->link_crawl) {
                $info .= '<b>Link Crawl: </b>' . $this->link_crawl . '</br>';
            }

            if (!empty($this->job_note)) {
                $label = new Label($this->job_note->notes_label, $grid, $column, $this);
                $info .= $label->display();
            }
            return $info;
        })->style('max-width:300px;word-break: break-word;');

        $grid->column('summary_viewers', 'Views/Days/AVG');

        $grid->column('Deli/Apply/ClickApply')->display(function () {
            /** @var Job $this */
            $link = '/candidates';
            if ($this->owned_id == config('app.talentsuccess.id')) {
                $link = '/talentsuccess/candidates';
            }
            $similarCount = sprintf('<a href="%s">%s</a>', route('jobs.show', ['job' => $this]), $this->similar_jobs_count);
            return '<a href="/admin'.$link.'/?status=1&job_id=' . $this->getKey() . '">' . $this->job_count->candidate_ready_count . '</a>' .
            ' / <a href="/admin'.$link.'/?job_id=' . $this->getKey() . '">' . $this->job_count->candidates_count . '</a>' .
            ' / ' . $this->job_count->click_apply_count . '<br />'
                . '<br/>'
                . $similarCount . '<br/>'
                ;
        });

        $grid->column('best_match','Best match')->display(function () {

            /** @var Job $this */
            $bestMatchCount = $this->jobBestMatch->count();

            return $bestMatchCount ? '<a style="color: #16A34A; font-weight: bold" href="/admin/candidates/?avg_skill_match=1&job_id=' . $this->getKey() . '">' . $bestMatchCount . '</a>' : "-";
        });

        $grid->column('status_display', __('Status'))->label([
            'Draft' => 'default',
            'Closed' => 'warning',
            'Closed*' => 'warning',
            'Open' => 'success',
            'Open*' => 'success',
            'Review' => 'info',
        ]);

        $grid->column('Features')->display(function () {
            $html = '';

            if (!$this->is_hot == true) {
                $html .= '<span style="display: block; margin-bottom: 5px;" class="label label-default">Super Hot Job</span>';
            } else {
                $html .= '<span style="display: block; margin-bottom: 5px;" class="label label-danger">Super Hot Job</span>';
            }

            if ($this->social_column) {
                if (!empty($this->fbads_sheet_id)) {
                    $html .= '<span style="display: block; margin-bottom: 5px;" class="label label-success">FB Catalog ID: ' . $this->fbads_sheet_id . '</span>';
                } else {
                    $html .= '<span style="display: block; margin-bottom: 5px;" class="label label-default">Feed To Catalog FB</span>';
                }

                if (!empty($this->ggads_sheet_id)) {
                    $html .= '<span style="display: block; margin-bottom: 5px;" class="label label-success">GG Datafeed ID: ' . $this->ggads_sheet_id . '</span>';
                } else {
                    $html .= '<span style="display: block; margin-bottom: 5px;" class="label label-default">Feed To GG DataFeed</span>';
                }
            }

            return $html;
        });

        $grid->column('')->display(function () {
            return
                '<b>Published at:</b> ' . $this->published_at . '<br>' .
                '<b>Expires at:</b> ' . $this->expires_at . '<br>' .
                '<b>Updated at:</b> ' . $this->updated_at . '<br>' .
                ($this->audits->first() ? '<b>Created by:</b> ' . ($this->audits->first()->user->email ?? 'Administrator') . '<br>' : '') .
                ($this->audits->last() ? '<b>Last modified by:</b> ' . ($this->audits->last()->user->email ?? 'Administrator') . ' ' .  $this->audits->last()->created_at . '<br>' : '');
        });

        $grid->actions(function ($actions) {
            $actions->add(new AddNote());

            if ($actions->row->isClose()) {
                $actions->add(new MarkAsOpen());
            }

            if ($actions->row->isOpen()) {
                $actions->add(new Republish());
                $actions->add(new MarkAsClose());
            }

            if (\request('_scope_') == 'trashed') {
                $actions->add(new Restore());
            }

            $actions->add(new CrossApply());
            $actions->add(new DuplicateJob());

            $actions->add(new PersonInCharge());

            $actions->disableDelete();

            if (!empty($this->row->fbads_sheet_id)) {
                $actions->add(new RemoveFacebookAds());
            }

            if (!empty($this->row->ggads_sheet_id)) {
                $actions->add(new RemoveGoogleAds());
            }
        });

        $grid->batchActions(function ($batch) {
            $batch->add(new BatchCloseJob());
            $batch->add(new GoogleAdsAction());
            $batch->add(new FacebookAdsAction());
        });

        $grid->model()->collection(function ($collection) {
            return $collection->map->append(['address_short_region_list', 'summary_viewers', 'image_thumbnail_url']);
        });

        $grid
            ->model()
            ->with([
                'addresses',
                'job_note',
                'notes.author',
                'taxonomies.term.translations',
                'media',
                // 'company',
                'company.translations',
                'subscriptions.term',
                'features.term',
                'job_count',
                'personChargeJob',
                'audits',
                'audits.user',
            ])
            ->withCount('similar_jobs')
            ->orderBy('id', 'desc')
            ->when(request('missing_invoice'), fn($query) => $query->whereNull('crm_invoice_id'));

        return $grid;
    }

    /**
    * Make a show builder.
    *
    * @param mixed $id
    * @return Show
    */
    protected function detail($id)
    {
        $show = new Show(Job::withoutGlobalScopes()->findOrFail($id));
        $show->field('id', __('Id'));
        $show->field('title', __('Title'));
        $show->field('content', __('Nội dung'));
        $show->field('requirements');
        $show->field('responsibilities');
        $show->field('expires_at');
        // $show->field('refreshed_at');
        // $show->field('published_at');
        $show->field('deleted_at');
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));
        // $show->field('company');

        /** @var Job $model */
        $model = $show->getModel();
        $model->loadCount('similar_jobs');

        $show->relation('company', 'Company', function ($companyShow) {
            /** @var Show $companyShow */
            $companyShow->fields(['id', 'display_name']);

            $companyShow->relation('addresses', 'Company Addresses', function ($addressGrid) {
                /** @var Grid $addressGrid */
                $addressGrid->columns([
                    'display_name' => 'name',
                    'street' => 'street',
                    'ward.name' => 'ward',
                    'district.name' => 'district',
                    'province.name' => 'province',
                    'postal_code' => 'postal_code',
                    'latitude' => 'latitude',
                    'longitude' => 'longitude',
                ]);

                static::disableGridOption($addressGrid);
            });

        });

        $show->relation('taxonomies', function ($taxonomiesGrid) {
            /** @var Grid $taxonomiesGrid */
            $taxonomiesGrid->model()->with(['term.translations']);
            $taxonomiesGrid->columns([
                'taxonomy' => '',
                'term.slug' => 'slug',
                'term.name' => 'name',
                'term.description' => 'description',
            ]);

            static::disableGridOption($taxonomiesGrid);
        });


        $show->relation('similar_jobs', 'Similar Jobs '.$model->similar_jobs_count, function ($similarJobsGrid) {
            /** @var Grid $similarJobsGrid */
            $columns = [
                'jobs.id as id', 'slug', 'status', 'title', 'owned_id',
                // without column content, requirements, responsibilities, ..
                'expires_at', 'jobs.created_at', 'jobs.updated_at',
                'confidence', // Grid/Model is not support pivot properly
                'job_similar_job.created_at as pivot_created_at',
                'job_similar_job.updated_at as pivot_updated_at',
            ];

            $similarJobsGrid->model()
                ->with([
                    // 'job_information',
                    'taxonomies.term.translations',
                    'company.translations',
                    'company.addresses.ward',
                    'addresses.ward',
                    // 'addresses.district',
                    'addresses.province',
                    // 'company.media',
                    'job_count'
                ])->select($columns);

            $similarJobsGrid->column('id')->display(function () {
                /** @var Job $this */
                return sprintf('<a href="%s">%s</a>', route('jobs.show', ['job' => $this]), $this->getKey());
            });

            $similarJobsGrid->column('title', 'Title & Company')->display(function ($value) {
                /** @var Job $this */
                $title = ($this->isClose() || $this->isOpen()) ? sprintf('<a href="%s" target="_blank">%s</a>',
                    $this->detail_url,
                    $value) : "<span class='text-gray'>$value</span>";
                return $title.'<br />'
                    .'<span class="experiences">'.implode(', ', $this->experiences_name->toArray()).'</span>'.'<br />'
                    .'<span class="skill">'.implode(', ', $this->skills_name->toArray()).'</span>'.'<br />'
                    .'<span class="main-skill text-bold">'.implode(', ', $this->extra_skills_name->toArray()).'</span>'.'<br />'
                    .'<span class="job-address text-gray">'.$this->address_short_region_list.'</span> '.'<br />'
                    .'<span class="company_name text-gray text-bold">'.$this->company->display_name.'</span> '.'<br />'
                    .'<span class="company-address text-gray">'.$this->company->address_short_region_list.'</span> '.'<br />'
                    ;
            });

            $similarJobsGrid->column('job_count', 'Count & Taxonomy')->display(function () {
                /** @var Job $this */
                $countArray = Arr::except($this->job_count->toArray(),
                    ['job_id', 'id', 'created_at', 'updated_at', 'deleted_at']);

                return '<textarea cols="40" rows="4">'
                    .json_encode($countArray + [
                            'exp' => $this->experiences_name,
                            'type' => $this->job_types_name,
                            'level' => $this->job_levels_name,
                            'cat' => $this->categories_name,
                            // 'addresses' => $this->addresses,
                            // 'taxonomies' => $this->taxonomies,
                            // 'skill' => $this->skills_name,
                            // 'main' => $this->extra_skills_name
                        ], JSON_UNESCAPED_CUSTOM | JSON_PRETTY_PRINT).'</textarea>';
            });

            $similarJobsGrid->column('status_display', __('Status'))->label([
                'Draft' => 'default',
                'Closed' => 'warning',
                'Open' => 'success',
                'Review' => 'info',
            ]);

            $similarJobsGrid->column('', 'updated_at/expires_at')->display(function () {
                return $this['updated_at'].'<br>'
                    .'<b>'.$this['expires_at'].'</b>';
            });

            $similarJobsGrid->column('confidence', 'confidence/created/updated')->display(function ($value) {
                /** @var Job $this */
                return $value.'<br />'
                    .$this['pivot_created_at'].'<br />'
                    .$this['pivot_updated_at'].'<br />';
            });

            static::disableGridOption($similarJobsGrid);

        });

        return $show;
    }

    public static function disableGridOption(Grid $grid)
    {
        $grid->disableActions()
            ->disableTools()
            ->disableColumnSelector()
            ->disableRowSelector()
            ->disableExport()
            ->disablePagination()
            ->disableFilter()
            ->disableCreateButton();
    }

    /**
     * Get All taxonomies for job creating/editing
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getTaxonomiesForJob()
    {
        return $this->taxonomyRepository->getModel()
            ->with('term.translations')
            ->whereIn(
                'taxonomy',
                [
                    'categories',
                    'packages',
                    'skills',
                    'services',
                    'job_levels',
                    'job_types',
                    'experiences',
                    'other_supports',
                    'contract_types',
                    'role',
                    'responsibilities',
                    'benefits',
                    'education',
                    'requirements',
                    'recruitment_processes',
                    'education_major',
                    'job_banner',
                    'job_template',
                    'job_template_color',
                ]
            )
            ->get()
            ->groupBy(function ($item) {
                return $item->taxonomy;
            });
    }

    /**
    * Make a form builder.
    *
    * @return Form
    */
    protected function form($id = null)
    {
        if (!empty($id)) {
            $form = new Form(Job::withoutGlobalScopes());
        } else {
            $form = new Form(new Job());
        }

        $taxonomies = $this->getTaxonomiesForJob();
        $form->tab('Basic info', function (\App\Contracts\Form $form) use ($taxonomies) {
            $form->select('level', 'Level')
                ->options([
                    'free' => 'Free',
                    'paid' => 'Paid'
                ])
                ->default('free')
                ->required();

                $states = [
                    'on'  => ['value' => 1, 'text' => 'enable', 'color' => 'success'],
                    'off' => ['value' => 0, 'text' => 'disable', 'color' => 'danger'],
                ];

                $form->switch('is_content_image', 'Content image')->states($states);


            $form->divider();

            $form
                ->text('title', __('Title'))
                ->rules('required')
                ->showOriginal()
                ->showPrevious();

            $form->select('new_status', __('Status'))->options(Job::getStatusDescription());
            $form->date('expires_at', 'Expires at');
            $form->date('published_at', 'Publish at');
            $form->date('original_published_at', 'Original Publish at');
            $form->datetime('refreshed_at', 'Refresh at')->rules('before_or_equal:now');

            $form
                ->tinymce('content', __('JOB DESCRIPTION'))
                ->showOriginal()
                ->showPrevious()
                ->auditShowModal();

            $form
                ->tinymce('responsibilities_original', __('JOB RESPONSIBILITY'))
                ->showOriginal()
                ->showPrevious()
                ->auditShowModal();

            $form->sortableTable('responsibilities', 'Responsibilities', function (\Amscore\Admin\Form\NestedForm $table) use ($taxonomies) {
                $table->select('id', 'Name')
                    ->options($taxonomies->get('responsibilities')?->pluck('term.name', 'id')->all())
                    ->rules('required')
                    ->loadTextDescription('description', '/admin/ajax/taxonomy-description');

                $table->text('description', 'Description');
            });

            $form
                ->tinymce('requirements_original', __('JOB REQUIREMENT'))
                ->showOriginal()
                ->showPrevious()
                ->auditShowModal();

            $form->sortableTable('requirements', 'Requirements', function (\Amscore\Admin\Form\NestedForm $table) use ($taxonomies) {
                $table->select('id', 'Name')
                    ->options($taxonomies->get('requirements')?->pluck('term.name', 'id')->all())
                    ->rules('required')
                    ->loadTextDescription('description', '/admin/ajax/taxonomy-description');

                $table->text('description', 'Description');
            });

            $form->file('content_html_desktop_file', 'Upload content_html_desktop')
                ->rules('mimes:html,htm')
                ->help('Upload file html, htm to update content_html_desktop');

            $form->file('content_html_mobile_file', 'Upload content_html_mobile')
                ->rules('mimes:html,htm')
                ->help('Upload file html, htm to update content_html_mobile');

            $form->hidden('opened_at', 'opened_at');
            $form->hidden('closed_at', 'closed_at');
        })
        /**
        * Quan ly don vi so huu job.
        *
        */
        ->tab('Company', function ($form) {
            $form->select('owned_id', 'Owner')->options(function ($id) {
                $company = Company::find($id);

                if (!empty($company)) {
                    return [$company->id => $company->display_name];
                }
            })
            ->ajax('/admin/ajax/companies?status=1')
            ->load('addresses_id', '/admin/ajax/companies/addresses');

            $form
                ->multipleSelect('addresses_id', 'Addresses')->options(function () {
                    return $this->company->addresses->pluck('display_name', 'id');
                })
                ->showOriginal()
                ->showPrevious()
                ->auditShowModal()
                ->auditColumn('addresses');

            $form->tags('emails_cc', 'Send New Candidate Emails To');
        })
        /**
        * Quan ly phan loai job.
        *
        */
        ->tab('Taxonomy', function (Form $form) use ($taxonomies, $id) {
            $form->select('job_category_id', 'Job category')
                ->options(
                    JobCategory::query()->jobCategoriesEn()->get()->pluck('name', 'id')
                )
                ->required()
                ->load(
                    'job_category_role_ids',
                    api_url('/job-categories/get-roles-by-category-id-request').request('job_category_id'),
                    'id',
                    'name',
                );

            /*$form
                ->select('role_id', 'Role')
                ->options(
                    empty($taxonomies->get('role'))
                        ? []
                        : $taxonomies->get('role')->sortBy('sort_order')->pluck('term.name', 'id')
                )
                ->required();*/

            $jobRoles = Job::find($id)->job_category?->roles ?? JobCategory::query()->isRole()->get();
            $form->multipleSelect('job_category_role_ids', 'Job Roles')
                ->options($jobRoles->pluck('name', 'id') ?: [])
                ->required();

            $form
                ->multipleSelect('job_levels_id', 'Job levels')
                ->options(
                    empty($taxonomies->get('job_levels')) ? [] : $taxonomies->get('job_levels')->pluck('term.name', 'id')
                )
                ->required();

            $form->divider();
            $form->html('Experiences');

            $form
                ->select('experience_from_id', __('Experiences From'))
                ->help('Only the minimum year of experience will be displayed on TopDev website')
                ->options(
                    empty($taxonomies->get('experiences')) ? [] : $taxonomies->get('experiences')->sortBy('sort_order')->pluck('term.name', 'id')
                );

            $form
                ->select('experience_to_id', __('Experiences To'))
                ->options(
                    empty($taxonomies->get('experiences')) ? [] : $taxonomies->get('experiences')->sortBy('sort_order')->pluck('term.name', 'id')
                );

            $form->divider();

            $form->multipleSelect('job_types_id', 'Job types')->options(
                empty($taxonomies->get('job_types')) ? [] : $taxonomies->get('job_types')->pluck('term.name', 'id')
            );

            $form->multipleSelect('contract_types_id', 'Contract types')->options(
                empty($taxonomies->get('contract_types')) ? [] : $taxonomies->get('contract_types')->pluck('term.name', 'id')
            );

            $form->multipleSelect('categories_id', 'Categories')->options(
                empty($taxonomies->get('categories')) ? [] : $taxonomies->get('categories')->pluck('term.name', 'id')
            );

            if (Admin::user()->isAdministrator()) {
                $form->tags('skills_name', 'Best skills')->options(
                    empty($taxonomies->get('skills')) ? [] : $taxonomies->get('skills')->pluck('term.name', 'id')
                );
            } else {
                $form->multipleSelect('skills_name', 'Best skills')->options(
                    empty($taxonomies->get('skills')) ? [] : $taxonomies->get('skills')->pluck('term.name', 'term.name')
                );
            }

            $form->tags('extra_skills_name', 'Main skills')->options(
                empty($taxonomies->get('skills')) ? [] : $taxonomies->get('skills')->pluck('term.name', 'id')
            );

            $form->divider();
            $form->html('<div class="group_required_optional_skills">Best Match</div>');

            $form->tags('required_skills_name', 'Required skills')->options(
                empty($taxonomies->get('skills')) ? [] : $taxonomies->get('skills')->pluck('term.name', 'id')
            );

            $form->tags('optional_skills_name', 'Optional skills')->options(
                empty($taxonomies->get('skills')) ? [] : $taxonomies->get('skills')->pluck('term.name', 'id')
            );

            $form->radio('location_type', 'Location')->options([
                0 => 'Same job location(s)',
                1 => 'Other'
            ])
            ->setGroupClass('location_type_best_match')
            ->required();

            $form->multipleSelect('location_best_match', 'New province')
            ->options(
                VietnamArea::province()->get()->keyBy('code')->map(function ($item) {
                    return $item['name'];
                })
            )->setGroupClass('province_best_match');

            $form->divider();

            $form->html("Template Design");

            $form
                ->select('banner_id', 'Banner')
                ->options(
                    empty($taxonomies->get('job_banner'))
                        ? []
                        : $taxonomies->get('job_banner')->sortBy('sort_order')->pluck('term.name', 'id')
                );

            $form
                ->select('template_id', 'Job template')
                ->options(
                    empty($taxonomies->get('job_template'))
                        ? []
                        : $taxonomies->get('job_template')->sortBy('sort_order')->pluck('term.name', 'id')
                );

            $form
                ->select('template_color_id', 'Job template color')
                ->options(
                    empty($taxonomies->get('job_template_color'))
                        ? []
                        : $taxonomies->get('job_template_color')->sortBy('sort_order')->pluck('term.name', 'id')
                );
        })
        /**
        * Quan ly media, files job.
        *
        */
        ->tab('Media', function ($form) {
            $form->multipleImage('image_banner_url', 'Image banner')->removable();
            $form->multipleImage('image_thumbnail_url')->removable();
            $form->multipleImage('image_galleries_url')->removable();
        })
        /**
        * Quan ly noi dung job.
        *
        */
        ->tab('Extra Info', function ($form) use ($taxonomies) {
            $form->tinymce('job_information_benefit', 'Job Benefit');

            $form->sortableTable('benefits', 'Benefits', function (\Amscore\Admin\Form\NestedForm $table) use ($taxonomies) {
                $table->select('id', 'Name')
                    ->options($taxonomies->get('benefits')?->pluck('term.name', 'id')->all())
                    ->rules('required')
                    ->loadTextDescription('description', '/admin/ajax/taxonomy-description');

                $table->text('description', 'Description');
            });

            $form->sortableTable('job_information_other_supports', 'Other support', function ($table) use ($taxonomies) {
                $table
                    ->select('term_id', 'Name')
                    ->options(
                        empty($taxonomies->get('other_supports')) ? [] : $taxonomies->get('other_supports')->pluck('term.name', 'id')
                    );
                $table->text('description', 'Description');
            });

           $form
               ->sortableTable('job_note_recruiment_process', 'Recruitment process <br>(max 8 steps)', function ($table) {
                   $table
                       ->text('name', 'Name')
                       ->attribute(['maxlength' => 100])
                       ->help('Tối đa 100 ký tự, KHÔNG nhập IN HOA toàn bộ')
                       ->rules('min:3|max:100');
               })
               ->setMaxLimit(8)
               ->showOriginal()
               ->showPrevious()
               ->auditShowModal()
               ->auditAttribute('job_note_audits')
               ->auditColumn('recruiment_process');

            $form->sortableTable('recruitment_processes', 'Recruitment Process', function (\Amscore\Admin\Form\NestedForm $table) use ($taxonomies) {
                $table->select('id', 'Name')
                    ->options($taxonomies->get('recruitment_processes')?->pluck('term.name', 'id')->all())
                    ->rules('required')
                    ->loadTextDescription('description', '/admin/ajax/taxonomy-description');

                $table->text('description', 'Description');
            });


            $form->sortableTable('job_information_languages', 'Languages', function ($table) use ($taxonomies) {
                $table
                    ->select('language', 'Language')
                    ->options(
                        empty($taxonomies->get('language')) ? [] : $taxonomies->get('language')->pluck('term.name', 'id')
                    );

                $table->select('fluency', 'Fluency')->options(
                    empty($taxonomies->get('language_level')) ? [] : $taxonomies->get('language_level')->pluck('term.name', 'id')
                );
            });

            $form
                ->embeds('job_information_salary', 'Salary', function (EmbeddedForm $form) {
                    $form->radio('is_negotiable', 'Negotiable')
                        ->options([1 => 'Yes', 0 => 'No'])
                        ->default(0)
                        ->required();

                    $form->number('min', 'Min salary')->default(0)->setGroupClass('salary-min-max-input');
                    $form->number('max', 'Max salary')->default(0)->setGroupClass('salary-min-max-input');
                    $form->select('currency', 'Currency')
                        ->options(config('constant.CURRENCY'))
                        ->default('VND')
                        ->required()
                        ->setGroupClass('salary-min-max-input');

                    $form
                        ->html('Thông tin lương này sẽ ko hiển trên topdev.vn. Chỉ để trống thông tin này khi khách hàng không đồng ý cung cấp thông tin lương')
                        ->setGroupClass('salary-estimate-input');
                    $form
                        ->number('min_estimate', 'Min Estimate')
                        ->default(0)
                        ->setGroupClass('salary-estimate-input');

                    $form
                        ->number('max_estimate', 'Max Estimate')
                        ->default(0)
                        ->setGroupClass('salary-estimate-input');

                    $form->select('currency_estimate', 'Currency')
                        ->options(config('constant.CURRENCY'))
                        ->default('VND')
                        ->required()
                        ->setGroupClass('salary-estimate-input');
                })
                ->showOriginal()
                ->showPrevious()
                ->auditColumn('salary');

            $form->table('job_translation_working_hours', 'Working hours', function ($table) {
                $table->text('time', __('Time'));
            });
        })
        /**
        * Quan ly cac dich vu job.
        *
        */
        ->tab('Features', function ($form) use ($taxonomies, $id) {
            $invoices = [];
            $packages = empty($taxonomies->get('packages')) ? collect() : $taxonomies->get('packages')->map(fn($package) => ['id' => $package->id, 'text' => $package->term->name]);
            $foundPackages = $packages;
            if ($id) {
                $job = Job::whereId($id)->select('owned_id', 'crm_invoice_id', 'id')->first();
                if ($job->owned_id) {
                    $amsPackageId = Arr::last($job->packages)['name'] ?? null;
                    $invoices = CrmApi::getAvailableInvoices($job->owned_id, $job->crm_invoice_id, $amsPackageId);
                    if ($job->crm_invoice_id) {
                        $packageIds = CrmApi::getPackageByInvoice($job->crm_invoice_id, $amsPackageId);
                        $foundPackages = $packageIds ? $packages->filter(fn($item) => in_array($item['id'], $packageIds))->values() : collect();
                    }
                }
            }

            $form->select('crm_invoice_id', 'Invoice')
                ->options($invoices);

            $form->table('packages', 'Packages', function ($table) use ($foundPackages) {
                $table->select('name', 'Package')
                    ->options($foundPackages->pluck('text', 'id'))
                    ->rules('required');
                $table->text('expires_in', 'Expires in (days)')->help('0 là hết hạn, Null là vô  cực :))');
            });

            $form->table('services', 'Services', function ($table) use ($taxonomies) {
                $table->select('name', 'Service')
                    ->options(
                        empty($taxonomies->get('services')) ? [] : $taxonomies->get('services')->pluck('term.name', 'id')
                    )->rules('required');
                $table->text('expires_in', 'Expires in (days)')->help('0 là hết hạn, Null là vô  cực :))');
            });

            $jsonPackages = json_encode($packages);

            $script = <<<SCRIPT

            (function ($) {
                var jobPackages = $jsonPackages,
                    filterPackages = structuredClone(jobPackages);

                function populatePackageDropdown(packageData) {
                    const selects = $('#has-many-packages select.packages').last();
                    if (selects.length) {
                        // Tmp disable to avoid trigger between custom listener
                        $(document).off('change', '#has-many-packages select.packages', onPackageSelectionChange);

                        selects.each(function (index, select) {
                            var selectedValue = $(select).val();
                            $(select).find("option").remove();
                            $(select).select2({
                                placeholder: {"id":"","text":"Package"},
                                allowClear: 1,
                                data: packageData
                            })
                            .val(selectedValue)
                            .trigger('change');
                        });

                        $(document).on('change', '#has-many-packages select.packages', onPackageSelectionChange);
                    }
                }

                function populateInvoiceDropdown(data) {
                    var invoices = data.data;
                    var invoiceSelect = $('select.crm_invoice_id');
                    if (invoiceSelect.length) {
                        invoiceSelect.off('change', onInvoiceSelectionChange);

                        var selectedValue = $(invoiceSelect).val();
                        $(invoiceSelect).find("option").remove();
                        $(invoiceSelect).select2({
                            placeholder: {"id":"","text":"Invocie"},
                            allowClear: 1,
                            data: Object.entries(invoices).map(([id, text]) => {return {id, text}})
                        })
                        .val(selectedValue)
                        .trigger('change');

                        invoiceSelect.on('change', onInvoiceSelectionChange);
                    }
                }

                $('#has-many-packages div.add').click(function () {
                    setTimeout(function () {
                        populatePackageDropdown(filterPackages);
                    }, 100);
                });

                function onInvoiceSelectionChange() {
                    var invoiceId = $(this).val();
                    if (invoiceId) {
                        $.get('/admin/ajax/jobs/available-packages-by-invoice/' + invoiceId).then(function(data) {
                            var packageIds = data.data;
                            filterPackages = structuredClone(jobPackages).filter(function (package) {
                                return packageIds.includes(package.id);
                            });
                            populatePackageDropdown(filterPackages);
                        });
                    } else {
                        filterPackages = structuredClone(jobPackages);
                        populatePackageDropdown(filterPackages);
                    }

                }

                $('select.crm_invoice_id').on('change', onInvoiceSelectionChange);

                function onPackageSelectionChange() {
                    var selectedPackage = $(this).hasClass('packages') ? $(this).val() : null,
                        companyId = $('select.owned_id').val();

                    if (companyId && selectedPackage) {
                        $.get('/admin/ajax/jobs/available-invoices/' + companyId + '/0/' + selectedPackage).then(populateInvoiceDropdown);
                    } else if (companyId) {
                        $.get('/admin/ajax/jobs/available-invoices/' + companyId).then(populateInvoiceDropdown);
                    } else {
                        populateInvoiceDropdown({data: []});
                    }
                }

                $(document).on('change', '#has-many-packages select.packages', onPackageSelectionChange);

                $('select.owned_id').on('change', onPackageSelectionChange);
            })(jQuery);

            SCRIPT;

            Admin::script($script);
        })
        /**
        * Notes
        *
        */
        ->tab('Notes', function (Form $form) use ($taxonomies) {
            $form->multipleSelect('job_note_notes_label', 'Notes label')->options(
                $taxonomies->get('services')
                ->merge($taxonomies->get('packages'))
                ->pluck('term.name', 'term.name')
            );

            $form->textarea('job_note_employer_notes', 'Employer notes');
        })
        /**
         * SEO tab
         */
        ->tab('SEO', function ($form) {
            $form->text('job_meta.meta_title', __('Meta title'));
            $form->text('job_meta.meta_keywords', __('Meta keywords'));
            $form->textarea('job_meta.meta_description', __('Meta description'));
        })
        /**
         * Blog posts settings
         */
        ->tab('Blog', function ($form) {
            $form->text('blog_tags');
            $form->divider();
            $form->sortableTable('blog_posts', 'Posts', function ($table) {
                $table->text('title');
                $table->text('author');
                $table->text('time');
                $table->url('url');
            });
            $form->multipleImage('sidebar_image_banner_url')->removable();
            $form->url('sidebar_image_link');
        });

        $form->tools(function (Tools $tools) {
            $tools->disableView();
            $tools->disableDelete();
        });

        $form->beforeModified(function(Form $form) {
            $form->model()->setOldValuesAudit(
                JobAuditTransformer::make($form->model())->toArray(request())
            );
        });

        /**
         * Form Submitted event
         */
        $form->submitted(function (Form $form) {
            $rules = [
                'job_note_recruiment_process' => 'array|max_item:8',
                'content_html_desktop_file' => 'mimes:html,htm',
                'content_html_mobile_file' => 'mimes:html,htm',
            ];

            $messages = [
                'job_note_recruiment_process.max_item' => 'The recruitment process has no more than 8 steps',
                'content_html_desktop_file.mimes' => 'The content_html_desktop file must be a file of type: html, htm',
                'content_html_mobile_file.mimes' => 'The content_html_mobile file must be a file of type: html, htm',
            ];

            $rules = array_merge($rules, [
                'new_status' => [new FreeJobStatusReviewRule()]
            ]);


            // read content html files
            if (request()->hasFile('content_html_desktop_file')) {
                $form->content_html_desktop = $this->jobContentHtmlFormat(
                    request()->file('content_html_desktop_file')->getContent()
                );
            }

            if (request()->hasFile('content_html_mobile_file')) {
                $form->content_html_mobile = $this->jobContentHtmlFormat(
                    request()->file('content_html_mobile_file')->getContent()
                );
            }

            // Custom validator for ignore fields
            $validator = Validator::make(request()->all(), $rules, $messages);

            $validator->validate();

            /**
             * Set ignore fields for forms
             * These fields will not be processed
             */
            $form->ignore([
                'job_information_benefits',
                'job_information_languages',
                'job_information_salary',
                'job_information_other_supports',
                'job_note_notes_label',
                'job_note_recruiment_process',
                'job_note_employer_notes',
                'job_translation_working_hours',
                'experience_from_id',
                'experience_to_id',
                'job_information_benefit',
                'content_html_desktop_file',
                'content_html_mobile_file',
                'responsibilities',
                'requirements',
                'recruitment_processes',
                'banner_id',
                'template_id',
                'template_color_id',
                'benefits',
            ]);
        });

        /**
         * Form Saving event
         */
        $form->saving(function (Form $form) {
            $form->model()->disableAuditing();
            $status = $form->new_status;
            switch ($status) {
                case Job::STATUS_OPEN_PLUS:
                    $form->new_status = Job::STATUS_OPEN;
                    is_null($form->opened_at) && $form->opened_at = now();
                    break;
                case Job::STATUS_OPEN:
                    $form->opened_at && $form->opened_at = null;
                    break;
                case Job::STATUS_CLOSED_PLUS:
                    $form->new_status = Job::STATUS_CLOSED;
                    is_null($form->closed_at) && $form->closed_at = now();
                    break;
                case Job::STATUS_CLOSED:
                    $form->closed_at && $form->closed_at = null;
                    break;
                default:
                    break;
            }
        });

        /**
         * Form Saved event
         */
        $form->saved(function (Form $form)  {
            if ($form->isStoring()) {
                if (($packages = request('packages')) && !empty($packages)) {
                    $packages = $this->cleanAmscore($packages);
                    $form->model()->setPackagesAttribute($packages);
                }

                if (($services = request('services')) && !empty($services)) {
                    $services = $this->cleanAmscore($services);
                    $form->model()->setServicesAttribute($services);
                }

                $form->model()->save();
            }

            $hasJobInformation = request()->hasAny([
                'job_information_benefit',
                'job_information_languages',
                'job_information_salary',
                'job_information_other_supports',
            ]);
            if ($hasJobInformation) {
                $salary = request('job_information_salary');
                if (Arr::get($salary, 'is_negotiable')) {
                    Arr::set($salary, 'min', 0);
                    Arr::set($salary, 'max', 0);
                } else {
                    Arr::set($salary, 'min_estimate', 0);
                    Arr::set($salary, 'max_estimate', 0);
                }

                $form->model()->job_information()->updateOrCreate([], [
                    'benefits' => request('job_information_benefit'),
                    'languages' => $this->cleanAmsCore(request('job_information_languages')),
                    'salary' => $this->cleanAmsCore($salary),
                    'other_supports' => $this->cleanAmsCore(request('job_information_other_supports')),
                ]);
            } else {
                if ($form->model()->job_information()->doesntExist()) {
                    $form->model()->job_information()->create();
                }
            }

            if (request()->hasAny(['job_note_recruiment_process', 'job_note_notes_label', 'job_note_employer_notes'])) {
                $form->model()->job_note()->updateOrCreate([], [
                    'recruiment_process' => $this->cleanAmsCore(request('job_note_recruiment_process')),
                    'notes_label' => $this->cleanAmscore(request('job_note_notes_label')),
                    'employer_notes' => $this->cleanAmscore(request('job_note_employer_notes')),
                ]);
            } else {
                if ($form->model()->job_note()->doesntExist()) {
                    $form->model()->job_note()->create();
                }
            }

            if (request()->has('job_category_role_ids')) {
                $categoryIds = [];

                if (request('job_category_id') && intval(request('job_category_id')) > 0) {
                    $categoryIds[] = intval(request('job_category_id'));
                }

                if (is_array(request('job_category_role_ids'))) {
                    $roleIds = array_filter(request('job_category_role_ids'), function($id) {
                        return intval($id) > 0;
                    });
                    $categoryIds = array_merge($categoryIds, $roleIds);
                }

                if (!empty($categoryIds)) {
                    $form->model()->job_categories()->sync($categoryIds);
                }
            }

            if (request()->has('job_translation_working_hours')) {
                $form->model()->job_translation()->updateOrCreate([], [
                    'working_hours' => $this->cleanAmsCore(request('job_translation_working_hours'))
                ]);
            }

            // Add other supports to taxonomies collections
            if (request()->has('job_information_other_supports')) {
                $form->model()->detachTaxonomiesCollection('other_supports');

                $otherSupports = $this->cleanAmsCore(request()->get('job_information_other_supports'));
                $form->model()->addTaxonomies(
                    collect($otherSupports)->pluck('term_id'),
                    'other_supports'
                );
            }

            // Add experiences from / to
            if (request()->has(['experience_from_id', 'experience_to_id'])) {
                $experiences = array_filter([
                    request()->get('experience_from_id'),
                    request()->get('experience_to_id')
                ]);

                $form->model()->addTaxonomies(
                    $experiences,
                    'experiences'
                );
            }

            // Responsibilities
            $sortOrder = 0;
            collect(request('responsibilities'))->each(function ($item) use ($form, &$sortOrder) {
                $form->model()->taxonomies()->detach($item['id']);

                if ($item['_remove_'] === "0") {
                    $form->model()->taxonomies()->attach($item['id'], [
                        'custom_properties' => [
                            'id' => $item['id'],
                            'description' => $item['description'],
                            'sort_order' => $sortOrder++,
                        ]
                    ]);
                }
            });

            // Requirements
            collect(request('requirements'))->each(function ($item) use ($form, &$sortOrder) {
                $form->model()->taxonomies()->detach($item['id']);

                if ($item['_remove_'] === "0") {
                    $form->model()->taxonomies()->attach($item['id'], [
                        'custom_properties' => [
                            'id' => $item['id'],
                            'description' => $item['description'],
                            'sort_order' => $sortOrder++,
                        ],
                    ]);
                }
            });

            // Recruitment processes
            collect(request('recruitment_processes'))->each(function ($item) use ($form, &$sortOrder) {
                $form->model()->taxonomies()->detach($item['id']);

                if ($item['_remove_'] === "0") {
                $form->model()->taxonomies()->attach($item['id'], [
                        'custom_properties' => [
                            'id' => $item['id'],
                            'description' => $item['description'],
                            'sort_order' => $sortOrder++,
                        ]
                ]);
                }
            });

            // Benefits
            collect(request('benefits'))->each(function ($item) use ($form, &$sortOrder) {
                $form->model()->taxonomies()->detach($item['id']);

                if ($item['_remove_'] === "0") {
                    $form->model()->taxonomies()->attach($item['id'], [
                        'custom_properties' => [
                            'id' => $item['id'],
                            'description' => $item['description'],
                            'sort_order' => $sortOrder++,
                        ]
                    ]);
                }
            });

            // Job template
            if (request()->has('banner_id')) {
                $model = $form->model();
                if ($model->banner_id) {
                    $model->taxonomies()->detach($model->banner_id);
                }
                $model->taxonomies()->attach(request('banner_id'));
            }

            // Job template
            if (request()->has('template_id')) {
                $model = $form->model();
                if ($model->template_id) {
                    $model->taxonomies()->detach($model->template_id);
                }
                $model->taxonomies()->attach(request('template_id'));
            }

            // Job template
            if (request()->has('template_color_id')) {
                $model = $form->model();
                if ($model->template_color_id) {
                    $model->taxonomies()->detach($model->template_color_id);
                }
                $model->taxonomies()->attach(request('template_color_id'));
            }

            /** @var Job $job */
            $job = $form->model();
            $job->refresh();

            // Generate content image
            if ($job->is_content_image && $job->level === Job::JOB_LEVEL_PAID) {
                app(\Modules\Job\Services\JobService::class)->generateJobDescriptionHTML($job);

                if ($job->content_html_desktop && $job->content_html_mobile) {
                    Log::info('JobController::update save job content image', [
                        'job' => $job->id,
                    ]);
                }
            }

            $oldValuesAudit = $job->getOldValuesAudit();

            $audit = new Audit();
            $audit->user_type = get_class(auth()->user());
            $audit->user_id = auth()->id();

            if (request()->isMethod('POST')) {
                $audit->event = 'created';
                $audit->old_values = [];
            } else {
                $audit->event = 'updated';
                $audit->old_values = $oldValuesAudit;
            }
            $audit->new_values = JobAuditTransformer::make($job)->toArray(request());
            $audit->auditable_type = Job::class;
            $audit->auditable_id = $job->id;
            $audit->url = request()->url();
            $audit->ip_address = request()->ip();
            $audit->user_agent = request()->userAgent();
            $audit->save();

            // send email notify activate warranty
            $statusJob =  $oldValuesAudit['status_display'] ?? null;
            $oldExpires = $oldValuesAudit['expires_at'] ?? null;
            $newExpires = $form->input('expires_at');
            if (Carbon::parse($newExpires)->isAfter($oldExpires) && $statusJob === 'Open') {
                // @phpstan-ignore-next-line
                Notification::send($job->company,new NotifyActivateWarranty($job, $oldExpires, $newExpires));
            }

            // Handle job location best match
            $arrLocationBestMatch = collect(request()->get('location_best_match'))
            ->filter(function ($value) {
                return $value;
            })
            ->toArray();
            $job->jobLocationBestMatch()->where('job_id', $job->id)->whereNotIn('province_id', $arrLocationBestMatch)->delete();
            foreach ($arrLocationBestMatch as $id) {
                JobLocationBestMatch::updateOrCreate(
                    [
                        'province_id' => $id,
                        'job_id' => $job->id,
                    ],
                    [
                        'province_id' => $id,
                        'job_id' => $job->id,
                    ]
                );
            }
        });

        /**
         * Set eager load for form model
         */
        $form
            ->model()
            ->with(
                'audits',
                'job_translation',
                'job_note',
                'job_meta',
                'job_information',
                'media',
                'company.addresses',
                'taxonomies.term.translations',
                'features.term',
                'subscriptions.term',
                'candidates',
                'jobLocationBestMatch'
            );

        /**
         * Append these attribute to the form's value
         */
        $form->append([
            'hackerrank',
            'services',
            'packages',
            'extra_skills_name',
            'required_skills_name',
            'optional_skills_name',
            'location_type',
            'location_best_match',
            'skills_name',
            'job_types_id',
            'job_levels_id',
            'contract_types_id',
            'categories_id',
            'image_galleries_url',
            'image_thumbnail_url',
            'addresses_id',
            'image_banner_url',
            'meta_title',
            'meta_keywords',
            'meta_description',
            'job_information_benefits',
            'job_information_languages',
            'job_information_salary',
            'job_information_other_supports',
            'job_note_notes_label',
            'job_note_employer_notes',
            'job_translation_working_hours',
            'sidebar_image_banner_url',
            'experience_from_id',
            'experience_to_id',
            'job_information_benefit',
            'new_status',
            'role_id',
            'recruitment_processes',
            'template_id',
            'template_color_id',
            'banner_id',
            'benefits',

            'responsibilities_original',
            'requirements_original',
            'is_content_image',

            'job_category_id',
            'job_category_role_ids',
        ]);

        return $form;
    }

    public function jobs(Request $request)
    {
        $q = $request->get('q');

        return Job::select('id', 'status', 'slug', DB::raw('CONCAT( title, " (#", id, ")" ) AS text'))->where('title', 'like', "%$q%")->paginate();
    }

    /**
    * Update the specified resource in storage.
    *
    * @param int $id
    *
    * @return \Illuminate\Http\Response
    */
    public function update($id)
    {
        if (request()->get('_autosave', false)) {
            return Job::withoutSyncingToSearch(function () use ($id) {
                return $this->form()->update($id);
            });
        }

        return $this->form()->update($id);
    }

    /**
    * Store a newly created resource in storage.
    *
    * @return mixed
    */
    public function store()
    {
        return $this->form()->store();
    }

    /**
    * Approval the specified resource from storage.
    *
    * @param int $id
    *
    * @return \Illuminate\Http\Response
    */
    public function approval($id)
    {
        $job = Job::withoutGlobalScopes()->find($id)->approval();
        return 'success';
    }

    /**
    * Remove the specified resource from storage.
    *
    * @param int $id
    *
    * @return \Illuminate\Http\Response
    */
    public function destroy($id)
    {
        return $this->form()->destroy($id);
    }

    /**
    * Clean value before create.
    *
    * @return \mixed
    */
    private function cleanAmscore($values)
    {
        if (is_string($values)) {
            return trim($values);
        }

        if (is_array($values)) {
            return collect($values)->reject(function ($value, $key) {
                if (is_array($value) && array_key_exists(NestedForm::REMOVE_FLAG_NAME, $value)) {
                    return $value[NestedForm::REMOVE_FLAG_NAME] == 1;
                }

                return false;
            })->map(function ($value) {
                if (is_array($value) && array_key_exists(NestedForm::REMOVE_FLAG_NAME, $value)) {
                    unset($value[NestedForm::REMOVE_FLAG_NAME]);
                }

                return $value;
            })->toArray();
        }

        return $values;
    }

    public function personCharge(Request $request)
    {
        $keyword = $request->get('q');
        return Administrator::select('id', DB::raw('CONCAT(IFNULL(username,""), IFNULL(CONCAT(" | ", email),""), IFNULL(CONCAT(" | ", name),"")) as text'))->where('name', 'like', "%$keyword%")->paginate(null, ['id', 'text']);
    }

    public function getMissingInvoices(Request $request, BaseHttpResponse $response)
    {
        return $response->setData(['missing_invoices_count' => Job::whereNull('crm_invoice_id')->where('status', Job::STATUS_OPEN)->count()]);
    }

    public function jobContentHtmlFormat($content)
    {
        $content = preg_replace('/<link.*?>/', '', $content);

        $content = preg_replace('/<!--(.|\s)*?-->/', '', $content);
        return preg_replace('/\/\*(.|\s)*?\*\//', '', $content);
    }

    public function previewTemplate(JobService $jobService, Job $job)
    {
        return $jobService->renderTemplate($job);
    }
}
