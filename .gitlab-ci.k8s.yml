variables:
  SECRET_DETECTION_DISABLED: 1
  CODE_QUALITY_DISABLED: 1
  TEST_DISABLED: 1
  BROWSER_PERFORMANCE_DISABLED: 1

include:
  - template: Auto-DevOps.gitlab-ci.yml  # https://gitlab.com/gitlab-org/gitlab/blob/master/lib/gitlab/ci/templates/Auto-DevOps.gitlab-ci.yml

.global_cache: &global_cache
  paths:
    - node_modules/
    - public/
    - vendor/
    - .phpstan/
    - .phpunit.result.cache
    - .php-cs-fixer.cache
  policy: pull-push

.auto-deploy:
  variables:
    DB_MIGRATE: /cnb/lifecycle/launcher php artisan migrate
    HELM_UPGRADE_EXTRA_ARGS: --values .gitlab/${CI_ENVIRONMENT_SLUG}-secret.volume.yaml

  before_script:
    - |
      [[ -n "${VOLUME_NFS_PATH}" ]] && SED_SCRIPT=";s#path: /srv/data/resources#path: ${VOLUME_NFS_PATH:-/srv/data/resources}#"
      [[ -n "${VOLUME_NFS_SERVER}" ]] && SED_SCRIPT="$SED_SCRIPT;s#server: ************#server: ${VOLUME_NFS_SERVER:-************}#"
      sed "s/secretName: env\.secret/secretName: ${CI_ENVIRONMENT_SLUG}-secret/g ${SED_SCRIPT:-}" .gitlab/dotenv-secret-volume.yaml > .gitlab/${CI_ENVIRONMENT_SLUG}-secret.volume.yaml
    - |
      echo "Please create Secrets using kubectl"
      echo "kubectl create secret generic gg-credentials \\
        --from-file=TopDev-20b72d2b7c42.json=storage/gg_credentials/TopDev-20b72d2b7c42.json \\
        --from-file=topdev-webapp-bb8b3295f757.json=storage/gg_credentials/topdev-webapp-bb8b3295f757.json \\
        --from-file=client_id.json=storage/gg_credentials/client_id.json \\
        --from-file=token.json=storage/gg_credentials/token.json \\
        -n $KUBE_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -"
      echo ""
      echo "kubectl create secret generic my-gg-credentials \\
        --from-file=credentials.json=storage/my_gg_credentials/credentials.json \\
        --from-file=token.json=storage/my_gg_credentials/token.json \\
        -n $KUBE_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -"

stop_review:
  variables:
    HELM_UPGRADE_EXTRA_ARGS: ""
    GIT_STRATEGY: none
  before_script: []

build:
  # Use the Docker executor with Docker socket binding
  tags: ["102-stg-docker"]
  services: []
  variables:
    AUTO_DEVOPS_BUILD_IMAGE_CNB_BUILDER: heroku/builder:22
    #BUILDPACK_URL: heroku/php,heroku/procfile
  cache:
    # inherit all global cache settings
    <<: *global_cache
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "develop"'

review:
  variables:
    WEB_CONCURRENCY: 7
  rules:
    - if: '$CI_COMMIT_BRANCH == "review" || $CI_COMMIT_BRANCH == "develop"'

# Build testing on stg server
build tesing:
  stage: build
  tags:
    - 102-stg
  only:
    - develop
  variables:
    GIT_STRATEGY: none
  script:
    - cd /var/www/ams
    - git pull origin develop
    - /usr/bin/php8.2 /usr/bin/composer install
    - /usr/bin/php8.2 artisan horizon:terminate
    - /usr/bin/php8.2 /opt/cachetool/cachetool.phar opcache:reset --fcgi=/run/php/php8.2-fpm.sock
  environment:
    name: testing
    url: https://amsdev.topdev.asia

