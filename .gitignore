/node_modules
/public/hot
# public/js/chunks/
# public/css/
/public/storage
/public/resources
/public/facebook-job-feed
/public/docs
/public/vendor/laravel-admin/minify-manifest.json
/storage/*.key
/storage/medialibrary/
/storage/activity_module/
/storage/gg_credentials/token.json
/storage/facebook-job-feed
/storage/google-job-feed
/vendor
.env
.env.*
!.env.example
adsapi_php.ini
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/app/Admin
*/.DS_Store
env/.env.sonnx
env/.env.local
storage/*.json
.vscode
.idea
http-client.private.env.json
storage/gg_credentials/token.json
storage/gg_credentials/client_id.json
storage/my_gg_credentials
storage/my_gg_credentials/token.json
storage/my_gg_credentials/credentials.json
.phpstan
.php-cs-fixer.cache

# All json in gg_credentials except TopDev.json
storage/gg_credentials/*.json
storage/gg_credentials/TopDev-20b72d2b7c42.json
/.config/psysh/
/_ide_helper.php
tools/vendor
.phpunit.cache/test-results

# gitlab auto devops
/chart/
/gl-auto-build-variables.env
/.gitlab/*-secret.volume.yaml
/gitlab-exporter/
/sitespeed-results/
