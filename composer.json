{"name": "topdev/ams", "type": "project", "description": "Applancer MS base Laravel.", "version": "1.0.0", "authors": [{"name": "sonnx", "email": "<EMAIL>"}], "keywords": ["topdev", "ams"], "license": "proprietary", "require": {"php": "~8.2.0", "ext-ctype": "*", "ext-json": "*", "ext-mbstring": "*", "ext-pdo": "*", "ext-redis": "*", "arcanedev/laravel-notes": "^10.0", "astrotomic/laravel-translatable": "^11.9", "chelout/laravel-relationship-events": "^2.0", "cyrildewit/eloquent-viewable": "^7.0", "doctrine/dbal": "^3.0", "facebook/php-business-sdk": "^19.0", "fakerphp/faker": "^1.23", "genealabs/laravel-model-caching": "^11.0", "google/apiclient": "^2.7", "googleads/googleads-php-lib": "^64.0", "guzzlehttp/guzzle": "^7.0.1", "guzzlehttp/psr7": "^2.6", "irazasyed/telegram-bot-sdk": "^3.13", "itsgoingd/clockwork": "^5.1", "jackpopp/geodistance": "dev-master", "josiasmontag/laravel-recaptchav3": "^1.0", "kalnoy/nestedset": "^6.0", "laravel-notification-channels/fcm": "^4.0", "laravel/framework": "^10.0", "laravel/horizon": "^5.29", "laravel/legacy-factories": "^1.4", "laravel/scout": "^10.8", "laravel/tinker": "^2.9", "league/flysystem": "^3.0", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1", "matchish/laravel-scout-elasticsearch": "^7.0", "matthiasmullie/minify": "^1.3", "mautic/api-library": "^3.0", "nwidart/laravel-modules": "^10.0", "overtrue/laravel-follow": "^1.1", "owen-it/laravel-auditing": "^13.0", "php-http/message": "^1.16", "predis/predis": "^2.0", "protonemedia/laravel-analytics-event-tracking": "^1.6", "psr/http-factory": "^1.0", "redmix0901/core": "dev-master", "redmix0901/elastic-resource": "^1.0", "seld/jsonlint": "^1.4", "sentry/sentry-laravel": "^4.2", "spatie/laravel-medialibrary": "^10.0", "spatie/laravel-model-states": "^2.7", "symfony/cache": "^6.0", "symfony/dom-crawler": "~3.1|~4.0", "thomasjohnkane/snooze": "^2.1", "unleash/client": "^2.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "barryvdh/laravel-ide-helper": "^3.0", "driftingly/rector-laravel": "^1.0", "filp/whoops": "^2.0", "larastan/larastan": "^2.0", "laravel/pint": "^1.17", "mockery/mockery": "^1.0", "nunomaduro/collision": "^7.0", "pestphp/pest": "^2.34", "rector/rector": "^1.0", "spatie/laravel-ignition": "^2.0", "symfony/css-selector": "~3.1"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true, "pestphp/pest-plugin": true}}, "extra": {"laravel": {"dont-discover": [], "providers": []}}, "autoload": {"psr-4": {"App\\": "app/", "Modules\\": "Mo<PERSON>les/", "Amscore\\Admin\\": "amscore/src/", "Amscore\\Admin\\Helpers\\": "amscore/src/Helpers/src/", "Amscore\\Admin\\MediaManager\\": "amscore/src/MediaManager/src/", "Amscore\\Admin\\LockScreen\\": "amscore/src/LockScreen/src/", "Amscore\\Admin\\Nocaptcha\\": "amscore/src/Nocaptcha/src/", "Amscore\\Tinymce\\": "amscore/src/Tinymce/src/", "Amscore\\Metable\\": "amscore/src/Metable/src/", "Amscore\\AuthenticationLog\\": "amscore/src/AuthenticationLog/src/", "Amscore\\Chartjs\\": "amscore/src/Chartjs/src/", "Jxlwqq\\JsonEditor\\": "amscore/src/JsonEditor/src/"}, "files": ["amscore/src/helpers.php", "app/constants.php", "app/helpers.php"], "classmap": ["database/seeds"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "sniff": ["./tools/vendor/bin/php-cs-fixer fix -vvv --dry-run --show-progress=dots"], "fix": ["./tools/vendor/bin/php-cs-fixer fix -vvv --show-progress=dots"], "analyse": ["./vendor/bin/phpstan --memory-limit=512M analyse"], "test": ["./vendor/bin/phpunit"]}}